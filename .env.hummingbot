# Hummingbot Integration Environment Variables
# Copy this file to .env and update with your actual values

# Hummingbot Gateway Configuration
HUMMINGBOT_GATEWAY_HOST=localhost
HUMMINGBOT_GATEWAY_PORT=15888
HUMMINGBOT_GATEWAY_PROTOCOL=https
HUMMINGBOT_GATEWAY_PASSPHRASE=your_secure_passphrase_here
HUMMINGBOT_GATEWAY_CERT_PATH=./certs/gateway.crt
HUMMINGBOT_GATEWAY_KEY_PATH=./certs/gateway.key

# Hummingbot Client Configuration
HUMMINGBOT_CLIENT_HOST=localhost
HUMMINGBOT_CLIENT_PORT=8080
HUMMINGBOT_CONFIG_PASSWORD=your_config_password_here

# Exchange API Credentials (Update with your actual credentials)
# Binance
BINANCE_API_KEY=your_binance_api_key
BINANCE_SECRET_KEY=your_binance_secret_key
BINANCE_TESTNET=true

# Coinbase Pro
COINBASE_PRO_API_KEY=your_coinbase_pro_api_key
COINBASE_PRO_SECRET_KEY=your_coinbase_pro_secret_key
COINBASE_PRO_PASSPHRASE=your_coinbase_pro_passphrase
COINBASE_PRO_SANDBOX=true

# Kraken
KRAKEN_API_KEY=your_kraken_api_key
KRAKEN_SECRET_KEY=your_kraken_secret_key

# DEX Configuration (for Uniswap, PancakeSwap, etc.)
ETHEREUM_RPC_URL=https://mainnet.infura.io/v3/your_infura_project_id
ETHEREUM_CHAIN_ID=1
ETHEREUM_WALLET_PRIVATE_KEY=your_ethereum_wallet_private_key

BSC_RPC_URL=https://bsc-dataseed.binance.org/
BSC_CHAIN_ID=56
BSC_WALLET_PRIVATE_KEY=your_bsc_wallet_private_key

# Trading Configuration
DEFAULT_TRADING_PAIR=BTC-USDT
DEFAULT_EXCHANGE=binance
MAX_ORDER_SIZE=1000
MIN_ORDER_SIZE=10
DEFAULT_SLIPPAGE=0.01

# Risk Management
MAX_POSITION_SIZE=50000
MAX_DAILY_LOSS=1000
STOP_LOSS_PERCENTAGE=0.05
TAKE_PROFIT_PERCENTAGE=0.10

# Logging
LOG_LEVEL=INFO
ENABLE_HUMMINGBOT_LOGS=true
HUMMINGBOT_LOG_PATH=./logs/hummingbot

# Development/Testing
ENABLE_PAPER_TRADING=true
TEST_MODE=true
SIMULATION_MODE=false