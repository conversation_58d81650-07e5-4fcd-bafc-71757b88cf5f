FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first for better caching
COPY requirements.txt .

# Install Python dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create necessary directories
RUN mkdir -p /app/logs /app/data /app/models /app/config

# Set environment variables
ENV PYTHONPATH=/app
ENV PYTHONUNBUFFERED=1

# Expose ports
EXPOSE 5000 8080

# Create startup script
RUN echo '#!/bin/bash\n\
set -e\n\
echo "Starting AI Trading System..."\n\
echo "Waiting for dependencies..."\n\
sleep 10\n\
echo "Starting Flask API..."\n\
python -m src.api.main &\n\
echo "Starting Dashboard..."\n\
python -m src.dashboard.app &\n\
echo "Starting Main Trading System..."\n\
python -m src.main\n' > /app/start.sh

RUN chmod +x /app/start.sh

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:5000/health || exit 1

# Start the application
CMD ["/app/start.sh"]