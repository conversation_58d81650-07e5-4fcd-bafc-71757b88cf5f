# 🤖 AI Trading Bot - Ku<PERSON><PERSON>m Talimatları

## 📋 İçindekiler
1. [<PERSON><PERSON> Na<PERSON><PERSON>l <PERSON>alışır?](#bot-nasıl-çalışır)
2. [Başlatma ve Durdurma](#başlatma-ve-durdurma)
3. [Web Arayüzü](#web-arayüzü)
4. [API Kullanımı](#api-kullanımı)
5. [Konfigürasyon](#konfigürasyon)
6. [Monitoring ve Loglar](#monitoring-ve-loglar)
7. [Güvenlik](#güvenlik)

---

## 🚀 Bot Nasıl Çalışır?

### Temel Çalışma Prensibi
```
1. Market Verisi Toplama → 2. AI Analizi → 3. <PERSON><PERSON> → 4. Risk Kontrolü → 5. Emir Verme
```

### <PERSON> Bileşenler
- **📊 Data Collector**: Market verilerini toplar (fiyat, volume, news)
- **🧠 AI Engine**: Machine learning ile analiz yapar
- **⚡ Decision Manager**: Trading kararları verir
- **⚠️ Risk Manager**: <PERSON> kontrolü yapar
- **💰 Trading Controller**: Emirleri yönetir

---

## 🎮 Başlatma ve Durdurma

### 1. Temel Başlatma
```bash
# Ana bot'u başlat
python main.py

# Veya arka planda çalıştır
nohup python main.py > bot.log 2>&1 &
```

### 2. Web Dashboard ile Başlatma
```bash
# API sunucusunu başlat
python src/api/main.py

# Dashboard'u başlat (yeni terminal)
streamlit run src/dashboard/app.py --server.port 8501
```

### 3. Docker ile Başlatma
```bash
# Tüm servisleri başlat
docker-compose up -d

# Sadece trading bot'u başlat
docker-compose up ai-trading-system
```

### 4. Güvenli Durdurma
```bash
# Ctrl+C ile durdur (graceful shutdown)
# Veya process ID ile
kill -TERM <PID>
```

---

## 🌐 Web Arayüzü

### Dashboard Erişimi
- **URL**: http://localhost:8501
- **API**: http://localhost:5000

### Ana Sayfalar

#### 📊 Overview (Genel Bakış)
- Portfolio değeri ve performans
- Günlük P&L
- Asset allocation grafiği
- Sistem durumu

#### 💼 Portfolio (Portföy)
- Mevcut pozisyonlar
- Bakiye bilgileri
- Açık emirler
- Geçmiş işlemler

#### 📈 Trades (İşlemler)
- Gerçekleştirilen işlemler
- Kar/zarar analizi
- İşlem geçmişi
- Performance metrikleri

#### 🎯 Strategies (Stratejiler)
- Aktif stratejiler
- Strateji performansları
- Parametre ayarları
- Backtest sonuçları

#### ⚙️ Settings (Ayarlar)
- API key yönetimi
- Risk parametreleri
- Trading ayarları
- Sistem konfigürasyonu

---

## 🔌 API Kullanımı

### Temel Endpoints

#### Sistem Durumu
```bash
curl http://localhost:5000/api/v1/status
```

#### Portfolio Bilgisi
```bash
curl http://localhost:5000/api/v1/portfolio
```

#### İşlem Geçmişi
```bash
curl http://localhost:5000/api/v1/trades
```

#### Manuel Emir Verme
```bash
curl -X POST http://localhost:5000/api/v1/orders \
  -H "Content-Type: application/json" \
  -d '{
    "symbol": "BTC/USDT",
    "side": "buy",
    "amount": 0.001,
    "type": "market"
  }'
```

#### Bot Kontrolü
```bash
# Bot'u durdur
curl -X POST http://localhost:5000/api/v1/bot/stop

# Bot'u başlat
curl -X POST http://localhost:5000/api/v1/bot/start

# Bot durumunu kontrol et
curl http://localhost:5000/api/v1/bot/status
```

---

## ⚙️ Konfigürasyon

### 1. Temel Ayarlar (config/base.yaml)
```yaml
trading:
  default_exchange: "kucoin"
  max_positions: 10
  max_position_size: 1000.0
  enable_paper_trading: true

risk:
  max_portfolio_risk: 0.02  # %2
  stop_loss_percentage: 0.02  # %2
  take_profit_percentage: 0.04  # %4

ai:
  confidence_threshold: 0.7
  model_type: "ensemble"
```

### 2. Exchange Ayarları (config/exchanges.yaml)
```yaml
kucoin:
  enabled: true
  api_key: "your_api_key"
  api_secret: "your_secret"
  passphrase: "your_passphrase"
```

### 3. Environment Variables (.env)
```bash
KUCOIN_API_KEY="your_api_key"
KUCOIN_API_SECRET="your_secret"
KUCOIN_PASSPHRASE="your_passphrase"
```

---

## 📊 Monitoring ve Loglar

### Log Dosyaları
```
logs/
├── trading.log          # Ana trading logları
├── error.log           # Hata logları
├── api.log             # API logları
└── performance.log     # Performance metrikleri
```

### Log Seviyeleri
- **DEBUG**: Detaylı debug bilgileri
- **INFO**: Genel bilgi mesajları
- **WARNING**: Uyarı mesajları
- **ERROR**: Hata mesajları
- **CRITICAL**: Kritik hatalar

### Real-time Monitoring
```bash
# Canlı log takibi
tail -f logs/trading.log

# Hata loglarını takip et
tail -f logs/error.log

# Sistem kaynaklarını izle
htop
```

### Performance Metrikleri
- **Sharpe Ratio**: Risk-adjusted return
- **Max Drawdown**: Maksimum kayıp
- **Win Rate**: Kazanma oranı
- **Profit Factor**: Kar faktörü

---

## 🔒 Güvenlik

### API Key Güvenliği
1. **Testnet kullanın**: Geliştirme için testnet API'leri
2. **IP kısıtlaması**: API key'leri IP ile sınırlayın
3. **Minimum yetkiler**: Sadece gerekli yetkileri verin
4. **Düzenli rotasyon**: API key'leri düzenli değiştirin

### Risk Yönetimi
1. **Position limits**: Maksimum pozisyon büyüklüğü
2. **Stop-loss**: Otomatik zarar durdurma
3. **Daily limits**: Günlük işlem limitleri
4. **Correlation limits**: Korelasyon kontrolü

### Sistem Güvenliği
1. **Firewall**: Sadece gerekli portları açın
2. **SSL/TLS**: HTTPS kullanın
3. **Authentication**: API erişimi için auth
4. **Backup**: Düzenli yedekleme

---

## 🚨 Acil Durum Prosedürleri

### Bot'u Hızla Durdurma
```bash
# Graceful shutdown
pkill -TERM -f "python main.py"

# Force kill (son çare)
pkill -KILL -f "python main.py"
```

### Tüm Pozisyonları Kapatma
```bash
# API ile tüm pozisyonları kapat
curl -X POST http://localhost:5000/api/v1/positions/close-all
```

### Acil Durum Kontakları
- **Sistem Admin**: [<EMAIL>]
- **Risk Manager**: [<EMAIL>]
- **Technical Support**: [<EMAIL>]

---

## 📞 Destek ve Sorun Giderme

### Sık Karşılaşılan Sorunlar

#### 1. API Bağlantı Hatası
```bash
# API key'leri kontrol et
python test_kucoin.py

# Network bağlantısını test et
ping api.kucoin.com
```

#### 2. Insufficient Balance
- Hesap bakiyesini kontrol edin
- Minimum order size'ı kontrol edin
- Trading fees'i hesaba katın

#### 3. Rate Limit Exceeded
- API rate limit'lerini kontrol edin
- Request frequency'yi azaltın
- Multiple API key kullanın

### Log Analizi
```bash
# Son 100 satır
tail -n 100 logs/trading.log

# Hata mesajlarını filtrele
grep "ERROR" logs/trading.log

# Belirli bir tarih aralığı
grep "2024-01-01" logs/trading.log
```

---

## 🎯 İleri Seviye Kullanım

### Custom Strategy Ekleme
```python
# src/strategies/my_strategy.py
class MyCustomStrategy(BaseStrategy):
    def analyze(self, data):
        # Your custom logic here
        return signal
```

### Webhook Entegrasyonu
```python
# TradingView webhook'u için
@app.route('/webhook/tradingview', methods=['POST'])
def tradingview_webhook():
    signal = request.json
    # Process signal
    return jsonify({'status': 'received'})
```

### Backtesting
```bash
# Historical backtest
python scripts/backtest.py --start-date 2024-01-01 --end-date 2024-12-31
```

---

## 📈 Performans Optimizasyonu

### Sistem Gereksinimleri
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Disk**: 50GB+ SSD
- **Network**: Stable internet connection

### Optimizasyon İpuçları
1. **Database indexing**: Veritabanı indeksleri
2. **Caching**: Redis cache kullanımı
3. **Async processing**: Asenkron işlemler
4. **Load balancing**: Yük dağılımı

---

## 🔄 Güncelleme ve Bakım

### Sistem Güncellemesi
```bash
# Git pull
git pull origin main

# Dependencies güncelle
pip install -r requirements.txt --upgrade

# Database migration
python scripts/migrate.py
```

### Düzenli Bakım
- **Günlük**: Log rotation, disk space kontrolü
- **Haftalık**: Performance analizi, backup kontrolü
- **Aylık**: API key rotation, security audit

---

## 📚 Ek Kaynaklar

- **Documentation**: [docs/](docs/)
- **API Reference**: [api-docs.md](api-docs.md)
- **Strategy Guide**: [strategy-guide.md](strategy-guide.md)
- **Risk Management**: [risk-guide.md](risk-guide.md)

---

**⚠️ Önemli Uyarı**: Bu bot gerçek para ile trading yapar. Lütfen risk yönetimi kurallarına uyun ve sadece kaybetmeyi göze alabileceğiniz miktarlarla trading yapın.