# Base Configuration for AI Trading Agent
# Author: inkbytefo

general:
  app_name: "AI Trading Agent"
  version: "1.0.0"
  environment: "development"
  debug: true
  log_level: "INFO"
  timezone: "UTC"
  config_dir: "config"
  data_dir: "data"
  logs_dir: "logs"
  temp_dir: "temp"
  max_workers: 4
  enable_metrics: true
  enable_monitoring: true
  enable_alerts: true
  loop_interval: 60

trading:
  default_exchange: "kucoin"
  base_currency: "USDT"
  quote_currencies: ["BTC", "ETH", "BNB", "ADA", "DOT"]
  max_positions: 10
  max_position_size: 10000.0
  min_position_size: 10.0
  default_order_size: 1000.0
  slippage_tolerance: 0.001
  commission_rate: 0.001
  enable_short_selling: false
  enable_margin_trading: false
  enable_futures_trading: false
  enable_options_trading: false
  min_confidence_threshold: 0.6
  max_daily_trades: 20
  risk_tolerance: "medium"
  technical_weight: 0.35
  sentiment_weight: 0.25
  pattern_weight: 0.20
  prediction_weight: 0.20
  risk_weight: 0.30

ai:
  model_type: "ensemble"
  prediction_horizon: 24
  confidence_threshold: 0.7
  retrain_interval: 24
  feature_count: 50
  lookback_period: 168
  validation_split: 0.2
  test_split: 0.1
  batch_size: 32
  epochs: 100
  learning_rate: 0.001
  dropout_rate: 0.2
  early_stopping_patience: 10
  model_save_path: "models"
  enable_feature_selection: true
  enable_hyperparameter_tuning: true
  enable_ensemble_voting: true
  sentiment_weight: 0.25
  technical_weight: 0.35
  pattern_weight: 0.20
  prediction_weight: 0.20
  signal_cooldown_minutes: 15

risk:
  max_portfolio_risk: 0.02
  max_position_risk: 0.005
  max_daily_loss: 0.01
  max_drawdown: 0.05
  position_size_limit: 0.20
  max_open_orders: 10
  max_concentration: 0.20
  max_correlation: 0.80
  min_liquidity: 0.70
  max_leverage: 2.0
  var_confidence: 0.95
  var_horizon: 1
  stress_test_scenarios: 1000
  correlation_threshold: 0.8
  concentration_limit: 0.3
  leverage_limit: 1.0
  stop_loss_percentage: 0.02
  take_profit_percentage: 0.04
  enable_stop_loss: true
  enable_take_profit: true
  enable_trailing_stop: false
  enable_position_sizing: true
  enable_correlation_check: true
  enable_concentration_check: true
  risk_free_rate: 0.02

data:
  primary_source: "exchange_api"
  update_frequency: 60
  cache_duration: 300
  max_cache_size: 1000
  enable_data_validation: true
  enable_data_cleaning: true
  enable_outlier_detection: true
  data_retention_days: 365
  enable_compression: true
  compression_algorithm: "gzip"

execution:
  default_venue: "binance"
  execution_algorithm: "twap"
  order_timeout: 300
  max_order_size: 100000.0
  min_order_size: 10.0
  enable_smart_routing: true
  enable_order_splitting: true
  max_order_splits: 10
  commission_model: "percentage"
  enable_pre_trade_checks: true
  enable_post_trade_analysis: true
  latency_threshold: 100.0
  fill_ratio_threshold: 0.95
  slippage_threshold: 0.002
  max_slippage: 0.005
  order_timeout_seconds: 300
  retry_attempts: 3
  execution_delay_seconds: 0.1
  venue_timeout_seconds: 30
  smart_routing_enabled: true
  max_daily_volume: 1000000.0
  max_position_size: 100000.0
  max_orders_per_minute: 60
  max_venues_per_order: 3
  sandbox_mode: true

monitoring:
  metrics_interval: 60
  health_check_interval: 30
  performance_tracking: true
  system_monitoring: true
  enable_profiling: false
  enable_memory_monitoring: true
  enable_cpu_monitoring: true
  enable_disk_monitoring: true
  enable_network_monitoring: true
  max_log_file_size: 100
  max_log_files: 10
  log_rotation_interval: "daily"
  metrics_retention_days: 30
  dashboard_refresh_interval: 5
  monitoring_interval_seconds: 60
  health_check_interval_seconds: 30
  cleanup_interval_hours: 24
  retention_days: 30
  cpu_warning_threshold: 70.0
  cpu_critical_threshold: 90.0
  memory_warning_threshold: 75.0
  memory_critical_threshold: 90.0
  disk_warning_threshold: 80.0
  disk_critical_threshold: 95.0
  response_time_warning_ms: 500
  response_time_critical_ms: 1000
  error_rate_warning_threshold: 5.0
  error_rate_critical_threshold: 10.0
  gpu_warning_threshold: 80.0
  gpu_critical_threshold: 95.0
  alert_cooldown_seconds: 300