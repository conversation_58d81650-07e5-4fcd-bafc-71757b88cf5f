# Development Environment Configuration
# Author: inkbytefo

general:
  debug: true
  log_level: "DEBUG"
  enable_metrics: true
  enable_monitoring: true
  enable_alerts: true

trading:
  # Use smaller amounts for development
  max_position_size: 1000.0
  default_order_size: 100.0
  min_position_size: 10.0
  max_daily_trades: 5
  enable_paper_trading: true

ai:
  # Faster training for development
  epochs: 10
  batch_size: 16
  retrain_interval: 6  # hours
  enable_hyperparameter_tuning: false

risk:
  # More conservative for development
  max_portfolio_risk: 0.01
  max_position_risk: 0.002
  max_daily_loss: 0.005
  max_drawdown: 0.02

data:
  # More frequent updates for development
  update_frequency: 30
  cache_duration: 60
  data_retention_days: 30

execution:
  # Test mode settings
  sandbox_mode: true
  order_timeout: 60
  max_order_size: 1000.0
  enable_pre_trade_checks: true

monitoring:
  # More frequent monitoring for development
  metrics_interval: 30
  health_check_interval: 15
  dashboard_refresh_interval: 2
  retention_days: 7