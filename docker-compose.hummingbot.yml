version: '3.8'

services:
  hummingbot-gateway:
    image: hummingbot/gateway:latest
    container_name: hummingbot-gateway
    ports:
      - "15888:15888"  # Gateway API port
    environment:
      - GATEWAY_PASSPHRASE=hummingbot123
      - GATEWAY_CERT_PASSPHRASE=hummingbot123
    volumes:
      - ./hummingbot_gateway_conf:/home/<USER>/conf
      - ./hummingbot_gateway_certs:/home/<USER>/certs
      - ./hummingbot_gateway_logs:/home/<USER>/logs
    networks:
      - hummingbot-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:15888/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  hummingbot-client:
    image: hummingbot/hummingbot:latest
    container_name: hummingbot-client
    depends_on:
      - hummingbot-gateway
    ports:
      - "8000:8000"  # Hummingbot API port
    environment:
      - CONFIG_PASSWORD=hummingbot123
    command: ["/bin/bash", "-c", "conda run -n hummingbot /home/<USER>/miniconda3/envs/hummingbot/bin/python -m hummingbot.client.ui.hummingbot_cli --enable-api"]
    volumes:
      - ./hummingbot_conf:/home/<USER>/conf
      - ./hummingbot_logs:/home/<USER>/logs
      - ./hummingbot_data:/home/<USER>/data
    networks:
      - hummingbot-network
    restart: unless-stopped
    stdin_open: true
    tty: true

networks:
  hummingbot-network:
    driver: bridge

volumes:
  hummingbot_gateway_conf:
  hummingbot_gateway_certs:
  hummingbot_gateway_logs:
  hummingbot_conf:
  hummingbot_logs:
  hummingbot_data: