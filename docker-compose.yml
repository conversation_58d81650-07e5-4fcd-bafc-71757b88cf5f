services:
  # AI Trading System Services
  ai-trading-system:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: ai-trading-system
    ports:
      - "5000:5000"  # Flask API port
      - "8080:8080"  # Dashboard port
    environment:
      - FLASK_ENV=production
      - DATABASE_URL=********************************************/trading_db
      - REDIS_URL=redis://redis:6379/0
      - HUMMINGBOT_API_HOST=hummingbot-api
      - HUMMINGBOT_API_PORT=8000
      - HUMMINGBOT_GATEWAY_HOST=hummingbot-gateway
      - HUMMINGBOT_GATEWAY_PORT=15888
      - BROKER_HOST=hummingbot-broker
      - BROKER_PORT=1883
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
      - ./data:/app/data
      - ./models:/app/models
    networks:
      - trading-network
    depends_on:
      - postgres
      - redis
      - hummingbot-api
      - hummingbot-broker
    restart: unless-stopped

  # Database Services
  postgres:
    image: postgres:15
    container_name: trading-postgres
    environment:
      - POSTGRES_DB=trading_db
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres-data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - trading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d trading_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    container_name: trading-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - trading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Hummingbot Services
  hummingbot-api:
    image: hummingbot/hummingbot-api:latest
    container_name: hummingbot-api
    ports:
      - "8000:8000"
    volumes:
      - ./hummingbot/bots:/hummingbot-api/bots
      - /var/run/docker.sock:/var/run/docker.sock
      - ./hummingbot/.env:/hummingbot-api/.env
    environment:
      - BROKER_HOST=hummingbot-broker
      - DATABASE_URL=postgresql+asyncpg://hbot:hummingbot-api@hummingbot-postgres:5432/hummingbot_api
      - CONFIG_PASSWORD=hummingbot123
      - USERNAME=admin
      - PASSWORD=admin
    networks:
      - trading-network
    depends_on:
      - hummingbot-postgres
      - hummingbot-broker
    restart: unless-stopped

  hummingbot-broker:
    image: emqx:5
    container_name: hummingbot-broker
    restart: unless-stopped
    environment:
      - EMQX_NAME=emqx
      - EMQX_HOST=node1.emqx.local
      - EMQX_CLUSTER__DISCOVERY_STRATEGY=static
      - EMQX_CLUSTER__STATIC__SEEDS=[<EMAIL>]
      - EMQX_LOADED_PLUGINS="emqx_recon,emqx_retainer,emqx_management,emqx_dashboard"
    volumes:
      - emqx-data:/opt/emqx/data
      - emqx-log:/opt/emqx/log
      - emqx-etc:/opt/emqx/etc
    ports:
      - "1883:1883"   # mqtt:tcp
      - "8883:8883"   # mqtt:tcp:ssl
      - "8083:8083"   # mqtt:ws
      - "8084:8084"   # mqtt:ws:ssl
      - "8081:8081"   # http:management
      - "18083:18083" # http:dashboard
      - "61613:61613" # web-stomp gateway
    networks:
      trading-network:
        aliases:
          - node1.emqx.local
    healthcheck:
      test: ["CMD", "/opt/emqx/bin/emqx_ctl", "status"]
      interval: 5s
      timeout: 25s
      retries: 5

  hummingbot-postgres:
    image: postgres:15
    container_name: hummingbot-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=hummingbot_api
      - POSTGRES_USER=hbot
      - POSTGRES_PASSWORD=hummingbot-api
    volumes:
      - hummingbot-postgres-data:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - trading-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U hbot -d hummingbot_api"]
      interval: 10s
      timeout: 5s
      retries: 5

  hummingbot-gateway:
    image: hummingbot/gateway:latest
    container_name: hummingbot-gateway
    ports:
      - "15888:15888"
    environment:
      - GATEWAY_PASSPHRASE=hummingbot123
      - GATEWAY_CERT_PASSPHRASE=hummingbot123
    volumes:
      - ./hummingbot/gateway_conf:/home/<USER>/conf
      - ./hummingbot/gateway_certs:/home/<USER>/certs
      - ./hummingbot/gateway_logs:/home/<USER>/logs
    networks:
      - trading-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "https://localhost:15888/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Optional: Hummingbot Dashboard
  hummingbot-dashboard:
    image: hummingbot/dashboard:latest
    container_name: hummingbot-dashboard
    ports:
      - "8501:8501"
    environment:
      - AUTH_SYSTEM_ENABLED=True
      - BACKEND_API_HOST=hummingbot-api
      - BACKEND_API_PORT=8000
      - BACKEND_API_USERNAME=admin
      - BACKEND_API_PASSWORD=admin
    volumes:
      - ./hummingbot/credentials.yml:/home/<USER>/credentials.yml
    networks:
      - trading-network
    depends_on:
      - hummingbot-api
    restart: unless-stopped

networks:
  trading-network:
    driver: bridge

volumes:
  postgres-data:
  redis-data:
  emqx-data:
  emqx-log:
  emqx-etc:
  hummingbot-postgres-data: