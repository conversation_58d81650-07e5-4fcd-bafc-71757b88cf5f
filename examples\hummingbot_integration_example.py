"""Hummingbot Integration Example.

This example demonstrates how to integrate with Hummingbot API
for automated trading bot management and monitoring.

Author: inkbytefo
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

from src.execution.hummingbot_client import (
    <PERSON><PERSON>botAPIClient,
    BotConfig,
    <PERSON>t<PERSON>tatus,
    OrderSide,
    OrderType,
    create_hummingbot_client
)
from src.config.hummingbot_config import HummingbotConfigManager
from src.utils.logger import get_logger


class HummingbotIntegrationExample:
    """Example class demonstrating Hummingbot integration."""
    
    def __init__(self):
        self.logger = get_logger(self.__class__.__name__)
        self.client: HummingbotAPIClient = None
        self.config_manager = HummingbotConfigManager()
    
    async def initialize(self):
        """Initialize Hummingbot client connection."""
        try:
            # Load configuration
            config = self.config_manager.load_config()
            
            # Create client with configuration
            self.client = await create_hummingbot_client(
                api_host=config.api.host,
                api_port=config.api.port,
                mqtt_host=config.api.mqtt_host,
                mqtt_port=config.api.mqtt_port,
                username=config.api.username,
                password=config.api.password
            )
            
            self.logger.info("Hummingbot client initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Hummingbot client: {e}")
            raise
    
    async def health_check(self) -> bool:
        """Perform health check on Hummingbot API.
        
        Returns:
            True if healthy, False otherwise
        """
        try:
            health_status = await self.client.health_check()
            self.logger.info(f"Hummingbot API health status: {health_status}")
            return health_status.get("status") == "healthy"
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            return False
    
    async def create_sample_bot(self) -> str:
        """Create a sample trading bot.
        
        Returns:
            Bot ID
        """
        try:
            # Define bot configuration
            bot_config = BotConfig(
                id="",  # Will be assigned by API
                name="sample_pure_market_making_bot",
                strategy="pure_market_making",
                exchange="binance",
                trading_pair="BTC-USDT",
                base_asset="BTC",
                quote_asset="USDT",
                parameters={
                    "bid_spread": 0.1,
                    "ask_spread": 0.1,
                    "order_amount": 0.001,
                    "order_refresh_time": 30,
                    "max_order_age": 1800,
                    "inventory_skew_enabled": True,
                    "inventory_target_base_pct": 50,
                    "filled_order_delay": 60,
                    "hanging_orders_enabled": True,
                    "hanging_orders_cancel_pct": 10
                }
            )
            
            # Create bot
            bot_id = await self.client.create_bot(bot_config)
            self.logger.info(f"Created sample bot with ID: {bot_id}")
            
            return bot_id
            
        except Exception as e:
            self.logger.error(f"Failed to create sample bot: {e}")
            raise
    
    async def manage_bot_lifecycle(self, bot_id: str):
        """Demonstrate bot lifecycle management.
        
        Args:
            bot_id: Bot identifier
        """
        try:
            # Get initial bot status
            bot_status = await self.client.get_bot_status(bot_id)
            self.logger.info(f"Bot {bot_id} initial status: {bot_status.status}")
            
            # Start the bot
            if bot_status.status == BotStatus.STOPPED:
                await self.client.start_bot(bot_id)
                self.logger.info(f"Started bot {bot_id}")
                
                # Wait a bit for bot to initialize
                await asyncio.sleep(5)
            
            # Monitor bot for a while
            monitoring_duration = 60  # seconds
            start_time = datetime.now()
            
            while (datetime.now() - start_time).seconds < monitoring_duration:
                # Check bot status
                current_status = await self.client.get_bot_status(bot_id)
                self.logger.info(f"Bot {bot_id} status: {current_status.status}")
                
                # Get performance metrics
                try:
                    metrics = await self.client.get_performance_metrics(bot_id)
                    self.logger.info(
                        f"Bot {bot_id} metrics - PnL: {metrics.total_pnl}, "
                        f"Trades: {metrics.total_trades}, Win Rate: {metrics.win_rate:.2%}"
                    )
                except Exception as e:
                    self.logger.warning(f"Could not get performance metrics: {e}")
                
                await asyncio.sleep(10)
            
            # Stop the bot
            await self.client.stop_bot(bot_id)
            self.logger.info(f"Stopped bot {bot_id}")
            
        except Exception as e:
            self.logger.error(f"Error managing bot lifecycle: {e}")
            raise
    
    async def demonstrate_manual_trading(self):
        """Demonstrate manual order placement."""
        try:
            # Get current portfolio balance
            balance = await self.client.get_portfolio_balance("binance")
            self.logger.info(f"Current portfolio balance: {balance}")
            
            # Get market data
            ticker = await self.client.get_ticker("binance", "BTC-USDT")
            current_price = float(ticker["last_price"])
            self.logger.info(f"Current BTC-USDT price: {current_price}")
            
            # Place a small buy order (example - be careful with real money!)
            if balance.get("USDT", 0) > 50:  # Only if we have enough USDT
                buy_price = current_price * 0.99  # 1% below market
                order_amount = 0.001  # Small amount for testing
                
                order_id = await self.client.place_order(
                    exchange="binance",
                    trading_pair="BTC-USDT",
                    side=OrderSide.BUY,
                    order_type=OrderType.LIMIT,
                    amount=order_amount,
                    price=buy_price
                )
                
                self.logger.info(f"Placed buy order {order_id} for {order_amount} BTC at {buy_price}")
                
                # Wait a bit then check order status
                await asyncio.sleep(5)
                order_status = await self.client.get_order_status(order_id)
                self.logger.info(f"Order {order_id} status: {order_status.status}")
                
                # Cancel the order if it's still open
                if order_status.status in ["PENDING", "OPEN"]:
                    await self.client.cancel_order(order_id)
                    self.logger.info(f"Cancelled order {order_id}")
            
        except Exception as e:
            self.logger.error(f"Error in manual trading demonstration: {e}")
    
    async def setup_real_time_monitoring(self, bot_id: str):
        """Setup real-time monitoring for a bot.
        
        Args:
            bot_id: Bot identifier to monitor
        """
        try:
            # Define callback for bot updates
            async def bot_update_callback(topic: str, payload: Dict[str, Any]):
                self.logger.info(f"Bot update received on {topic}: {payload}")
                
                if "status" in payload:
                    self.logger.info(f"Bot {bot_id} status changed to: {payload['status']}")
                
                if "order" in payload:
                    order_data = payload["order"]
                    self.logger.info(
                        f"Order update for bot {bot_id}: {order_data['side']} "
                        f"{order_data['amount']} {order_data['trading_pair']} "
                        f"at {order_data.get('price', 'market')} - Status: {order_data['status']}"
                    )
            
            # Define callback for market updates
            async def market_update_callback(topic: str, payload: Dict[str, Any]):
                if "ticker" in payload:
                    ticker_data = payload["ticker"]
                    self.logger.info(
                        f"Market update: {ticker_data['symbol']} - "
                        f"Price: {ticker_data['last_price']}, "
                        f"Volume: {ticker_data['volume']}"
                    )
            
            # Subscribe to updates
            self.client.subscribe_to_bot_updates(bot_id, bot_update_callback)
            self.client.subscribe_to_market_updates("binance", "BTC-USDT", market_update_callback)
            
            self.logger.info(f"Setup real-time monitoring for bot {bot_id}")
            
            # Keep monitoring for a while
            await asyncio.sleep(300)  # Monitor for 5 minutes
            
        except Exception as e:
            self.logger.error(f"Error setting up real-time monitoring: {e}")
    
    async def list_and_manage_all_bots(self):
        """List all bots and perform basic management."""
        try:
            # Get all bots
            bots = await self.client.list_bots()
            self.logger.info(f"Found {len(bots)} bots")
            
            for bot in bots:
                self.logger.info(
                    f"Bot {bot.id}: {bot.name} ({bot.strategy}) - "
                    f"Status: {bot.status}, Pair: {bot.trading_pair}"
                )
                
                # Get performance metrics for running bots
                if bot.status == BotStatus.RUNNING:
                    try:
                        metrics = await self.client.get_performance_metrics(
                            bot.id,
                            start_date=datetime.now() - timedelta(days=1)
                        )
                        
                        self.logger.info(
                            f"Bot {bot.id} 24h metrics - "
                            f"PnL: {metrics.total_pnl:.4f}, "
                            f"Trades: {metrics.total_trades}, "
                            f"Win Rate: {metrics.win_rate:.2%}"
                        )
                    except Exception as e:
                        self.logger.warning(f"Could not get metrics for bot {bot.id}: {e}")
            
        except Exception as e:
            self.logger.error(f"Error listing and managing bots: {e}")
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.client:
            await self.client.disconnect()
            self.logger.info("Disconnected from Hummingbot API")
    
    async def run_full_example(self):
        """Run the complete integration example."""
        try:
            # Initialize
            await self.initialize()
            
            # Health check
            if not await self.health_check():
                self.logger.error("Hummingbot API is not healthy, aborting example")
                return
            
            # List existing bots
            await self.list_and_manage_all_bots()
            
            # Create a sample bot
            bot_id = await self.create_sample_bot()
            
            # Demonstrate bot lifecycle management
            await self.manage_bot_lifecycle(bot_id)
            
            # Demonstrate manual trading
            await self.demonstrate_manual_trading()
            
            # Setup real-time monitoring (commented out for brevity)
            # await self.setup_real_time_monitoring(bot_id)
            
            # Clean up - delete the sample bot
            await self.client.delete_bot(bot_id)
            self.logger.info(f"Deleted sample bot {bot_id}")
            
        except Exception as e:
            self.logger.error(f"Error in full example: {e}")
        finally:
            await self.cleanup()


async def main():
    """Main function to run the example."""
    # Setup logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Run the example
    example = HummingbotIntegrationExample()
    await example.run_full_example()


if __name__ == "__main__":
    # Run the example
    asyncio.run(main())