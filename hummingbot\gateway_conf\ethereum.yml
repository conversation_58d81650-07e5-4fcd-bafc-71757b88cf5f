# Global settings for Ethereum
# Gas parameters for transactions (applies to all networks)
gasLimitTransaction: 3000000
manualGasPrice: 33

# Network-specific configurations
networks:
  mainnet:
    chainID: 1
    nodeURL: https://eth.llamarpc.com
    tokenListType: FILE
    nativeCurrencySymbol: ETH
    tokenListSource: /home/<USER>/conf/lists/mainnet.json
    gasPriceRefreshInterval: 60
  arbitrum:
    chainID: 42161
    nodeURL: https://arb1.arbitrum.io/rpc
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/arbitrum.json
    nativeCurrencySymbol: ETH
    gasPriceRefreshInterval: 60
  optimism:
    chainID: 10
    nodeURL: https://mainnet.optimism.io
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/optimism.json
    nativeCurrencySymbol: OETH
    gasPriceRefreshInterval: 60
  base:
    chainID: 8453
    nodeURL: https://mainnet.base.org
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/base.json
    nativeCurrencySymbol: ETH
    gasPriceRefreshInterval: 60
  sepolia:
    chainID: 11155111
    nodeURL: https://rpc.ankr.com/eth_sepolia 
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/sepolia.json
    nativeCurrencySymbol: ETH
    gasPriceRefreshInterval: 60
  bsc:
    chainID: 56
    nodeURL: https://binance.llamarpc.com
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/bsc.json
    nativeCurrencySymbol: BNB
    gasPriceRefreshInterval: 60
  avalanche:
    chainID: 43114
    nodeURL: https://api.avax.network/ext/bc/C/rpc
    tokenListType: FILE
    tokenListSource: conf/lists/avalanche.json
    nativeCurrencySymbol: AVAX
    gasPriceRefreshInterval: 60
  celo:
    chainID: 42220
    nodeURL: https://rpc.ankr.com/celo
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/celo.json
    nativeCurrencySymbol: CELO
    gasPriceRefreshInterval: 60
  polygon:
    chainID: 137
    nodeURL: https://rpc.ankr.com/polygon
    tokenListType: FILE
    tokenListSource: conf/lists/polygon.json
    nativeCurrencySymbol: POL
    gasPriceRefreshInterval: 60
  blast:
    chainID: 81457
    nodeURL: https://rpc.blast.io
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/mainnet.json
    nativeCurrencySymbol: ETH
    gasPriceRefreshInterval: 60
  zora:
    chainID: 7777777
    nodeURL: https://rpc.zora.energy
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/mainnet.json
    nativeCurrencySymbol: ETH
    gasPriceRefreshInterval: 60
  worldchain:
    chainID: 480
    nodeURL: https://worldchain-mainnet.g.alchemy.com/public
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/worldchain.json
    nativeCurrencySymbol: WLD
    gasPriceRefreshInterval: 60
