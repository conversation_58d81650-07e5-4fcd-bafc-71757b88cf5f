[{"chainId": 8453, "name": "Aerodrome Finance", "symbol": "AERO", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Coinbase Wrapped BTC", "symbol": "cbBTC", "address": "******************************************", "decimals": 8}, {"chainId": 8453, "name": "Coinbase Wrapped Staked ETH", "symbol": "cbETH", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Compound", "symbol": "COMP", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Degen", "symbol": "DEGEN", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "EURC", "symbol": "EURC", "address": "******************************************", "decimals": 6}, {"chainId": 8453, "name": "Dai Stablecoin", "symbol": "DAI", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Wrapped Ether", "symbol": "WETH", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "LayerZero", "symbol": "ZRO", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "WELL", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "SPX6900", "symbol": "SPX", "address": "******************************************", "decimals": 8}, {"chainId": 8453, "name": "USD Coin", "symbol": "USDC", "address": "******************************************", "decimals": 6}, {"chainId": 8453, "name": "Virtual Protocol", "symbol": "VIRTUAL", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Aave", "symbol": "aave", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "ghst", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "ACryptoS", "symbol": "acs", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "AGENT DOGE by Virtuals", "symbol": "<PERSON><PERSON><PERSON>", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "aixbt by Virtuals", "symbol": "aixbt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Alchemist Accelerate", "symbol": "alch", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Artificial Liquid Intelligence", "symbol": "ali", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Altura", "symbol": "alu", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Amped Finance", "symbol": "amp", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>", "symbol": "andy", "address": "0x18a8bd1fe17a1bb9ffb39ecd83e9489cfd17a022", "decimals": 18}, {"chainId": 8453, "name": "Anime", "symbol": "anime", "address": "0x0e0c9756a3290cd782cf4ab73ac24d25291c9564", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>z", "symbol": "usdz", "address": "0x04d5ddf5f3a8939889f11e97f8c4bb48317f1938", "decimals": 18}, {"chainId": 8453, "name": "Apu Apus<PERSON>ja", "symbol": "apu", "address": "0x3159fb5589acd6bf9f82eb0efe8382ed55aed8fd", "decimals": 18}, {"chainId": 8453, "name": "Arc", "symbol": "arc", "address": "0x61ca70b867a48265e553a7fbb81bfe44fada7ae6", "decimals": 18}, {"chainId": 8453, "name": "Archly Finance", "symbol": "arc", "address": "0xe8876189a80b2079d8c0a7867e46c50361d972c1", "decimals": 18}, {"chainId": 8453, "name": "ATA by Virtuals", "symbol": "ata", "address": "0xb18c609796848c723eacadc0be5b71ceb2289a48", "decimals": 18}, {"chainId": 8453, "name": "AthenaDAO", "symbol": "ath", "address": "0x58d75a1c4477914f9a98a8708feaed1dbe40b8a3", "decimals": 18}, {"chainId": 8453, "name": "AUKI", "symbol": "auki", "address": "0xf9569cfb8fd265e91aa478d86ae8c78b8af55df4", "decimals": 18}, {"chainId": 8453, "name": "Autonolas", "symbol": "olas", "address": "0x54330d28ca3357f294334bdc454a032e7f353416", "decimals": 18}, {"chainId": 8453, "name": "Avail", "symbol": "avail", "address": "0xd89d90d26b48940fa8f58385fe84625d468e057a", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "axl", "address": "0x23ee2343b892b1bb63503a4fabc840e0e2c6810f", "decimals": 18}, {"chainId": 8453, "name": "AxonDAO Governance Token", "symbol": "axgt", "address": "0x9b700b043e9587dde9a0c29a9483e2f8fa450d54", "decimals": 18}, {"chainId": 8453, "name": "B3 (Base)", "symbol": "b3", "address": "0xb3b32f9f8827d4634fe7d973fa1034ec9fddb3b3", "decimals": 18}, {"chainId": 8453, "name": "Balancer", "symbol": "bal", "address": "0x4158734d47fc9692176b5085e0f52ee0da5d47f1", "decimals": 18}, {"chainId": 8453, "name": "Based Andy", "symbol": "andy", "address": "0x543ba622733bc9a7bfadd1d07b6c35ae1f9659d9", "decimals": 18}, {"chainId": 8453, "name": "Based Apu", "symbol": "apu", "address": "0x6f35720b272bf23832852b13ae9888c706e1a379", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>", "symbol": "brett", "address": "0x532f27101965dd16442e59d40670faf5ebb142e4", "decimals": 18}, {"chainId": 8453, "name": "Based Fartcoin", "symbol": "fartcoin", "address": "0x2f6c17fa9f9bc3600346ab4e48c0701e1d5962ae", "decimals": 18}, {"chainId": 8453, "name": "Based Fwog", "symbol": "fwog", "address": "0x1035ae3f87a91084c6c5084d0615cc6121c5e228", "decimals": 18}, {"chainId": 8453, "name": "Base DOG", "symbol": "dog", "address": "0x3b916b8f6a710e9240ff08c1dd646dd8e8ed9e1e", "decimals": 18}, {"chainId": 8453, "name": "Based Pepe", "symbol": "pepe", "address": "0x52b492a33e447cdb854c7fc19f1e57e8bfa1777d", "decimals": 18}, {"chainId": 8453, "name": "Based Turbo", "symbol": "turbo", "address": "0xba5e66fb16944da22a62ea4fd70ad02008744460", "decimals": 18}, {"chainId": 8453, "name": "BaseSafe", "symbol": "safe", "address": "0xbd15d0c77133d3200756dc4d7a4f577dbb2cf6a3", "decimals": 18}, {"chainId": 8453, "name": "Basic Dog Meme", "symbol": "dog", "address": "0x9e53e88dcff56d3062510a745952dec4cefdff9e", "decimals": 18}, {"chainId": 8453, "name": "BIM", "symbol": "bim", "address": "0x555fff48549c1a25a723bd8e7ed10870d82e8379", "decimals": 18}, {"chainId": 8453, "name": "Bio Protocol", "symbol": "bio", "address": "0x226a2fa2556c48245e57cd1cba4c6c9e67077dd2", "decimals": 18}, {"chainId": 8453, "name": "Blue", "symbol": "blue", "address": "0x7f65323e468939073ef3b5287c73f13951b0ff5b", "decimals": 18}, {"chainId": 8453, "name": "Blue Guy", "symbol": "blue", "address": "0x891502ba08132653151f822a3a430198f1844115", "decimals": 18}, {"chainId": 8453, "name": "BMX", "symbol": "bmx", "address": "0x548f93779fbc992010c07467cbaf329dd5f059b7", "decimals": 18}, {"chainId": 8453, "name": "BOBO Coin", "symbol": "bobo", "address": "0x570b1533f6daa82814b25b62b5c7c4c55eb83947", "decimals": 18}, {"chainId": 8453, "name": "Bonk On Base", "symbol": "bonk", "address": "0x72499bddb67f4ca150e1f522ca82c87bc9fb18c8", "decimals": 18}, {"chainId": 8453, "name": "Bridged FRAX", "symbol": "frax", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON>", "symbol": "ctsi", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "CARV", "symbol": "carv", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "cat", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "cgETH Hashkey Cloud", "symbol": "cgeth.hashkey", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Onyxcoin", "symbol": "xcn", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Chainlink", "symbol": "link", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "CHEX Token", "symbol": "chex", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "clBTC", "symbol": "clbtc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Compounding OpenDollar", "symbol": "cusdo", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Constellation", "symbol": "dag", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "cookie", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "cookie", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>rtex", "symbol": "cx", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Cow", "symbol": "cow", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "crow with knife", "symbol": "caw", "address": "0xdfbea88c4842d30c26669602888d746d30f9d60d", "decimals": 18}, {"chainId": 8453, "name": "crvUSD", "symbol": "crvusd", "address": "0x417ac0e078398c154edfadd9ef675d30be60af93", "decimals": 18}, {"chainId": 8453, "name": "Curve DAO", "symbol": "crv", "address": "0x8ee73c484a26e0a5df2ee2a4960b789967dd0415", "decimals": 18}, {"chainId": 8453, "name": "Definitive", "symbol": "edge", "address": "0xed6e000def95780fb89734c07ee2ce9f6dcaf110", "decimals": 18}, {"chainId": 8453, "name": "Department Of Government Efficiency", "symbol": "doge", "address": "0x67f0870bb897f5e1c369976b4a2962d527b9562c", "decimals": 18}, {"chainId": 8453, "name": "Derive", "symbol": "drv", "address": "0x9d0e8f5b25384c7310cb8c6ae32c8fbeb645d083", "decimals": 18}, {"chainId": 8453, "name": "doginme", "symbol": "doginme", "address": "0x6921b130d297cc43754afba22e5eac0fbf8db75b", "decimals": 18}, {"chainId": 8453, "name": "DogWifHat", "symbol": "wif", "address": "0x7f6f6720a73c0f54f95ab343d7efeb1fa991f4f7", "decimals": 18}, {"chainId": 8453, "name": "DOLA", "symbol": "dola", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Echelon Prime", "symbol": "prime", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "ECOMI", "symbol": "omi", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Empyreal", "symbol": "emp", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Ethos Reserve Note", "symbol": "ern", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "FAME AI", "symbol": "fmc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Fasttoken", "symbol": "ftn", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Freysa AI", "symbol": "fai", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Gains Network", "symbol": "gns", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "GAME by Virtuals", "symbol": "game", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Game of Memes (ETH)", "symbol": "game", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "GME (Base)", "symbol": "gme", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "GHO", "symbol": "gho", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "GoPlus Security", "symbol": "gps", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Grass", "symbol": "grass", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Gravity (by <PERSON><PERSON><PERSON>)", "symbol": "g", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "HarryPotterObamaSonic10Inu (ETH)", "symbol": "bitcoin", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Synthetix Network", "symbol": "snx", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Hey <PERSON>on", "symbol": "anon", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "hippo", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>", "symbol": "hunt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "IoTeX", "symbol": "iotx", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "IXS", "symbol": "ixs", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "KAITO", "symbol": "kaito", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "kta", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "KelpDAO Bridged rsETH (Base)", "symbol": "r<PERSON>h", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Keyboard Cat (Base)", "symbol": "keycat", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "L2 Standard Bridged USDT (Base)", "symbol": "usdt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Landwolf", "symbol": "wolf", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Layer3", "symbol": "l3", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "LEOONO by Virtuals", "symbol": "leo", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "LimeWire", "symbol": "lmwr", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Liquid Staked ETH", "symbol": "l<PERSON>h", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Liquity USD", "symbol": "lusd", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Lombard Staked BTC", "symbol": "lbtc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Luna by Virtuals", "symbol": "luna", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "MAG7.ssi", "symbol": "mag7.ssi", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "MAGA", "symbol": "trump", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Magic Internet Money (Base)", "symbol": "mim", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "MANTRA", "symbol": "om", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Maverick Protocol", "symbol": "mav", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Metacade", "symbol": "mcade", "address": "0xc48823ec67720a04a9dfd8c7d109b2c3d6622094", "decimals": 18}, {"chainId": 8453, "name": "MetaFight Token", "symbol": "mft", "address": "0xb372dc09d8d84e1246760ee9d279e504a89f5684", "decimals": 18}, {"chainId": 8453, "name": "MetaInside by Virtuals", "symbol": "min", "address": "0xbb59167235bf3588b357de6cd98ca6f94d753c76", "decimals": 18}, {"chainId": 8453, "name": "Mr. <PERSON>", "symbol": "miggles", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "miu", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Moca Network", "symbol": "moca", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Mog Coin", "symbol": "mog", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Moonwell Flagship ETH (Morpho Vault)", "symbol": "mweth", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Morp<PERSON>", "symbol": "morpho", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Mountain Protocol USD", "symbol": "usdm", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON>", "symbol": "mnt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Non-Playable Coin", "symbol": "npc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Olympus", "symbol": "ohm", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "OmniCat", "symbol": "omni", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Omnis Genesis by Virtuals", "symbol": "omni", "address": "0xb58f9704c7a80d2775222f7cb2eed28beb9a06be", "decimals": 18}, {"chainId": 8453, "name": "OpenEden OpenDollar", "symbol": "usdo", "address": "0xad55aebc9b8c03fc43cd9f62260391c13c23e7c0", "decimals": 18}, {"chainId": 8453, "name": "ORA Coin", "symbol": "ora", "address": "0x333333c465a19c85f85c6cfbed7b16b0b26e3333", "decimals": 18}, {"chainId": 8453, "name": "Orbiter Finance", "symbol": "obt", "address": "0x514d8e8099286a13486ef6c525c120f51c239b52", "decimals": 18}, {"chainId": 8453, "name": "Origin Token", "symbol": "ogn", "address": "0x7002458b1df59eccb57387bc79ffc7c29e22e6f7", "decimals": 18}, {"chainId": 8453, "name": "Osaka Protocol", "symbol": "osak", "address": "0xbfd5206962267c7b4b4a8b3d76ac2e1b2a5c4d5e", "decimals": 18}, {"chainId": 8453, "name": "Overnight.fi USD+ (Base)", "symbol": "usd+", "address": "0xb79dd08ea68a908a97220c76d19a6aa9cbde4376", "decimals": 18}, {"chainId": 8453, "name": "PAAL AI", "symbol": "paal", "address": "0xd52333441c0553facb259600fa833a69186893a5", "decimals": 18}, {"chainId": 8453, "name": "PancakeSwap", "symbol": "cake", "address": "0x3055913c90fcc1a6ce9a358911721eeb942013a1", "decimals": 18}, {"chainId": 8453, "name": "Particle Network", "symbol": "parti", "address": "0x59264f02d301281f3393e1385c0aefd446eb0f00", "decimals": 18}, {"chainId": 8453, "name": "Peapods Finance", "symbol": "peas", "address": "0x02f92800f57bcd74066f5709f1daa1a4302df875", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "pendle", "address": "0xa99f6e6785da0f5d6fb42495fe424bce029eeb3e", "decimals": 18}, {"chainId": 8453, "name": "PEPE 0x69 ON BASE", "symbol": "pepe", "address": "0x698dc45e4f10966f6d1d98e3bfd7071d8144c233", "decimals": 18}, {"chainId": 8453, "name": "Pepe on Base", "symbol": "pepe", "address": "0x80f45eacf6537498ecc660e4e4a2d2f99e195cf4", "decimals": 18}, {"chainId": 8453, "name": "PONKE", "symbol": "ponke", "address": "0x4a0c64af541439898448659aedcec8e8e819fc53", "decimals": 18}, {"chainId": 8453, "name": "Propy", "symbol": "pro", "address": "0x18dd5b087bca9920562aff7a0199b96b9230438b", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "purr", "address": "0xe4fcf2d991505089bbb36275570757c1f9800cb0", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>", "symbol": "qi", "address": "0xd3fdcb837dafdb7c9c3ebd48fe22a53f6dd3d7d7", "decimals": 18}, {"chainId": 8453, "name": "Radiant Capital", "symbol": "rdnt", "address": "0xd722e55c1d9d9fa0021a5215cbb904b92b3dc5d4", "decimals": 18}, {"chainId": 8453, "name": "Reactor", "symbol": "arc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Rekt", "symbol": "rekt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "$REKT", "symbol": "rekt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "rez", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON>zo Restaked ETH", "symbol": "ezeth", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "ResearchCoin", "symbol": "rsc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Reserve Rights", "symbol": "rsr", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Ribbita by Virtuals", "symbol": "tibbir", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Rocket Pool ETH", "symbol": "reth", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON> by Virtuals", "symbol": "sai", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Send", "symbol": "send", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Seraph by <PERSON>s", "symbol": "seraph", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Shark Cat", "symbol": "sc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Silo Finance [OLD]", "symbol": "silo", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Ski Mask Dog", "symbol": "ski", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "SMARDEX", "symbol": "sdex", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Solidus Ai Tech", "symbol": "aitech", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Solv Protocol BTC", "symbol": "solvbtc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Solv Protocol Staked BTC", "symbol": "xsolvbtc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "SoSoValue", "symbol": "soso", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Spectral", "symbol": "spec", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Speculate", "symbol": "spec", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Stargate Finance", "symbol": "stg", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Steakhouse USDC (Base) Morpho Vault", "symbol": "steakusdc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "STP", "symbol": "stpt", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "SQD", "symbol": "sqd", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "anon", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Superbridge Bridged scrvUSD", "symbol": "scrvusd", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Superbridge Bridged wstETH (Base)", "symbol": "wsteth", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Super OETH", "symbol": "superoeth", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "sUSDa", "symbol": "susda", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "sUSDS", "symbol": "susds", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "sushi", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Sweat Economy", "symbol": "sweat", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Synapse", "symbol": "syn", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Syn Dog", "symbol": "syn", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Maple Finance", "symbol": "syrup", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "tBTC", "symbol": "tbtc", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Telcoin", "symbol": "tel", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Own The Doge", "symbol": "dog", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Threshold Network", "symbol": "t", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Token.com", "symbol": "token", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Token S", "symbol": "s", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "toshi", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "Tree", "symbol": "tree", "address": "******************************************", "decimals": 18}, {"chainId": 8453, "name": "/treeplanting", "symbol": "tree", "address": "0x6888c2409d48222e2cb738eb5a805a522a96ce80", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "rei", "address": "0x6b2504a03ca4d43d0d73776f6ad46dab2f2a4cfd", "decimals": 18}, {"chainId": 8453, "name": "USDS", "symbol": "usds", "address": "0x820c137fa70c8691f0e44dc420a5e53c168921dc", "decimals": 18}, {"chainId": 8453, "name": "Usual", "symbol": "usual", "address": "0x4acd4d03af6f9cc0fb7c5f0868b7b6287d7969c5", "decimals": 18}, {"chainId": 8453, "name": "Usual USD", "symbol": "usd0", "address": "0x758a3e0b1f842c9306b783f8a4078c6c8c03a270", "decimals": 18}, {"chainId": 8453, "name": "VaderAI by Virtuals", "symbol": "vader", "address": "0x731814e491571a2e9ee3c5b1f7f3b962ee8f4870", "decimals": 18}, {"chainId": 8453, "name": "Venice Token", "symbol": "vvv", "address": "0xacfe6019ed1a7dc6f7b508c02d1b04ec88cc21bf", "decimals": 18}, {"chainId": 8453, "name": "Venus", "symbol": "xvs", "address": "0xebb7873213c8d1d9913d8ea39aa12d74cb107995", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "symbol": "vcnt", "address": "0xdcf5130274753c8050ab061b1a1dcbf583f5bfd0", "decimals": 18}, {"chainId": 8453, "name": "Warpcore", "symbol": "core", "address": "0xe8e286b378254c4913c0c6964361636384b9d018", "decimals": 18}, {"chainId": 8453, "name": "Wayfinder", "symbol": "prompt", "address": "0x30c7235866872213f68cb1f08c37cb9eccb93452", "decimals": 18}, {"chainId": 8453, "name": "Web 3 Dollar", "symbol": "usd3", "address": "0xefb97aaf77993922ac4be4da8fbc9a2425322677", "decimals": 18}, {"chainId": 8453, "name": "WOO", "symbol": "woo", "address": "0xf3df0a31ec5ea438150987805e841f960b9471b6", "decimals": 18}, {"chainId": 8453, "name": "World Mobile Token", "symbol": "wmtx", "address": "0x3e31966d4f81c72d2a55310a6365a56a4393e98d", "decimals": 18}, {"chainId": 8453, "name": "Wormhole", "symbol": "w", "address": "0xb0ffa8000886e57f86dd5264b9582b2ad87b2b91", "decimals": 18}, {"chainId": 8453, "name": "XION", "symbol": "xion", "address": "0xe4c3461a20f50dad7b9e88ca0222a255c4126fc0", "decimals": 18}, {"chainId": 8453, "name": "yearn.finance", "symbol": "yfi", "address": "0x9eaf8c1e34f05a589eda6bafdf391cf6ad3cb239", "decimals": 18}, {"chainId": 8453, "name": "Yield Guild Games", "symbol": "ygg", "address": "0xaac78d1219c08aecc8e37e03858fe885f5ef1799", "decimals": 18}, {"chainId": 8453, "name": "Zentry", "symbol": "zent", "address": "0xdf49c226ed9cf05be0e38cdb86df4e8a945158b1", "decimals": 18}, {"chainId": 8453, "name": "<PERSON><PERSON>", "symbol": "zora", "address": "0x1111111111166b7fe7bd91427724b487980afc69", "decimals": 18}]