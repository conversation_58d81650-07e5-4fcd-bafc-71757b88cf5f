[{"chainId": 10, "name": "0xBitcoin", "symbol": "0xBTC", "address": "******************************************", "decimals": 8}, {"chainId": 10, "name": "<PERSON><PERSON>", "symbol": "AAVE", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Across Protocol Token", "symbol": "ACX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Aelin Protocol", "symbol": "AELIN", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Alongside Crypto Market Index", "symbol": "AMKT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Aptos", "symbol": "APT", "address": "******************************************", "decimals": 8}, {"chainId": 10, "name": "Balancer", "symbol": "BAL", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Bankless Token", "symbol": "BANK", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "BAXagent Coin", "symbol": "BAXA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "BOB Token", "symbol": "BOB", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "BarnBridge Governance Token", "symbol": "BOND", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "BitANT", "symbol": "BitANT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "BitBTC", "symbol": "BitBTC", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Chi USD", "symbol": "CHI", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Collab.Land", "symbol": "COLLAB", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Curve DAO Token", "symbol": "CRV", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON><PERSON>", "symbol": "CTSI", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Dai Stablecoin", "symbol": "DAI", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Dentacoin", "symbol": "DCN", "address": "******************************************", "decimals": 0}, {"chainId": 10, "name": "dForce", "symbol": "DF", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "dHEDGE DAO Token", "symbol": "DHT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Dola USD Stablecoin", "symbol": "DOLA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Unit protocol", "symbol": "DUCK", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Ethereum Name Service", "symbol": "ENS", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Equalizer", "symbol": "EQZ", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "<PERSON>", "symbol": "EST", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON>", "symbol": "ETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "StaFi <PERSON>", "symbol": "FIS", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Flashstake", "symbol": "FLASH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "<PERSON>", "symbol": "FLy", "address": "******************************************", "decimals": 4}, {"chainId": 10, "name": "ShapeShift FOX", "symbol": "FOX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "FRAX", "symbol": "FRAX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "FXS", "symbol": "FXS", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Giveth Token", "symbol": "GIV", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "R<PERSON>", "symbol": "GRG", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Gitcoin", "symbol": "GTC", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "GMO JPY", "symbol": "GYEN", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "Geyser", "symbol": "GYSR", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "HairDAO Token", "symbol": "HAIR", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON><PERSON><PERSON>", "symbol": "HAN", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "DAOhaus Token on Optimism", "symbol": "HAUS", "address": "0x01B8b6384298D4848E3BE63D4C9D17830EeE488A", "decimals": 18}, {"chainId": 10, "name": "HOP", "symbol": "HOP", "address": "0xc5102fe9359fd9a28f877a67e36b0f050d81a3cc", "decimals": 18}, {"chainId": 10, "name": "Impermax", "symbol": "IBEX", "address": "0xBB6BBaa0F6D839A00c82B10747aBc3b7E2eEcc82", "decimals": 18}, {"chainId": 10, "name": "Kyber Network Crystal v2", "symbol": "KNC", "address": "0xa00E3A3511aAC35cA78530c85007AFCd31753819", "decimals": 18}, {"chainId": 10, "name": "Kromatika", "symbol": "KROM", "address": "0xf98dcd95217e15e05d8638da4c91125e59590b07", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON><PERSON>", "symbol": "KUJI", "address": "0x3A18dcC9745eDcD1Ef33ecB93b0b6eBA5671e7Ca", "decimals": 6}, {"chainId": 10, "name": "Lido DAO Token", "symbol": "LDO", "address": "0xFdb794692724153d1488CcdBE0C56c252596735F", "decimals": 18}, {"chainId": 10, "name": "Chainlink", "symbol": "LINK", "address": "0x350a791bfc2c21f9ed5d10980dad2e2638ffa7f6", "decimals": 18}, {"chainId": 10, "name": "Theranos Coin", "symbol": "LIZ", "address": "0x3bB4445D30AC020a84c1b5A8A2C6248ebC9779D0", "decimals": 18}, {"chainId": 10, "name": "Loopfi", "symbol": "LPF", "address": "0x0B3e851cf6508A16266BC68A651ea68b31ef673b", "decimals": 18}, {"chainId": 10, "name": "LoopringCoin V2", "symbol": "LRC", "address": "0xFEaA9194F9F8c1B65429E31341a103071464907E", "decimals": 18}, {"chainId": 10, "name": "LUSD Stablecoin", "symbol": "LUSD", "address": "0xc40f949f8a4e094d1b49a23ea9241d289b7b2819", "decimals": 18}, {"chainId": 10, "name": "Lyra", "symbol": "LYRA", "address": "0x50c5725949A6F0c72E6C4a641F24049A917DB0Cb", "decimals": 18}, {"chainId": 10, "name": "Mask Network", "symbol": "MASK", "address": "0x3390108E913824B8eaD638444cc52B9aBdF63798", "decimals": 18}, {"chainId": 10, "name": "Metronome2", "symbol": "MET", "address": "0x9a2e53158e12bc09270af10c16a466cb2b5d7836", "decimals": 18}, {"chainId": 10, "name": "Maker", "symbol": "MKR", "address": "0xab7badef82e9fe11f6f33f87bc9bc2aa27f2fcb5", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON>", "symbol": "MOCHI", "address": "0x77D40CBc27f912dcDbF4348cAf87B427c4D02486", "decimals": 18}, {"chainId": 10, "name": "Monetum", "symbol": "MOM", "address": "0x5e70AfFE232e2919792f77EB94e566db0320fa88", "decimals": 18}, {"chainId": 10, "name": "Meta", "symbol": "MTA", "address": "0x929b939f8524c3be977af57a4a0ad3fb1e374b50", "decimals": 18}, {"chainId": 10, "name": "NFTEarth", "symbol": "NFTE", "address": "0xc96f4F893286137aC17e07Ae7F217fFca5db3AB6", "decimals": 18}, {"chainId": 10, "name": "Optimism", "symbol": "OP", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Paper", "symbol": "PAPER", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Perpetual", "symbol": "PERP", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "PhunToken", "symbol": "PHTK", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "PoolTogether", "symbol": "POOL", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Popcorn", "symbol": "POP", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Premia", "symbol": "PREMIA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "ParaSwap", "symbol": "PSP", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Rai Reflex Index", "symbol": "RAI", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Respawn Finance Wrapped Staked Ethereum", "symbol": "RFWSTETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Rari Go<PERSON>ken", "symbol": "RGT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Rocket Pool Protocol", "symbol": "RPL", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Sarcophagus", "symbol": "SARCO", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Saddle DAO", "symbol": "SDL", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Synthetix", "symbol": "SNX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Solana", "symbol": "SOL", "address": "******************************************", "decimals": 9}, {"chainId": 10, "name": "SPANK", "symbol": "SPANK", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "SUKU", "symbol": "SUKU", "address": "0xef6301da234fc7b0545c6e877d3359fe0b9e50a4", "decimals": 18}, {"chainId": 10, "name": "SushiToken", "symbol": "SUSHI", "address": "0x3eaEb77b03dBc0F6321AE1b72b2E9aDb0F60112B", "decimals": 18}, {"chainId": 10, "name": "Optimistic Thales Token", "symbol": "THALES", "address": "0x217D47011b23BB961eB6D93cA9945B7501a5BB11", "decimals": 18}, {"chainId": 10, "name": "Token Name Service", "symbol": "TKN", "address": "0x3Eb398fEc5F7327C6b15099a9681d9568ded2e82", "decimals": 18}, {"chainId": 10, "name": "<PERSON><PERSON>", "symbol": "TRB", "address": "0xaf8ca653fa2772d58f4368b0a71980e9e3ceb888", "decimals": 18}, {"chainId": 10, "name": "TrueUSD", "symbol": "TUSD", "address": "0xcB59a0A753fDB7491d5F3D794316F1adE197B21E", "decimals": 18}, {"chainId": 10, "name": "TheDAO", "symbol": "TheDAO", "address": "0xd8f365c2c85648f9b89d9f1bf72c0ae4b1c36cfd", "decimals": 16}, {"chainId": 10, "name": "Universal Basic Income", "symbol": "UBI", "address": "0xbb586ed34974b15049a876fd5366a4c2d1203115", "decimals": 18}, {"chainId": 10, "name": "UMA Voting Token v1", "symbol": "UMA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Uniswap", "symbol": "UNI", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "USD+", "symbol": "USD+", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "USDCoin", "symbol": "USDC", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "USDCoin (Bridged from Ethereum)", "symbol": "USDC.e", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "Decentralized USD", "symbol": "USDD", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Tether USD", "symbol": "USDT", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "UST (Wormhole)", "symbol": "UST", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "dForce USD", "symbol": "USX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Validator", "symbol": "VALX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Velodrome Finance", "symbol": "VELO", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "VitaDAO Token", "symbol": "VITA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "WardenSwap", "symbol": "WAD", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Wrapped BTC", "symbol": "WBTC", "address": "******************************************", "decimals": 8}, {"chainId": 10, "name": "Wrapped Ether", "symbol": "WETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Wootrade Network", "symbol": "WOO", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "CryptoFranc", "symbol": "XCHF", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "MetalSwap", "symbol": "XMT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "LayerZero", "symbol": "ZRO", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "0x Protocol Token", "symbol": "ZRX", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Z.com USD", "symbol": "ZUSD", "address": "******************************************", "decimals": 6}, {"chainId": 10, "name": "AladdinCRV", "symbol": "aCRV", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Alchemix ETH", "symbol": "alETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Coinbase Wrapped Staked ETH", "symbol": "cbETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Fr<PERSON>", "symbol": "frxETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Rocket Pool ETH", "symbol": "rETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Synthetic Bitcoin", "symbol": "sBTC", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Synthetic Ether", "symbol": "sETH", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Synthetic Chainlink", "symbol": "sLINK", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Synthetix USD", "symbol": "sUSD", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Staked Lyra", "symbol": "stkLYRA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "veKwenta", "symbol": "veKWENTA", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "wTBT(Bridge Token)", "symbol": "wTBT", "address": "******************************************", "decimals": 18}, {"chainId": 10, "name": "Wrapped liquid staked Ether 2.0", "symbol": "wstETH", "address": "******************************************", "decimals": 18}]