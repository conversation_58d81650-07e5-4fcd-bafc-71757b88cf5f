version: 2
configurations:
  $namespace server:
    configurationPath: server.yml
    schemaPath: server-schema.json

  $namespace ethereum:
    configurationPath: ethereum.yml
    schemaPath: ethereum-schema.json

  $namespace uniswap:
    configurationPath: uniswap.yml
    schemaPath: uniswap-schema.json

  $namespace solana:
    configurationPath: solana.yml
    schemaPath: solana-schema.json

  $namespace jupiter:
    configurationPath: jupiter.yml
    schemaPath: jupiter-schema.json

  $namespace meteora:
    configurationPath: meteora.yml
    schemaPath: meteora-schema.json

  $namespace raydium:
    configurationPath: raydium.yml
    schemaPath: raydium-schema.json
