networks:
  mainnet-beta:
    nodeURL: https://api.mainnet-beta.solana.com
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/solana.json
    nativeCurrencySymbol: SOL
  devnet:
    nodeURL: https://api.devnet.solana.com
    tokenListType: FILE
    tokenListSource: /home/<USER>/conf/lists/solana-devnet.json
    nativeCurrencySymbol: SOL

# Default compute units for a transaction used to estimate gasPrice
defaultComputeUnits: 200000

# Floor percentile of recent priority fee samples used to estimate gasPrice for a transaction
basePriorityFeePct: 90

# Multiplier for increasing priority fee on retry
priorityFeeMultiplier: 2

# Maximum priority fee in SOL
maxPriorityFee: 0.01

# Minimum priority fee in SOL
minPriorityFee: 0.0001

# Retry interval in milliseconds
retryIntervalMs: 500

# Number of retry attempts
retryCount: 10
