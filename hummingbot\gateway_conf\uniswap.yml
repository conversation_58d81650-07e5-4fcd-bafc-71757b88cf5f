# Global settings for Uniswap
# How much the execution price is allowed to move unfavorably
allowedSlippage: '2/100'

# For each swap, the maximum number of hops to consider
maximumHops: 4

# Network-specific pool configurations
networks:
  # Base network pools
  base:
    # AMM (Uniswap V2) pools for Base network
    amm:
      WETH-USDC: '******************************************'
      VIRTUAL-WETH: '******************************************'
    
    # CLMM (Uniswap V3) pools for Base network
    clmm:
      # Format: base-quote: pool_address
      WETH-USDC: '******************************************'
      
  # Ethereum Mainnet pools
  mainnet:
    # AMM (Uniswap V2) pools for Mainnet
    amm:
      ETH-USDC: '******************************************'
      ETH-USDT: '******************************************'
      ETH-DAI: '******************************************'
      USDC-USDT: '******************************************'
      WBTC-ETH: '******************************************'
    
    # CLMM (Uniswap V3) pools for Mainnet
    clmm:
      ETH-USDC: '******************************************'
      ETH-USDT: '******************************************'
      ETH-DAI: '******************************************'
      WBTC-ETH: '******************************************'
      
  # Arbitrum pools
  arbitrum:
    # AMM and CLMM pools for Arbitrum
    amm: {}
    clmm: {}
    
  # Optimism pools
  optimism:
    # AMM and CLMM pools for Optimism
    amm: {}
    clmm: {}
    
  # BSC pools
  bsc:
    # AMM and CLMM pools for BSC
    amm: {}
    clmm: {}
    
  # Avalanche pools
  avalanche:
    # AMM and CLMM pools for Avalanche
    amm: {}
    clmm: {}
    
  # Celo pools
  celo:
    # AMM and CLMM pools for Celo
    amm: {}
    clmm: {}
    
  # Polygon pools
  polygon:
    # AMM and CLMM pools for Polygon
    amm: {}
    clmm: {}
    
  # Sepolia testnet pools
  sepolia:
    # AMM and CLMM pools for Sepolia
    amm: {}
    clmm: {}
    
  # Blast pools
  blast:
    # AMM and CLMM pools for Blast
    amm: {}
    clmm: {}
    
  # Zora pools
  zora:
    # AMM and CLMM pools for Zora
    amm: {}
    clmm: {}
    
  # WorldChain pools
  worldchain:
    # AMM and CLMM pools for WorldChain
    amm:
      'WLD-USDC.e': '0x610e319b3a3ab56a0ed5562927d37c233774ba39'
    clmm: {}