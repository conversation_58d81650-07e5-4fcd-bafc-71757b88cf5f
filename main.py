#!/usr/bin/env python3
"""
AI Crypto Trading Agent - Main Entry Point

Author: inkbytefo
Description: Autonomous AI-powered cryptocurrency trading system
"""

import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

from src.core.agent import TradingAgent
from src.config.config_manager import ConfigManager
from src.config.settings import Settings
from src.utils.logger import setup_logger


async def main():
    """Main entry point for the AI Trading Agent."""
    
    # Setup logging
    setup_logger()
    logger = logging.getLogger(__name__)
    
    logger.info("Starting AI Crypto Trading Agent...")
    
    agent = None
    try:
        # Load configuration
        config_manager = ConfigManager()
        settings = config_manager.get_settings()

        # Initialize trading agent
        agent = TradingAgent(settings)
        
        # Start the agent
        await agent.start()
        
        logger.info("AI Trading Agent is running. Press Ctrl+C to stop.")
        
        # Keep running
        while True:
            await asyncio.sleep(1)
            
    except KeyboardInterrupt:
        logger.info("Shutting down gracefully...")
    except Exception as e:
        logger.error(f"Fatal error: {e}")
    finally:
        # Ensure proper cleanup
        if agent:
            try:
                await agent.stop()
                logger.info("Agent stopped successfully")
            except Exception as e:
                logger.error(f"Error stopping agent: {e}")


if __name__ == "__main__":
    asyncio.run(main())