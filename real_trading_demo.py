#!/usr/bin/env python3
"""
Real Trading Demo for AI Trading Agent

This module demonstrates real trading capabilities with proper
error handling and safety measures.

Author: inkbytefo
"""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Any
from src.core.agent import TradingAgent
from src.config.config_manager import ConfigManager, ConfigSection
from src.execution.execution_manager import ExecutionManager, ExecutionRequest, OrderSide
from src.utils.logger import setup_logger


class RealTradingDemo:
    """Real trading demonstration system."""
    
    def __init__(self):
        """Initialize real trading demo."""
        self.logger = logging.getLogger(__name__)
        self.config_manager = ConfigManager()
        self.trading_agent = None
        self.demo_results = []
        
    async def initialize_system(self):
        """Initialize the trading system."""
        try:
            self.logger.info("Initializing AI Trading System...")
            
            # Create and initialize trading agent
            config = self.config_manager.get(ConfigSection.GENERAL)
            self.trading_agent = TradingAgent(config)
            await self.trading_agent.start()
            
            self.logger.info("✅ Trading system initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize system: {e}")
            return False
    
    async def check_system_status(self):
        """Check overall system status."""
        try:
            self.logger.info("Checking system status...")
            
            status = {
                'trading_agent': self.trading_agent is not None,
                'hummingbot_connected': False,
                'data_collector_ready': False,
                'ai_engine_ready': False
            }
            
            if self.trading_agent:
                # Check Hummingbot connection
                if hasattr(self.trading_agent, 'execution_controller'):
                    hummingbot = self.trading_agent.execution_controller
                    status['hummingbot_connected'] = hummingbot.is_running
                
                # Check data collector
                if hasattr(self.trading_agent, 'data_collector'):
                    status['data_collector_ready'] = True
                
                # Check AI engine
                if hasattr(self.trading_agent, 'ai_engine'):
                    status['ai_engine_ready'] = True
            
            self.logger.info(f"System Status: {status}")
            return status
            
        except Exception as e:
            self.logger.error(f"Failed to check system status: {e}")
            return {}
    
    async def test_data_collection(self):
        """Test real-time data collection."""
        try:
            self.logger.info("Testing data collection...")
            
            if not self.trading_agent or not hasattr(self.trading_agent, 'data_collector'):
                self.logger.warning("Data collector not available")
                return False
            
            data_collector = self.trading_agent.data_collector
            
            # Start data collection
            await data_collector.start()
            self.logger.info("✅ Data collection started")
            
            # Wait for some data
            await asyncio.sleep(5)
            
            # Stop data collection
            await data_collector.stop()
            self.logger.info("✅ Data collection stopped")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Data collection test failed: {e}")
            return False
    
    async def simulate_trading_decision(self):
        """Simulate a trading decision process."""
        try:
            self.logger.info("Simulating trading decision...")
            
            # Mock market data
            market_data = {
                'symbol': 'BTC/USDT',
                'price': 45000.0,
                'volume': 1000000,
                'timestamp': datetime.now().isoformat()
            }
            
            # Mock AI analysis
            ai_analysis = {
                'prediction': 'bullish',
                'confidence': 0.75,
                'target_price': 46500.0,
                'stop_loss': 44000.0
            }
            
            # Mock risk assessment
            risk_assessment = {
                'risk_score': 0.3,  # Low risk
                'max_position_size': 0.01,  # 1% of portfolio
                'approved': True
            }
            
            # Generate trading decision
            trading_decision = {
                'action': 'buy',
                'symbol': market_data['symbol'],
                'amount': risk_assessment['max_position_size'],
                'price': market_data['price'],
                'confidence': ai_analysis['confidence'],
                'reasoning': f"AI predicts {ai_analysis['prediction']} with {ai_analysis['confidence']*100}% confidence"
            }
            
            self.logger.info(f"✅ Trading Decision: {trading_decision}")
            return trading_decision
            
        except Exception as e:
            self.logger.error(f"Trading decision simulation failed: {e}")
            return None
    
    async def execute_paper_trade(self, decision: Dict[str, Any]):
        """Execute a paper trade based on decision."""
        try:
            self.logger.info("Executing paper trade...")
            
            if not decision:
                self.logger.error("No trading decision provided")
                return False
            
            # Create paper trade record
            paper_trade = {
                'id': f"paper_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                'symbol': decision['symbol'],
                'side': decision['action'],
                'amount': decision['amount'],
                'price': decision['price'],
                'timestamp': datetime.now().isoformat(),
                'status': 'executed',
                'type': 'paper_trade'
            }
            
            # Calculate trade value
            trade_value = paper_trade['amount'] * paper_trade['price']
            
            self.logger.info(f"✅ Paper Trade Executed:")
            self.logger.info(f"   ID: {paper_trade['id']}")
            self.logger.info(f"   {paper_trade['side'].upper()} {paper_trade['amount']} {paper_trade['symbol']}")
            self.logger.info(f"   Price: ${paper_trade['price']:,.2f}")
            self.logger.info(f"   Value: ${trade_value:,.2f}")
            
            self.demo_results.append(paper_trade)
            return True
            
        except Exception as e:
            self.logger.error(f"Paper trade execution failed: {e}")
            return False
    
    async def test_hummingbot_connection(self):
        """Test Hummingbot connection and capabilities."""
        try:
            self.logger.info("Testing Hummingbot connection...")
            
            if not self.trading_agent or not hasattr(self.trading_agent, 'execution_controller'):
                self.logger.warning("Hummingbot controller not available")
                return False
            
            hummingbot = self.trading_agent.execution_controller
            
            # Check connection status
            if hummingbot.is_running:
                self.logger.info("✅ Hummingbot is connected and running")
                
                # Test getting balances (if available)
                try:
                    balances = await hummingbot.get_balances()
                    self.logger.info(f"✅ Account balances retrieved: {len(balances) if balances else 0} assets")
                except Exception as e:
                    self.logger.warning(f"Could not retrieve balances: {e}")
                
                return True
            else:
                self.logger.warning("🟡 Hummingbot not connected - running in simulation mode")
                return False
                
        except Exception as e:
            self.logger.error(f"Hummingbot connection test failed: {e}")
            return False
    
    async def run_trading_cycle(self):
        """Run a complete trading cycle."""
        try:
            self.logger.info("Running complete trading cycle...")
            
            # 1. Collect market data
            self.logger.info("📊 Step 1: Collecting market data...")
            data_success = await self.test_data_collection()
            
            # 2. Analyze market and make decision
            self.logger.info("🤖 Step 2: Analyzing market and making decision...")
            decision = await self.simulate_trading_decision()
            
            # 3. Execute trade (paper trade for safety)
            self.logger.info("💰 Step 3: Executing trade...")
            trade_success = await self.execute_paper_trade(decision)
            
            # 4. Check Hummingbot status
            self.logger.info("🔗 Step 4: Checking Hummingbot connection...")
            hummingbot_ready = await self.test_hummingbot_connection()
            
            cycle_result = {
                'data_collection': data_success,
                'decision_making': decision is not None,
                'trade_execution': trade_success,
                'hummingbot_ready': hummingbot_ready,
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ Trading cycle completed: {cycle_result}")
            return cycle_result
            
        except Exception as e:
            self.logger.error(f"Trading cycle failed: {e}")
            return None
    
    async def generate_demo_report(self):
        """Generate demonstration report."""
        try:
            self.logger.info("Generating demo report...")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': await self.check_system_status(),
                'demo_trades': self.demo_results,
                'total_trades': len(self.demo_results),
                'recommendations': []
            }
            
            # Add recommendations
            if not report['system_status'].get('hummingbot_connected', False):
                report['recommendations'].append("Configure Hummingbot API for live trading")
            
            if report['total_trades'] > 0:
                report['recommendations'].append("Paper trading successful - ready for live trading with proper risk management")
            
            return report
            
        except Exception as e:
            self.logger.error(f"Failed to generate demo report: {e}")
            return None


async def main():
    """Main demo execution."""
    print("🚀 AI Trading Agent - Real Trading Demo")
    print("=" * 50)
    
    # Setup logging
    setup_logger(level="INFO")
    logger = logging.getLogger(__name__)
    
    try:
        # Initialize demo
        demo = RealTradingDemo()
        
        # Initialize system
        logger.info("Initializing trading system...")
        init_success = await demo.initialize_system()
        
        if not init_success:
            logger.error("Failed to initialize system")
            return
        
        # Check system status
        status = await demo.check_system_status()
        
        # Run trading cycles
        logger.info("Running trading demonstration...")
        
        for cycle in range(3):  # Run 3 trading cycles
            logger.info(f"\n🔄 Trading Cycle {cycle + 1}/3")
            cycle_result = await demo.run_trading_cycle()
            
            if cycle_result:
                logger.info(f"Cycle {cycle + 1} completed successfully")
            else:
                logger.warning(f"Cycle {cycle + 1} had issues")
            
            # Wait between cycles
            if cycle < 2:
                await asyncio.sleep(2)
        
        # Generate final report
        report = await demo.generate_demo_report()
        
        # Display results
        print("\n" + "=" * 50)
        print("📊 Demo Results:")
        print(f"✅ Total paper trades executed: {len(demo.demo_results)}")
        
        for trade in demo.demo_results:
            print(f"   📈 {trade['side'].upper()} {trade['amount']} {trade['symbol']} @ ${trade['price']:,.2f}")
        
        if status.get('hummingbot_connected', False):
            print("\n🟢 System ready for LIVE TRADING!")
            print("⚠️  Remember to:")
            print("   - Set appropriate position sizes")
            print("   - Configure stop losses")
            print("   - Monitor trades closely")
        else:
            print("\n🟡 System ready for PAPER TRADING")
            print("⚠️  To enable live trading:")
            print("   - Configure Hummingbot API connection")
            print("   - Set up exchange API keys")
            print("   - Test with small amounts first")
        
        print("\n🎯 Demo completed successfully!")
        
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        print(f"❌ Demo failed: {e}")


if __name__ == "__main__":
    asyncio.run(main())