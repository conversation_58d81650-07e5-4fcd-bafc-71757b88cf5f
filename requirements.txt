# Core dependencies
numpy==1.24.3
aiohttp==3.9.1
aiohttp[speedups]==3.9.1
aiofiles==23.2.1
fastapi==0.104.1
uvicorn==0.24.0
pydantic==2.5.0
pydantic-settings==2.1.0
flask==3.0.0
streamlit==1.28.1

# Data processing
pandas==2.1.4
scipy==1.11.4
scikit-learn==1.3.2
joblib==1.3.2

# AI/ML libraries
transformers==4.36.0
torch==2.1.1
sentence-transformers==2.2.2
vaderSentiment==3.3.2
textblob==0.17.1

# Technical analysis
# TA-Lib==0.4.28  # Commented out due to Windows installation issues
pandas-ta==0.3.14b0
yfinance==0.2.18
ccxt==4.1.64

# Database
psycopg2-binary==2.9.9
redis==5.0.1
sqlalchemy==2.0.23
alembic==1.13.1

# Web scraping
beautifulsoup4==4.12.2
requests==2.31.0
selenium==4.15.2
feedparser==6.0.10

# News APIs
newsapi-python==0.2.7
praw==7.7.1
tweepy==4.14.0

# Utilities
python-dotenv==1.0.0
PyYAML==6.0.1
loguru==0.7.2
schedule==1.2.0
websockets==12.0
cryptography==41.0.7
bcrypt==4.1.2

# Hummingbot API Client
# hummingbot-api-client==1.0.0

# Hummingbot integration (using latest available version)
# hummingbot==20250715

# Monitoring
prometheus-client==0.19.0
grafana-api==1.0.3

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0