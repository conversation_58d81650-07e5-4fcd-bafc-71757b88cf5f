#!/usr/bin/env python3
"""
Hummingbot Setup Script for AI Trading Agent

This script helps set up Hummingbot Gateway and Client for integration
with the AI Trading Agent.

Author: inkbytefo
"""

import os
import sys
import asyncio
import logging
import subprocess
from pathlib import Path
from typing import Dict, Any

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from src.utils.logger import setup_logger
from src.config.hummingbot_config import HummingbotConfigManager


class HummingbotSetup:
    """Hummingbot setup and configuration manager."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.project_root = Path(__file__).parent.parent
        self.config_manager = HummingbotConfigManager()
        
    async def setup_environment(self):
        """Set up Hummingbot environment."""
        self.logger.info("Setting up Hummingbot environment...")
        
        try:
            # Create necessary directories
            await self._create_directories()
            
            # Generate SSL certificates
            await self._generate_ssl_certificates()
            
            # Create configuration files
            await self._create_config_files()
            
            # Set up Docker environment
            await self._setup_docker_environment()
            
            self.logger.info("Hummingbot environment setup completed successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to set up Hummingbot environment: {e}")
            raise
    
    async def _create_directories(self):
        """Create necessary directories for Hummingbot."""
        directories = [
            'hummingbot_conf',
            'hummingbot_logs', 
            'hummingbot_data',
            'hummingbot_gateway_conf',
            'hummingbot_gateway_certs',
            'hummingbot_gateway_logs',
            'certs',
            'logs/hummingbot'
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            self.logger.info(f"Created directory: {dir_path}")
    
    async def _generate_ssl_certificates(self):
        """Generate SSL certificates for Hummingbot Gateway."""
        self.logger.info("Generating SSL certificates...")
        
        certs_dir = self.project_root / 'certs'
        gateway_certs_dir = self.project_root / 'hummingbot_gateway_certs'
        
        # Generate self-signed certificate for Gateway
        cert_commands = [
            [
                'openssl', 'req', '-x509', '-newkey', 'rsa:4096',
                '-keyout', str(certs_dir / 'gateway.key'),
                '-out', str(certs_dir / 'gateway.crt'),
                '-days', '365', '-nodes',
                '-subj', '/C=US/ST=State/L=City/O=Organization/CN=localhost'
            ],
            [
                'openssl', 'req', '-x509', '-newkey', 'rsa:4096',
                '-keyout', str(gateway_certs_dir / 'server_key.pem'),
                '-out', str(gateway_certs_dir / 'server_cert.pem'),
                '-days', '365', '-nodes',
                '-subj', '/C=US/ST=State/L=City/O=Organization/CN=localhost'
            ]
        ]
        
        for cmd in cert_commands:
            try:
                result = subprocess.run(cmd, capture_output=True, text=True, check=True)
                self.logger.info(f"Certificate generated successfully")
            except subprocess.CalledProcessError as e:
                self.logger.warning(f"Failed to generate certificate with openssl: {e}")
                self.logger.info("Please generate SSL certificates manually or install openssl")
    
    async def _create_config_files(self):
        """Create Hummingbot configuration files."""
        self.logger.info("Creating configuration files...")
        
        # Gateway configuration
        gateway_config = {
            "server": {
                "port": 15888,
                "id": "gateway-api"
            },
            "logging": {
                "logLevel": "info"
            },
            "database": {
                "type": "sqlite",
                "path": "./db/gateway.db"
            }
        }
        
        gateway_config_path = self.project_root / 'hummingbot_gateway_conf' / 'gateway.yml'
        with open(gateway_config_path, 'w') as f:
            import yaml
            yaml.dump(gateway_config, f, default_flow_style=False)
        
        # Client configuration template
        client_config = {
            "template_version": 1,
            "strategy": "pure_market_making",
            "exchange": "binance_paper_trade",
            "market": "BTC-USDT",
            "bid_spread": 0.1,
            "ask_spread": 0.1,
            "order_amount": 0.001,
            "price_ceiling": -1,
            "price_floor": -1,
            "ping_pong_enabled": False,
            "order_levels": 1,
            "order_level_amount": 0,
            "order_level_spread": 1,
            "inventory_skew_enabled": False,
            "inventory_target_base_pct": 50,
            "inventory_range_multiplier": 1,
            "filled_order_delay": 60,
            "order_refresh_time": 30,
            "order_refresh_tolerance_pct": 0,
            "max_order_age": 1800,
            "order_optimization_enabled": False,
            "ask_order_optimization_depth": 0,
            "bid_order_optimization_depth": 0,
            "add_transaction_costs": False,
            "price_source": "current_market",
            "price_type": "mid_price",
            "price_source_exchange": "binance",
            "price_source_market": "BTC-USDT",
            "price_source_custom_api": "",
            "custom_api_update_interval": 5
        }
        
        client_config_path = self.project_root / 'hummingbot_conf' / 'pure_market_making.yml'
        with open(client_config_path, 'w') as f:
            import yaml
            yaml.dump(client_config, f, default_flow_style=False)
    
    async def _setup_docker_environment(self):
        """Set up Docker environment for Hummingbot."""
        self.logger.info("Setting up Docker environment...")
        
        # Check if Docker is available
        try:
            result = subprocess.run(['docker', '--version'], capture_output=True, text=True, check=True)
            self.logger.info(f"Docker found: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("Docker not found. Please install Docker to use containerized Hummingbot.")
            return
        
        # Check if Docker Compose is available
        try:
            result = subprocess.run(['docker-compose', '--version'], capture_output=True, text=True, check=True)
            self.logger.info(f"Docker Compose found: {result.stdout.strip()}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            self.logger.warning("Docker Compose not found. Please install Docker Compose.")
            return
        
        self.logger.info("Docker environment is ready")
    
    async def start_hummingbot_services(self):
        """Start Hummingbot Gateway and Client services."""
        self.logger.info("Starting Hummingbot services...")
        
        try:
            # Start services using Docker Compose
            cmd = ['docker-compose', '-f', 'docker-compose.hummingbot.yml', 'up', '-d']
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True, check=True)
            
            self.logger.info("Hummingbot services started successfully")
            self.logger.info("Gateway API available at: https://localhost:15888")
            self.logger.info("Use 'docker-compose -f docker-compose.hummingbot.yml logs -f' to view logs")
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to start Hummingbot services: {e}")
            self.logger.error(f"Error output: {e.stderr}")
            raise
    
    async def stop_hummingbot_services(self):
        """Stop Hummingbot Gateway and Client services."""
        self.logger.info("Stopping Hummingbot services...")
        
        try:
            cmd = ['docker-compose', '-f', 'docker-compose.hummingbot.yml', 'down']
            result = subprocess.run(cmd, cwd=self.project_root, capture_output=True, text=True, check=True)
            
            self.logger.info("Hummingbot services stopped successfully")
            
        except subprocess.CalledProcessError as e:
            self.logger.error(f"Failed to stop Hummingbot services: {e}")
            raise
    
    async def test_connection(self):
        """Test connection to Hummingbot Gateway."""
        self.logger.info("Testing Hummingbot Gateway connection...")
        
        try:
            import aiohttp
            import ssl
            
            # Create SSL context that ignores certificate verification for self-signed certs
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            
            connector = aiohttp.TCPConnector(ssl=ssl_context)
            
            async with aiohttp.ClientSession(connector=connector) as session:
                async with session.get('https://localhost:15888/') as response:
                    if response.status == 200:
                        self.logger.info("✅ Hummingbot Gateway connection successful")
                        return True
                    else:
                        self.logger.warning(f"Gateway responded with status: {response.status}")
                        return False
                        
        except Exception as e:
            self.logger.error(f"Failed to connect to Hummingbot Gateway: {e}")
            return False


async def main():
    """Main setup function."""
    print("🚀 Hummingbot Setup for AI Trading Agent")
    print("=" * 50)
    
    # Setup logging
    setup_logger(level="INFO")
    logger = logging.getLogger(__name__)
    
    setup = HummingbotSetup()
    
    try:
        # Setup environment
        await setup.setup_environment()
        
        # Ask user if they want to start services
        start_services = input("\nDo you want to start Hummingbot services now? (y/n): ").lower().strip()
        
        if start_services == 'y':
            await setup.start_hummingbot_services()
            
            # Wait a bit for services to start
            await asyncio.sleep(10)
            
            # Test connection
            await setup.test_connection()
        
        print("\n✅ Hummingbot setup completed successfully!")
        print("\nNext steps:")
        print("1. Update .env.hummingbot with your actual API credentials")
        print("2. Configure your trading strategies in hummingbot_conf/")
        print("3. Start the AI Trading Agent with Hummingbot integration enabled")
        
    except Exception as e:
        logger.error(f"Setup failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())