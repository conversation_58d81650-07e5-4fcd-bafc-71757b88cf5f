#!/usr/bin/env python3
"""Docker Compose Startup Script.

This script manages the Docker Compose environment for the AI Trading System
with Hummingbot integration.

Author: inkbytefo
"""

import os
import sys
import subprocess
import time
import argparse
import logging
from pathlib import Path
from typing import List, Dict, Optional

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class DockerComposeManager:
    """Manager for Docker Compose operations."""
    
    def __init__(self, project_root: Path):
        self.project_root = project_root
        self.compose_file = project_root / "docker-compose.yml"
        self.env_file = project_root / ".env"
        
        # Service groups for selective startup
        self.service_groups = {
            "core": ["postgres", "redis"],
            "ai_system": ["ai-trading-system"],
            "hummingbot": [
                "hummingbot-postgres", 
                "hummingbot-broker", 
                "hummingbot-api",
                "hummingbot-gateway"
            ],
            "monitoring": ["hummingbot-dashboard"],
            "all": []
        }
    
    def check_prerequisites(self) -> bool:
        """Check if Docker and Docker Compose are installed.
        
        Returns:
            True if prerequisites are met
        """
        try:
            # Check Docker
            result = subprocess.run(
                ["docker", "--version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            logger.info(f"Docker version: {result.stdout.strip()}")
            
            # Check Docker Compose
            result = subprocess.run(
                ["docker", "compose", "version"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            logger.info(f"Docker Compose version: {result.stdout.strip()}")
            
            # Check if Docker daemon is running
            result = subprocess.run(
                ["docker", "info"], 
                capture_output=True, 
                text=True, 
                check=True
            )
            
            return True
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Docker prerequisite check failed: {e}")
            return False
        except FileNotFoundError:
            logger.error("Docker is not installed or not in PATH")
            return False
    
    def create_required_directories(self):
        """Create required directories for volumes."""
        directories = [
            "data/postgres",
            "data/redis",
            "data/hummingbot-postgres",
            "logs",
            "hummingbot/conf",
            "hummingbot/logs",
            "hummingbot/data",
            "hummingbot/certs",
            "models",
            "config"
        ]
        
        for directory in directories:
            dir_path = self.project_root / directory
            dir_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"Created directory: {dir_path}")
    
    def setup_environment(self):
        """Setup environment variables."""
        if not self.env_file.exists():
            logger.info("Creating .env file with default values")
            
            env_content = """
# AI Trading System Configuration
FLASK_ENV=production
FLASK_DEBUG=false
SECRET_KEY=your-secret-key-change-this
JWT_SECRET_KEY=your-jwt-secret-change-this

# Database Configuration
POSTGRES_DB=trading_db
POSTGRES_USER=trading_user
POSTGRES_PASSWORD=trading_password
DATABASE_URL=********************************************************/trading_db

# Redis Configuration
REDIS_URL=redis://redis:6379/0

# Hummingbot Configuration
HUMMINGBOT_USERNAME=admin
HUMMINGBOT_PASSWORD=admin
HUMMINGBOT_CONFIG_PASSWORD=hummingbot123

# Hummingbot Database
HUMMINGBOT_DB_HOST=hummingbot-postgres
HUMMINGBOT_DB_PORT=5432
HUMMINGBOT_DB_NAME=hummingbot_api
HUMMINGBOT_DB_USER=hbot
HUMMINGBOT_DB_PASSWORD=hummingbot-api

# MQTT Broker
BROKER_HOST=hummingbot-broker
BROKER_PORT=1883

# API Configuration
API_HOST=0.0.0.0
API_PORT=5000
DASHBOARD_PORT=8080

# Logging
LOG_LEVEL=INFO

# Security
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REQUESTS_PER_MINUTE=100
"""
            
            with open(self.env_file, 'w') as f:
                f.write(env_content.strip())
            
            logger.info(f"Created .env file at {self.env_file}")
        else:
            logger.info(f"Using existing .env file at {self.env_file}")
    
    def run_command(self, command: List[str], check: bool = True) -> subprocess.CompletedProcess:
        """Run a command and log the output.
        
        Args:
            command: Command to run
            check: Whether to raise exception on non-zero exit code
            
        Returns:
            Completed process
        """
        logger.info(f"Running command: {' '.join(command)}")
        
        try:
            result = subprocess.run(
                command,
                cwd=self.project_root,
                capture_output=True,
                text=True,
                check=check
            )
            
            if result.stdout:
                logger.info(f"Command output: {result.stdout.strip()}")
            
            if result.stderr and result.returncode != 0:
                logger.error(f"Command error: {result.stderr.strip()}")
            
            return result
            
        except subprocess.CalledProcessError as e:
            logger.error(f"Command failed with exit code {e.returncode}")
            if e.stdout:
                logger.error(f"Stdout: {e.stdout}")
            if e.stderr:
                logger.error(f"Stderr: {e.stderr}")
            raise
    
    def pull_images(self):
        """Pull Docker images."""
        logger.info("Pulling Docker images...")
        self.run_command(["docker", "compose", "pull"])
    
    def build_images(self, no_cache: bool = False):
        """Build Docker images.
        
        Args:
            no_cache: Whether to build without cache
        """
        logger.info("Building Docker images...")
        
        command = ["docker", "compose", "build"]
        if no_cache:
            command.append("--no-cache")
        
        self.run_command(command)
    
    def start_services(self, services: List[str] = None, detached: bool = True):
        """Start Docker Compose services.
        
        Args:
            services: List of services to start (None for all)
            detached: Whether to run in detached mode
        """
        command = ["docker", "compose", "up"]
        
        if detached:
            command.append("-d")
        
        if services:
            command.extend(services)
            logger.info(f"Starting services: {', '.join(services)}")
        else:
            logger.info("Starting all services")
        
        self.run_command(command)
    
    def stop_services(self, services: List[str] = None):
        """Stop Docker Compose services.
        
        Args:
            services: List of services to stop (None for all)
        """
        command = ["docker", "compose", "stop"]
        
        if services:
            command.extend(services)
            logger.info(f"Stopping services: {', '.join(services)}")
        else:
            logger.info("Stopping all services")
        
        self.run_command(command)
    
    def restart_services(self, services: List[str] = None):
        """Restart Docker Compose services.
        
        Args:
            services: List of services to restart (None for all)
        """
        command = ["docker", "compose", "restart"]
        
        if services:
            command.extend(services)
            logger.info(f"Restarting services: {', '.join(services)}")
        else:
            logger.info("Restarting all services")
        
        self.run_command(command)
    
    def show_logs(self, services: List[str] = None, follow: bool = False, tail: int = 100):
        """Show Docker Compose logs.
        
        Args:
            services: List of services to show logs for (None for all)
            follow: Whether to follow logs
            tail: Number of lines to show from the end
        """
        command = ["docker", "compose", "logs"]
        
        if follow:
            command.append("-f")
        
        command.extend(["--tail", str(tail)])
        
        if services:
            command.extend(services)
        
        logger.info(f"Showing logs for services: {services or 'all'}")
        
        # For logs, we want to show output in real-time
        subprocess.run(command, cwd=self.project_root)
    
    def show_status(self):
        """Show status of all services."""
        logger.info("Showing service status...")
        self.run_command(["docker", "compose", "ps"])
    
    def cleanup(self, volumes: bool = False):
        """Clean up Docker Compose resources.
        
        Args:
            volumes: Whether to remove volumes as well
        """
        logger.info("Cleaning up Docker Compose resources...")
        
        command = ["docker", "compose", "down"]
        if volumes:
            command.append("-v")
            logger.warning("This will remove all volumes and data!")
        
        self.run_command(command)
    
    def wait_for_services(self, services: List[str], timeout: int = 300):
        """Wait for services to be healthy.
        
        Args:
            services: List of services to wait for
            timeout: Timeout in seconds
        """
        logger.info(f"Waiting for services to be ready: {', '.join(services)}")
        
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                result = self.run_command(
                    ["docker", "compose", "ps", "--format", "json"],
                    check=False
                )
                
                if result.returncode == 0:
                    # Parse service status
                    import json
                    service_statuses = []
                    for line in result.stdout.strip().split('\n'):
                        if line:
                            service_statuses.append(json.loads(line))
                    
                    # Check if all required services are running
                    running_services = {
                        status['Service']: status['State'] 
                        for status in service_statuses
                    }
                    
                    all_ready = True
                    for service in services:
                        if service not in running_services:
                            logger.info(f"Service {service} not found")
                            all_ready = False
                        elif running_services[service] != 'running':
                            logger.info(f"Service {service} state: {running_services[service]}")
                            all_ready = False
                    
                    if all_ready:
                        logger.info("All services are ready!")
                        return True
                
                time.sleep(5)
                
            except Exception as e:
                logger.warning(f"Error checking service status: {e}")
                time.sleep(5)
        
        logger.error(f"Timeout waiting for services after {timeout} seconds")
        return False


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Docker Compose Manager for AI Trading System")
    parser.add_argument(
        "action", 
        choices=["start", "stop", "restart", "logs", "status", "cleanup", "build", "pull"],
        help="Action to perform"
    )
    parser.add_argument(
        "--services",
        nargs="*",
        help="Specific services to target"
    )
    parser.add_argument(
        "--group",
        choices=["core", "ai_system", "hummingbot", "monitoring", "all"],
        help="Service group to target"
    )
    parser.add_argument(
        "--no-cache",
        action="store_true",
        help="Build without cache"
    )
    parser.add_argument(
        "--volumes",
        action="store_true",
        help="Include volumes in cleanup"
    )
    parser.add_argument(
        "--follow",
        action="store_true",
        help="Follow logs"
    )
    parser.add_argument(
        "--tail",
        type=int,
        default=100,
        help="Number of log lines to show"
    )
    
    args = parser.parse_args()
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Create manager
    manager = DockerComposeManager(project_root)
    
    # Check prerequisites
    if not manager.check_prerequisites():
        logger.error("Prerequisites not met. Please install Docker and Docker Compose.")
        sys.exit(1)
    
    # Determine target services
    target_services = args.services
    if args.group and args.group in manager.service_groups:
        target_services = manager.service_groups[args.group]
    
    try:
        if args.action == "start":
            # Setup environment and directories
            manager.setup_environment()
            manager.create_required_directories()
            
            # Start services
            manager.start_services(target_services)
            
            # Wait for core services if starting all or core
            if not target_services or args.group in ["all", "core"]:
                core_services = ["postgres", "redis"]
                manager.wait_for_services(core_services)
            
            logger.info("Services started successfully!")
            
        elif args.action == "stop":
            manager.stop_services(target_services)
            
        elif args.action == "restart":
            manager.restart_services(target_services)
            
        elif args.action == "logs":
            manager.show_logs(target_services, args.follow, args.tail)
            
        elif args.action == "status":
            manager.show_status()
            
        elif args.action == "cleanup":
            manager.cleanup(args.volumes)
            
        elif args.action == "build":
            manager.build_images(args.no_cache)
            
        elif args.action == "pull":
            manager.pull_images()
    
    except KeyboardInterrupt:
        logger.info("Operation interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Operation failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()