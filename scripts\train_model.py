#!/usr/bin/env python3
"""
Offline Model Training Script - Train ML models for cryptocurrency trading

Author: inkbytefo
Description: Offline training script for machine learning models used in the trading agent.
             This script should be run on a dedicated training machine with sufficient resources.
"""

import os
import sys
import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple
import joblib
import warnings
warnings.filterwarnings('ignore')

# ML libraries
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import pandas_ta as ta

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('model_training.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

class ModelTrainer:
    """Offline model trainer for cryptocurrency prediction models."""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or {}
        self.logger = logger
        
        # Model configuration
        self.prediction_horizons = [5, 15, 30, 60]  # minutes
        self.min_training_samples = 1000
        
        # Models for different prediction types
        self.models = {
            'price': {
                'short': RandomForestRegressor(n_estimators=100, random_state=42, n_jobs=-1),
                'medium': GradientBoostingRegressor(n_estimators=100, random_state=42),
                'long': LinearRegression(n_jobs=-1)
            },
            'direction': {
                'short': RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1),
                'medium': GradientBoostingRegressor(n_estimators=50, random_state=42)
            },
            'volatility': {
                'short': RandomForestRegressor(n_estimators=50, random_state=42, n_jobs=-1)
            }
        }
        
        # Scalers for feature normalization
        self.scalers = {
            'features': StandardScaler(),
            'price': MinMaxScaler(),
            'volatility': StandardScaler()
        }
        
        # Model performance tracking
        self.model_performance = {}
        
        # Feature importance tracking
        self.feature_importance = {}
    
    def save_models(self, models_dir: str = 'models'):
        """Save trained models, scalers, and metadata with versioning."""
        try:
            self.logger.info("Saving models and scalers...")
            
            # Create a versioned directory using a timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            versioned_dir = os.path.join(models_dir, timestamp)
            os.makedirs(versioned_dir, exist_ok=True)
            
            # Save models and scalers
            for model_type, model_group in self.models.items():
                for model_name, model in model_group.items():
                    model_path = os.path.join(versioned_dir, f'{model_type}_{model_name}_model.joblib')
                    joblib.dump(model, model_path)
            
            for scaler_name, scaler in self.scalers.items():
                scaler_path = os.path.join(versioned_dir, f'{scaler_name}_scaler.joblib')
                joblib.dump(scaler, scaler_path)
            
            # Save performance and feature importance metadata
            metadata = {
                'performance': self.model_performance,
                'feature_importance': self.feature_importance
            }
            metadata_path = os.path.join(versioned_dir, 'metadata.json')
            with open(metadata_path, 'w') as f:
                json.dump(metadata, f, indent=4)
            
            self.logger.info(f"Models, scalers, and metadata saved to {versioned_dir}")

            # Update the 'latest' symlink to point to the new versioned directory
            latest_symlink_path = os.path.join(models_dir, 'latest')
            if os.path.lexists(latest_symlink_path):
                os.unlink(latest_symlink_path)
            
            # Create symlink (relative path for portability)
            relative_versioned_dir = os.path.relpath(versioned_dir, models_dir)
            os.symlink(relative_versioned_dir, latest_symlink_path)
            
            self.logger.info(f"'latest' symlink updated to point to {versioned_dir}")

        except Exception as e:
            self.logger.error(f"Error saving models: {e}")
            raise

    def load_historical_data(self, data_path: str = None) -> pd.DataFrame:
        """Load historical market data for training."""
        try:
            if data_path and os.path.exists(data_path):
                self.logger.info(f"Loading data from {data_path}")
                if data_path.endswith('.parquet'):
                    df = pd.read_parquet(data_path)
                elif data_path.endswith('.csv'):
                    df = pd.read_csv(data_path)
                else:
                    raise ValueError("Unsupported file format. Use .parquet or .csv")
            else:
                # Generate synthetic data for demonstration
                self.logger.warning("No data file provided. Generating synthetic data for demonstration.")
                df = self._generate_synthetic_data()
            
            self.logger.info(f"Loaded {len(df)} data points")
            return df
            
        except Exception as e:
            self.logger.error(f"Error loading data: {e}")
            raise
    
    def _generate_synthetic_data(self, n_samples: int = 10000) -> pd.DataFrame:
        """Generate synthetic market data for demonstration purposes."""
        self.logger.info(f"Generating {n_samples} synthetic data points")
        
        # Generate time series
        start_date = datetime.now() - timedelta(days=365)
        dates = pd.date_range(start=start_date, periods=n_samples, freq='1min')
        
        # Generate price data with trend and noise
        np.random.seed(42)
        price_trend = np.cumsum(np.random.randn(n_samples) * 0.001) + 50000
        price_noise = np.random.randn(n_samples) * 100
        prices = price_trend + price_noise
        
        # Generate volume data
        volumes = np.random.exponential(1000, n_samples)
        
        # Create DataFrame
        df = pd.DataFrame({
            'timestamp': dates,
            'symbol': 'BTC/USDT',
            'open': prices,
            'high': prices * (1 + np.random.uniform(0, 0.02, n_samples)),
            'low': prices * (1 - np.random.uniform(0, 0.02, n_samples)),
            'close': prices,
            'volume': volumes
        })
        
        return df
    
    def prepare_features(self, df: pd.DataFrame) -> Tuple[np.ndarray, Dict[str, np.ndarray]]:
        """Prepare features and targets for training."""
        self.logger.info("Preparing features and targets...")
        
        try:
            # Calculate technical indicators
            df = df.copy()
            
            # Price-based indicators
            df['sma_20'] = ta.sma(df['close'], length=20)
            df['sma_50'] = ta.sma(df['close'], length=50)
            df['ema_12'] = ta.ema(df['close'], length=12)
            df['ema_26'] = ta.ema(df['close'], length=26)
            
            # Momentum indicators
            df['rsi'] = ta.rsi(df['close'], length=14)
            macd = ta.macd(df['close'])
            df['macd'] = macd['MACD_12_26_9']
            df['macd_signal'] = macd['MACDs_12_26_9']
            df['macd_histogram'] = macd['MACDh_12_26_9']
            
            # Volatility indicators
            bbands = ta.bbands(df['close'], length=20)
            df['bb_upper'] = bbands['BBU_20_2.0']
            df['bb_middle'] = bbands['BBM_20_2.0']
            df['bb_lower'] = bbands['BBL_20_2.0']
            df['bb_width'] = (df['bb_upper'] - df['bb_lower']) / df['bb_middle']
            
            # Volume indicators
            df['volume_sma'] = ta.sma(df['volume'], length=20)
            df['volume_ratio'] = df['volume'] / df['volume_sma']
            
            # Price change features
            df['price_change'] = df['close'].pct_change()
            df['price_change_5'] = df['close'].pct_change(5)
            df['price_change_15'] = df['close'].pct_change(15)
            df['price_change_30'] = df['close'].pct_change(30)
            
            # Volatility features
            df['volatility_5'] = df['price_change'].rolling(5).std()
            df['volatility_15'] = df['price_change'].rolling(15).std()
            df['volatility_30'] = df['price_change'].rolling(30).std()
            
            # Drop rows with NaN values
            df = df.dropna()
            
            if len(df) < self.min_training_samples:
                raise ValueError(f"Insufficient data after feature engineering: {len(df)} samples")
            
            # Prepare feature matrix
            feature_columns = [
                'sma_20', 'sma_50', 'ema_12', 'ema_26', 'rsi', 'macd', 'macd_signal', 'macd_histogram',
                'bb_upper', 'bb_middle', 'bb_lower', 'bb_width', 'volume_ratio',
                'price_change', 'price_change_5', 'price_change_15', 'price_change_30',
                'volatility_5', 'volatility_15', 'volatility_30'
            ]
            
            X = df[feature_columns].values
            
            # Prepare targets for different prediction horizons
            targets = {}
            
            # Price targets
            price_targets = []
            direction_targets = []
            volatility_targets = []
            
            for i in range(len(df) - max(self.prediction_horizons)):
                # Future price (average of different horizons)
                future_prices = []
                future_directions = []
                future_volatilities = []
                
                for horizon in self.prediction_horizons:
                    if i + horizon < len(df):
                        future_price = df.iloc[i + horizon]['close']
                        current_price = df.iloc[i]['close']
                        
                        future_prices.append(future_price)
                        future_directions.append(1 if future_price > current_price else 0)
                        
                        # Calculate volatility over the horizon
                        price_series = df.iloc[i:i+horizon]['close']
                        volatility = price_series.pct_change().std()
                        future_volatilities.append(volatility)
                
                if future_prices:
                    price_targets.append(np.mean(future_prices))
                    direction_targets.append(np.mean(future_directions))
                    volatility_targets.append(np.mean(future_volatilities))
            
            # Trim features to match target length
            X = X[:len(price_targets)]
            
            targets = {
                'price': np.array(price_targets),
                'direction': np.array(direction_targets),
                'volatility': np.array(volatility_targets)
            }
            
            self.logger.info(f"Prepared {X.shape[0]} samples with {X.shape[1]} features")
            return X, targets
            
        except Exception as e:
            self.logger.error(f"Error preparing features: {e}")
            raise
    
    def train_models(self, X: np.ndarray, targets: Dict[str, np.ndarray]):
        """Train all models with the prepared data."""
        self.logger.info("Starting model training...")
        
        try:
            # Fit feature scaler
            self.scalers['features'].fit(X)
            X_scaled = self.scalers['features'].transform(X)
            
            # Train models for each prediction type
            for pred_type, y in targets.items():
                self.logger.info(f"Training {pred_type} models...")
                
                # Fit target scaler if needed
                if pred_type in ['price', 'volatility']:
                    self.scalers[pred_type].fit(y.reshape(-1, 1))
                    y_scaled = self.scalers[pred_type].transform(y.reshape(-1, 1)).flatten()
                else:
                    y_scaled = y
                
                # Train models for different timeframes
                for timeframe, model in self.models[pred_type].items():
                    self.logger.info(f"Training {pred_type}_{timeframe} model...")
                    
                    try:
                        # Split data
                        X_train, X_test, y_train, y_test = train_test_split(
                            X_scaled, y_scaled, test_size=0.2, random_state=42
                        )
                        
                        # Train model
                        model.fit(X_train, y_train)
                        
                        # Evaluate model
                        y_pred = model.predict(X_test)
                        
                        # Calculate metrics
                        mse = mean_squared_error(y_test, y_pred)
                        mae = mean_absolute_error(y_test, y_pred)
                        r2 = r2_score(y_test, y_pred)
                        
                        # Store performance
                        model_key = f"{pred_type}_{timeframe}"
                        self.model_performance[model_key] = {
                            'mse': mse,
                            'mae': mae,
                            'r2': r2,
                            'training_samples': len(X_train),
                            'test_samples': len(X_test)
                        }
                        
                        # Store feature importance if available
                        if hasattr(model, 'feature_importances_'):
                            self.feature_importance[model_key] = model.feature_importances_
                        
                        self.logger.info(f"✓ {model_key} - R²: {r2:.4f}, MAE: {mae:.4f}, MSE: {mse:.4f}")
                        
                    except Exception as e:
                        self.logger.error(f"Error training {pred_type}_{timeframe} model: {e}")
                        raise
            
            self.logger.info("Model training completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error during model training: {e}")
            raise
    
    def evaluate_models(self):
        """Print detailed model evaluation results."""
        self.logger.info("\n" + "="*60)
        self.logger.info("MODEL EVALUATION RESULTS")
        self.logger.info("="*60)
        
        for model_key, performance in self.model_performance.items():
            self.logger.info(f"\n{model_key.upper()}:")
            self.logger.info(f"  R² Score: {performance['r2']:.4f}")
            self.logger.info(f"  MAE: {performance['mae']:.4f}")
            self.logger.info(f"  MSE: {performance['mse']:.4f}")
            self.logger.info(f"  Training Samples: {performance['training_samples']}")
            self.logger.info(f"  Test Samples: {performance['test_samples']}")
            
            if model_key in self.feature_importance:
                top_features = np.argsort(self.feature_importance[model_key])[-5:]
                self.logger.info(f"  Top 5 Features: {top_features}")

def main():
    """Main training function."""
    logger.info("Starting offline model training...")
    logger.info("Author: inkbytefo")
    logger.info("="*60)
    
    try:
        # Initialize trainer
        trainer = ModelTrainer()
        
        # Load historical data
        # You can specify a data file path here:
        # data_path = "path/to/your/historical_data.parquet"
        data_path = None  # Will use synthetic data
        
        df = trainer.load_historical_data(data_path)
        
        # Prepare features and targets
        X, targets = trainer.prepare_features(df)
        
        # Train models
        trainer.train_models(X, targets)
        
        # Evaluate models
        trainer.evaluate_models()
        
        # Save models
        trainer.save_models()
        
        logger.info("\n" + "="*60)
        logger.info("TRAINING COMPLETED SUCCESSFULLY!")
        logger.info("="*60)
        logger.info("Models are ready for deployment to the live trading agent.")
        logger.info("Copy the model files from the 'models/' directory to your production environment.")
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()