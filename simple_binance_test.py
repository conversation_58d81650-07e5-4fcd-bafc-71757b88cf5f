#!/usr/bin/env python3
"""
Simple Binance Testnet Test

Bu script Binance testnet ile basit bağlantı testini yapar.

Author: inkbytefo
"""

import os
import ccxt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_binance_testnet():
    """Binance testnet bağlantısını test et."""
    
    print("🚀 Binance Testnet Bağlantı Testi")
    print("=" * 50)
    
    # API credentials
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    if not api_key or not api_secret:
        print("❌ API credentials bulunamadı!")
        print("   .env dosyasında BINANCE_API_KEY ve BINANCE_API_SECRET tanımlı olmalı")
        return False
    
    print(f"✅ API Key: {api_key[:8]}...")
    print(f"✅ API Secret: {api_secret[:8]}...")
    
    try:
        # Binance testnet exchange oluştur
        exchange = ccxt.binance({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': True,  # Testnet mode
            'enableRateLimit': True,
        })
        
        # Testnet URL'lerini manuel olarak ayarla
        exchange.urls['api'] = {
            'public': 'https://testnet.binance.vision/api',
            'private': 'https://testnet.binance.vision/api',
            'v3': 'https://testnet.binance.vision/api/v3',
            'v1': 'https://testnet.binance.vision/api/v1'
        }
        
        print("✅ Exchange configured for testnet")
        
        # Test 1: Server time
        print("\n📋 Test 1: Server Time")
        try:
            server_time = exchange.fetch_time()
            print(f"✅ Server time: {server_time}")
        except Exception as e:
            print(f"❌ Server time test failed: {e}")
        
        # Test 2: Market data (public endpoint)
        print("\n📋 Test 2: Market Data")
        try:
            ticker = exchange.fetch_ticker('BTC/USDT')
            print(f"✅ BTC/USDT Price: ${ticker['last']:,.2f}")
            print(f"✅ 24h Change: {ticker['percentage']:.2f}%")
        except Exception as e:
            print(f"❌ Market data test failed: {e}")
        
        # Test 3: Order book
        print("\n📋 Test 3: Order Book")
        try:
            order_book = exchange.fetch_order_book('BTC/USDT', limit=5)
            print(f"✅ Order book retrieved")
            print(f"   Best bid: ${order_book['bids'][0][0]:,.2f}")
            print(f"   Best ask: ${order_book['asks'][0][0]:,.2f}")
        except Exception as e:
            print(f"❌ Order book test failed: {e}")
        
        # Test 4: Account balance (private endpoint)
        print("\n📋 Test 4: Account Balance")
        try:
            balance = exchange.fetch_balance()
            print("✅ Balance retrieved successfully")
            
            # Show non-zero balances
            non_zero_balances = {k: v for k, v in balance['total'].items() if v > 0}
            if non_zero_balances:
                print("   Non-zero balances:")
                for currency, amount in non_zero_balances.items():
                    print(f"     {currency}: {amount}")
            else:
                print("   All balances are zero (normal for new testnet account)")
                
        except Exception as e:
            print(f"❌ Balance test failed: {e}")
        
        # Test 5: Trading rules
        print("\n📋 Test 5: Trading Rules")
        try:
            markets = exchange.load_markets()
            btc_market = markets.get('BTC/USDT')
            if btc_market:
                print("✅ Trading rules for BTC/USDT:")
                print(f"   Min order size: {btc_market['limits']['amount']['min']}")
                print(f"   Min notional: {btc_market['limits']['cost']['min']}")
                print(f"   Price precision: {btc_market['precision']['price']}")
            else:
                print("❌ BTC/USDT market not found")
        except Exception as e:
            print(f"❌ Trading rules test failed: {e}")
        
        print("\n🎉 Testnet bağlantı testleri tamamlandı!")
        print("\n📋 Sonraki Adımlar:")
        print("1. ✅ Binance testnet bağlantısı çalışıyor")
        print("2. 💰 Testnet hesabınıza test fonları ekleyin:")
        print("   https://testnet.binance.vision/")
        print("3. 🔄 Ana trading sistemini çalıştırabilirsiniz")
        
        return True
        
    except Exception as e:
        print(f"❌ Genel hata: {e}")
        return False


if __name__ == "__main__":
    success = test_binance_testnet()
    if success:
        print("\n✅ Test başarılı!")
    else:
        print("\n❌ Test başarısız!")