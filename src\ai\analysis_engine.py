#!/usr/bin/env python3
"""
AI Analysis Engine - Main AI orchestrator for market analysis

Author: inkbytefo
Description: Combines multiple AI models to analyze market data and generate insights
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
import json

from .sentiment_analyzer import SentimentAnalyzer
from .pattern_detector import PatternDetector
from .prediction_model import PredictionModel
from ..config.settings import AISettings


@dataclass
class MarketInsight:
    """Market insight data structure."""
    timestamp: datetime
    insight_type: str  # 'bullish', 'bearish', 'neutral', 'warning'
    confidence: float  # 0-1
    reasoning: str
    supporting_data: Dict[str, Any]
    timeframe: str  # 'short', 'medium', 'long'
    priority: int  # 1-5 (5 = highest)


@dataclass
class TradingSignal:
    """AI-generated trading signal."""
    timestamp: datetime
    action: str  # 'buy', 'sell', 'hold'
    asset: str
    confidence: float  # 0-1
    strength: float  # 0-1
    reasoning: List[str]
    risk_level: str  # 'low', 'medium', 'high'
    suggested_allocation: float  # 0-1 (percentage of portfolio)
    stop_loss: Optional[float]
    take_profit: Optional[float]


class AnalysisEngine:
    """Main AI analysis engine that orchestrates all AI components."""
    
    def __init__(self, settings: AISettings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        # Initialize AI components
        self.sentiment_analyzer = SentimentAnalyzer(settings.sentiment)
        self.pattern_detector = PatternDetector(settings.patterns)
        self.prediction_model = PredictionModel(settings.prediction)
        
        # Analysis configuration
        self.analysis_config = {
            'sentiment_weight': settings.sentiment_weight,
            'technical_weight': settings.technical_weight,
            'pattern_weight': settings.pattern_weight,
            'prediction_weight': settings.prediction_weight,
            'min_confidence_threshold': settings.confidence_threshold,
            'signal_cooldown_minutes': settings.signal_cooldown_minutes
        }
        
        # State tracking
        self.last_analysis = None
        self.recent_signals = []
        self.market_regime = 'neutral'  # 'bull', 'bear', 'neutral', 'volatile'
        self.analysis_history = []
        
        # Performance tracking
        self.analysis_stats = {
            'total_analyses': 0,
            'successful_predictions': 0,
            'accuracy_rate': 0.0,
            'avg_confidence': 0.0
        }
    
    async def start(self):
        """Start the analysis engine and all AI components."""
        self.logger.info("Starting AI Analysis Engine...")
        
        # Start AI components
        await self.sentiment_analyzer.start()
        await self.pattern_detector.start()
        await self.prediction_model.start()
        
        self.logger.info("AI Analysis Engine started")
    
    async def stop(self):
        """Stop the analysis engine and all AI components."""
        self.logger.info("Stopping AI Analysis Engine...")
        
        # Stop AI components
        await self.sentiment_analyzer.stop()
        await self.pattern_detector.stop()
        await self.prediction_model.stop()
        
        self.logger.info("AI Analysis Engine stopped")
    
    async def analyze_market(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Perform comprehensive market analysis using all AI components."""
        analysis_start = datetime.now()
        
        try:
            # Extract data components
            news_data = market_data.get('news', {})
            social_data = market_data.get('social_sentiment', {})
            technical_data = market_data.get('technical', {})
            market_prices = market_data.get('market_data', {})
            
            # Parallel analysis using all AI components
            sentiment_task = self.sentiment_analyzer.analyze(
                news_data, social_data
            )
            pattern_task = self.pattern_detector.detect_patterns(
                technical_data, market_prices
            )
            prediction_task = self.prediction_model.predict(
                market_data
            )
            
            # Wait for all analyses to complete
            sentiment_analysis, pattern_analysis, prediction_analysis = await asyncio.gather(
                sentiment_task, pattern_task, prediction_task
            )
            
            # Combine analyses
            combined_analysis = self._combine_analyses(
                sentiment_analysis,
                pattern_analysis, 
                prediction_analysis,
                technical_data
            )
            
            # Generate insights
            insights = self._generate_insights(combined_analysis, market_data)
            
            # Generate trading signals
            signals = self._generate_trading_signals(combined_analysis, insights)
            
            # Update market regime
            self._update_market_regime(combined_analysis)
            
            # Prepare final analysis result
            analysis_result = {
                'timestamp': analysis_start,
                'market_regime': self.market_regime,
                'overall_sentiment': combined_analysis['overall_sentiment'],
                'confidence': combined_analysis['confidence'],
                'insights': insights,
                'signals': signals,
                'components': {
                    'sentiment': sentiment_analysis,
                    'patterns': pattern_analysis,
                    'predictions': prediction_analysis
                },
                'analysis_duration': (datetime.now() - analysis_start).total_seconds()
            }
            
            # Update state and statistics
            self.last_analysis = analysis_result
            self._update_statistics(analysis_result)
            self._store_analysis_history(analysis_result)
            
            self.logger.info(f"Market analysis completed in {analysis_result['analysis_duration']:.2f}s")
            return analysis_result
            
        except Exception as e:
            self.logger.error(f"Error in market analysis: {e}")
            return self._get_fallback_analysis()
    
    def _combine_analyses(self, sentiment_analysis: Dict, pattern_analysis: Dict, 
                         prediction_analysis: Dict, technical_data: Dict) -> Dict[str, Any]:
        """Combine results from all AI components into unified analysis."""
        
        # Extract sentiment scores
        sentiment_score = sentiment_analysis.get('overall_score', 0)
        sentiment_confidence = sentiment_analysis.get('confidence', 0)
        
        # Extract pattern scores
        pattern_score = pattern_analysis.get('overall_signal', 0)
        pattern_confidence = pattern_analysis.get('confidence', 0)
        
        # Extract prediction scores
        prediction_score = prediction_analysis.get('direction_score', 0)
        prediction_confidence = prediction_analysis.get('confidence', 0)
        
        # Extract technical scores
        technical_score = 0
        technical_confidence = 0
        if technical_data and 'overall_signals' in technical_data:
            tech_signals = technical_data['overall_signals']
            if tech_signals['signal'] == 'buy':
                technical_score = tech_signals['strength']
            elif tech_signals['signal'] == 'sell':
                technical_score = -tech_signals['strength']
            technical_confidence = tech_signals['confidence']
        
        # Weighted combination
        weights = self.analysis_config
        
        combined_score = (
            sentiment_score * weights['sentiment_weight'] +
            technical_score * weights['technical_weight'] +
            pattern_score * weights['pattern_weight'] +
            prediction_score * weights['prediction_weight']
        )
        
        combined_confidence = (
            sentiment_confidence * weights['sentiment_weight'] +
            technical_confidence * weights['technical_weight'] +
            pattern_confidence * weights['pattern_weight'] +
            prediction_confidence * weights['prediction_weight']
        )
        
        # Determine overall sentiment
        if combined_score > 0.2:
            overall_sentiment = 'bullish'
        elif combined_score < -0.2:
            overall_sentiment = 'bearish'
        else:
            overall_sentiment = 'neutral'
        
        return {
            'overall_sentiment': overall_sentiment,
            'combined_score': combined_score,
            'confidence': combined_confidence,
            'component_scores': {
                'sentiment': sentiment_score,
                'technical': technical_score,
                'patterns': pattern_score,
                'prediction': prediction_score
            },
            'component_confidences': {
                'sentiment': sentiment_confidence,
                'technical': technical_confidence,
                'patterns': pattern_confidence,
                'prediction': prediction_confidence
            }
        }
    
    def _generate_insights(self, combined_analysis: Dict, market_data: Dict) -> List[MarketInsight]:
        """Generate market insights based on combined analysis."""
        insights = []
        timestamp = datetime.now()
        
        # Overall market sentiment insight
        sentiment = combined_analysis['overall_sentiment']
        confidence = combined_analysis['confidence']
        
        if confidence > 0.7:
            if sentiment == 'bullish':
                insights.append(MarketInsight(
                    timestamp=timestamp,
                    insight_type='bullish',
                    confidence=confidence,
                    reasoning=f"Strong bullish signals detected across multiple indicators with {confidence:.1%} confidence",
                    supporting_data=combined_analysis['component_scores'],
                    timeframe='short',
                    priority=4
                ))
            elif sentiment == 'bearish':
                insights.append(MarketInsight(
                    timestamp=timestamp,
                    insight_type='bearish',
                    confidence=confidence,
                    reasoning=f"Strong bearish signals detected across multiple indicators with {confidence:.1%} confidence",
                    supporting_data=combined_analysis['component_scores'],
                    timeframe='short',
                    priority=4
                ))
        
        # Volatility warning
        technical_data = market_data.get('technical', {})
        if technical_data:
            market_strength = technical_data.get('market_strength', 0)
            if market_strength > 0.8:
                insights.append(MarketInsight(
                    timestamp=timestamp,
                    insight_type='warning',
                    confidence=0.8,
                    reasoning="High market volatility detected - increased risk of sudden price movements",
                    supporting_data={'market_strength': market_strength},
                    timeframe='short',
                    priority=5
                ))
        
        # Sentiment divergence insight
        sentiment_score = combined_analysis['component_scores']['sentiment']
        technical_score = combined_analysis['component_scores']['technical']
        
        if abs(sentiment_score - technical_score) > 0.5:
            insights.append(MarketInsight(
                timestamp=timestamp,
                insight_type='warning',
                confidence=0.7,
                reasoning="Divergence detected between sentiment and technical indicators",
                supporting_data={
                    'sentiment_score': sentiment_score,
                    'technical_score': technical_score
                },
                timeframe='medium',
                priority=3
            ))
        
        return insights
    
    def _generate_trading_signals(self, combined_analysis: Dict, insights: List[MarketInsight]) -> List[TradingSignal]:
        """Generate trading signals based on analysis and insights."""
        signals = []
        timestamp = datetime.now()
        
        # Check signal cooldown
        if self._is_in_signal_cooldown():
            return signals
        
        overall_sentiment = combined_analysis['overall_sentiment']
        confidence = combined_analysis['confidence']
        combined_score = combined_analysis['combined_score']
        
        # Only generate signals above minimum confidence threshold
        if confidence < self.analysis_config['min_confidence_threshold']:
            return signals
        
        # Determine action based on sentiment and score
        if overall_sentiment == 'bullish' and combined_score > 0.3:
            action = 'buy'
            risk_level = 'medium' if confidence > 0.8 else 'high'
            allocation = min(confidence * 0.3, 0.2)  # Max 20% allocation
            
        elif overall_sentiment == 'bearish' and combined_score < -0.3:
            action = 'sell'
            risk_level = 'medium' if confidence > 0.8 else 'high'
            allocation = min(confidence * 0.3, 0.2)
            
        else:
            action = 'hold'
            risk_level = 'low'
            allocation = 0
        
        # Generate reasoning
        reasoning = []
        if combined_analysis['component_scores']['sentiment'] > 0.2:
            reasoning.append("Positive market sentiment detected")
        elif combined_analysis['component_scores']['sentiment'] < -0.2:
            reasoning.append("Negative market sentiment detected")
        
        if combined_analysis['component_scores']['technical'] > 0.2:
            reasoning.append("Technical indicators show bullish signals")
        elif combined_analysis['component_scores']['technical'] < -0.2:
            reasoning.append("Technical indicators show bearish signals")
        
        if combined_analysis['component_scores']['patterns'] > 0.2:
            reasoning.append("Bullish patterns identified")
        elif combined_analysis['component_scores']['patterns'] < -0.2:
            reasoning.append("Bearish patterns identified")
        
        # Create signal for major cryptocurrencies
        major_cryptos = ['BTC/USDT', 'ETH/USDT', 'BNB/USDT']
        
        for crypto in major_cryptos:
            if action != 'hold':
                signal = TradingSignal(
                    timestamp=timestamp,
                    action=action,
                    asset=crypto,
                    confidence=confidence,
                    strength=abs(combined_score),
                    reasoning=reasoning.copy(),
                    risk_level=risk_level,
                    suggested_allocation=allocation,
                    stop_loss=self._calculate_stop_loss(action, combined_score),
                    take_profit=self._calculate_take_profit(action, combined_score)
                )
                signals.append(signal)
        
        # Store signals for cooldown tracking
        self.recent_signals.extend(signals)
        
        return signals
    
    def _calculate_stop_loss(self, action: str, score: float) -> Optional[float]:
        """Calculate stop loss percentage based on action and score."""
        if action == 'buy':
            return -0.05 - (abs(score) * 0.02)  # 5-7% stop loss for buys
        elif action == 'sell':
            return 0.05 + (abs(score) * 0.02)   # 5-7% stop loss for sells
        return None
    
    def _calculate_take_profit(self, action: str, score: float) -> Optional[float]:
        """Calculate take profit percentage based on action and score."""
        if action == 'buy':
            return 0.10 + (abs(score) * 0.05)   # 10-15% take profit for buys
        elif action == 'sell':
            return -0.10 - (abs(score) * 0.05)  # 10-15% take profit for sells
        return None
    
    def _update_market_regime(self, combined_analysis: Dict):
        """Update the current market regime based on analysis."""
        sentiment = combined_analysis['overall_sentiment']
        confidence = combined_analysis['confidence']
        
        if confidence > 0.7:
            if sentiment == 'bullish':
                self.market_regime = 'bull'
            elif sentiment == 'bearish':
                self.market_regime = 'bear'
            else:
                self.market_regime = 'neutral'
        else:
            self.market_regime = 'volatile'
    
    def _is_in_signal_cooldown(self) -> bool:
        """Check if we're in signal cooldown period."""
        if not self.recent_signals:
            return False
        
        cooldown_minutes = self.analysis_config['signal_cooldown_minutes']
        cutoff_time = datetime.now() - timedelta(minutes=cooldown_minutes)
        
        # Remove old signals
        self.recent_signals = [
            signal for signal in self.recent_signals 
            if signal.timestamp > cutoff_time
        ]
        
        return len(self.recent_signals) > 0
    
    def _update_statistics(self, analysis_result: Dict):
        """Update analysis performance statistics."""
        self.analysis_stats['total_analyses'] += 1
        
        confidence = analysis_result.get('confidence', 0)
        total_confidence = self.analysis_stats['avg_confidence'] * (self.analysis_stats['total_analyses'] - 1)
        self.analysis_stats['avg_confidence'] = (total_confidence + confidence) / self.analysis_stats['total_analyses']
    
    def _store_analysis_history(self, analysis_result: Dict):
        """Store analysis in history for learning and improvement."""
        # Keep only last 100 analyses
        if len(self.analysis_history) >= 100:
            self.analysis_history.pop(0)
        
        self.analysis_history.append({
            'timestamp': analysis_result['timestamp'],
            'sentiment': analysis_result['overall_sentiment'],
            'confidence': analysis_result['confidence'],
            'signals_count': len(analysis_result.get('signals', []))
        })
    
    def _get_fallback_analysis(self) -> Dict[str, Any]:
        """Return fallback analysis in case of errors."""
        return {
            'timestamp': datetime.now(),
            'market_regime': 'neutral',
            'overall_sentiment': 'neutral',
            'confidence': 0.1,
            'insights': [],
            'signals': [],
            'components': {},
            'analysis_duration': 0,
            'error': True
        }
    
    def get_analysis_stats(self) -> Dict[str, Any]:
        """Get analysis performance statistics."""
        return self.analysis_stats.copy()
    
    def get_market_regime(self) -> str:
        """Get current market regime."""
        return self.market_regime
    
    def get_last_analysis(self) -> Optional[Dict[str, Any]]:
        """Get the last analysis result."""
        return self.last_analysis