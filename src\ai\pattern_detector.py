#!/usr/bin/env python3
"""
Pattern Detector - AI-powered pattern recognition for technical analysis

Author: inkbytefo
Description: Detects chart patterns and market structures using machine learning
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from scipy import signal
from scipy.stats import linregress
from sklearn.preprocessing import MinMaxScaler
from sklearn.cluster import DBSCAN
import pandas_ta as ta
from dataclasses import dataclass

# Performance optimization imports
from ..utils.performance_optimization import (
    profile, memoize, cached, BatchProcessor, MemoryOptimizer,
    AsyncPoolExecutor, CacheStrategy
)


@dataclass
class Pattern:
    """Detected pattern data structure."""
    name: str
    pattern_type: str  # 'reversal', 'continuation', 'breakout'
    confidence: float  # 0-1
    signal: str  # 'bullish', 'bearish', 'neutral'
    timeframe: str
    start_time: datetime
    end_time: datetime
    key_levels: List[float]
    description: str
    reliability: float  # Historical reliability of this pattern


class PatternDetector:
    """AI-powered pattern detection for cryptocurrency markets."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Pattern detection configuration (optimized for performance)
        self.min_pattern_length = config.get('min_pattern_length', 10)
        self.max_pattern_length = config.get('max_pattern_length', 50)  # Reduced for performance
        self.confidence_threshold = config.get('confidence_threshold', 0.6)
        
        # Performance optimization components
        self.memory_optimizer = MemoryOptimizer()
        self.batch_processor = BatchProcessor(batch_size=20, max_wait_time=0.3)
        self.executor = AsyncPoolExecutor(max_workers=2, executor_type='thread')
        
        # Pattern cache for faster repeated detections
        self.pattern_cache = {}
        self.cache_duration = 120  # 2 minutes cache
        
        # Pattern reliability scores (based on historical performance)
        self.pattern_reliability = {
            'head_and_shoulders': 0.75,
            'inverse_head_and_shoulders': 0.75,
            'double_top': 0.70,
            'double_bottom': 0.70,
            'triple_top': 0.65,
            'triple_bottom': 0.65,
            'ascending_triangle': 0.68,
            'descending_triangle': 0.68,
            'symmetrical_triangle': 0.60,
            'flag': 0.72,
            'pennant': 0.70,
            'wedge_rising': 0.65,
            'wedge_falling': 0.65,
            'channel_up': 0.63,
            'channel_down': 0.63,
            'support_resistance': 0.58,
            'breakout': 0.55
        }
        
        # Pattern history for learning
        self.pattern_history = []
        self.max_history_size = 1000
        
        # Market structure analysis
        self.market_structure = {
            'trend': 'neutral',
            'strength': 0,
            'key_levels': [],
            'last_update': None
        }
    
    async def start(self):
        """Start the pattern detector."""
        self.logger.info("Starting Pattern Detector...")
        self.logger.info("Pattern Detector started")
    
    async def stop(self):
        """Stop the pattern detector."""
        self.logger.info("Stopping Pattern Detector...")
        self.logger.info("Pattern Detector stopped")
    
    async def detect_patterns(self, technical_data: Dict[str, Any], 
                            market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Detect patterns in technical and market data."""
        detection_start = datetime.now()
        
        try:
            detected_patterns = []
            overall_signal = 0
            confidence = 0
            
            # Analyze each trading pair
            if 'pairs' in technical_data:
                for pair, pair_data in technical_data['pairs'].items():
                    try:
                        # Get OHLCV data for pattern detection
                        ohlcv_data = await self._get_ohlcv_data(pair, market_data)
                        
                        if ohlcv_data is not None and len(ohlcv_data) > self.min_pattern_length:
                            # Detect patterns for this pair
                            pair_patterns = await self._detect_pair_patterns(pair, ohlcv_data, pair_data)
                            detected_patterns.extend(pair_patterns)
                    
                    except Exception as e:
                        self.logger.warning(f"Error detecting patterns for {pair}: {e}")
            
            # Calculate overall signal from detected patterns
            if detected_patterns:
                overall_signal, confidence = self._calculate_overall_signal(detected_patterns)
            
            # Analyze market structure
            market_structure = self._analyze_market_structure(detected_patterns)
            
            # Update pattern history
            self._update_pattern_history(detected_patterns)
            
            result = {
                'timestamp': detection_start,
                'patterns': detected_patterns,
                'overall_signal': overall_signal,
                'confidence': confidence,
                'market_structure': market_structure,
                'pattern_count': len(detected_patterns),
                'detection_duration': (datetime.now() - detection_start).total_seconds()
            }
            
            self.logger.debug(f"Pattern detection completed in {result['detection_duration']:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in pattern detection: {e}")
            return self._get_fallback_patterns()
    
    async def _get_ohlcv_data(self, pair: str, market_data: Dict[str, Any]) -> Optional[pd.DataFrame]:
        """Extract OHLCV data for a trading pair."""
        try:
            # This would typically fetch from the market data
            # For now, we'll simulate or use cached data
            if 'ohlcv' in market_data and pair in market_data['ohlcv']:
                ohlcv = market_data['ohlcv'][pair]
                df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
                df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                df.set_index('timestamp', inplace=True)
                return df
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error getting OHLCV data for {pair}: {e}")
            return None
    
    async def _detect_pair_patterns(self, pair: str, ohlcv_data: pd.DataFrame, 
                                  technical_data: Dict[str, Any]) -> List[Pattern]:
        """Detect patterns for a specific trading pair."""
        patterns = []
        
        try:
            # Prepare data
            high = ohlcv_data['high'].values
            low = ohlcv_data['low'].values
            close = ohlcv_data['close'].values
            volume = ohlcv_data['volume'].values
            timestamps = ohlcv_data.index
            
            # Detect different pattern types
            patterns.extend(await self._detect_reversal_patterns(pair, high, low, close, timestamps))
            patterns.extend(await self._detect_continuation_patterns(pair, high, low, close, timestamps))
            patterns.extend(await self._detect_triangle_patterns(pair, high, low, close, timestamps))
            patterns.extend(await self._detect_support_resistance(pair, high, low, close, timestamps))
            patterns.extend(await self._detect_breakout_patterns(pair, high, low, close, volume, timestamps))
            
            # Filter patterns by confidence
            patterns = [p for p in patterns if p.confidence >= self.confidence_threshold]
            
            # Sort by confidence
            patterns.sort(key=lambda x: x.confidence, reverse=True)
            
        except Exception as e:
            self.logger.error(f"Error detecting patterns for {pair}: {e}")
        
        return patterns
    
    async def _detect_reversal_patterns(self, pair: str, high: np.ndarray, low: np.ndarray, 
                                      close: np.ndarray, timestamps: pd.DatetimeIndex) -> List[Pattern]:
        """Detect reversal patterns like head and shoulders, double tops/bottoms."""
        patterns = []
        
        try:
            # Find peaks and troughs
            peaks = signal.find_peaks(high, distance=5, prominence=np.std(high) * 0.5)[0]
            troughs = signal.find_peaks(-low, distance=5, prominence=np.std(low) * 0.5)[0]
            
            # Head and Shoulders
            hs_patterns = self._detect_head_and_shoulders(high, low, close, peaks, troughs, timestamps, pair)
            patterns.extend(hs_patterns)
            
            # Double Top/Bottom
            double_patterns = self._detect_double_patterns(high, low, close, peaks, troughs, timestamps, pair)
            patterns.extend(double_patterns)
            
            # Triple Top/Bottom
            triple_patterns = self._detect_triple_patterns(high, low, close, peaks, troughs, timestamps, pair)
            patterns.extend(triple_patterns)
            
        except Exception as e:
            self.logger.warning(f"Error detecting reversal patterns: {e}")
        
        return patterns
    
    async def _detect_continuation_patterns(self, pair: str, high: np.ndarray, low: np.ndarray,
                                          close: np.ndarray, timestamps: pd.DatetimeIndex) -> List[Pattern]:
        """Detect continuation patterns like flags and pennants."""
        patterns = []
        
        try:
            # Flag patterns
            flag_patterns = self._detect_flag_patterns(high, low, close, timestamps, pair)
            patterns.extend(flag_patterns)
            
            # Pennant patterns
            pennant_patterns = self._detect_pennant_patterns(high, low, close, timestamps, pair)
            patterns.extend(pennant_patterns)
            
            # Wedge patterns
            wedge_patterns = self._detect_wedge_patterns(high, low, close, timestamps, pair)
            patterns.extend(wedge_patterns)
            
        except Exception as e:
            self.logger.warning(f"Error detecting continuation patterns: {e}")
        
        return patterns
    
    async def _detect_triangle_patterns(self, pair: str, high: np.ndarray, low: np.ndarray,
                                      close: np.ndarray, timestamps: pd.DatetimeIndex) -> List[Pattern]:
        """Detect triangle patterns."""
        patterns = []
        
        try:
            window_size = min(50, len(close) // 3)
            
            for i in range(window_size, len(close) - window_size):
                segment_high = high[i-window_size:i+window_size]
                segment_low = low[i-window_size:i+window_size]
                segment_close = close[i-window_size:i+window_size]
                
                # Find trend lines
                upper_trend = self._find_trend_line(segment_high, 'resistance')
                lower_trend = self._find_trend_line(segment_low, 'support')
                
                if upper_trend and lower_trend:
                    # Check for triangle formation
                    triangle_type = self._classify_triangle(upper_trend, lower_trend)
                    
                    if triangle_type:
                        confidence = self._calculate_triangle_confidence(segment_high, segment_low, upper_trend, lower_trend)
                        
                        if confidence > self.confidence_threshold:
                            pattern = Pattern(
                                name=triangle_type,
                                pattern_type='continuation',
                                confidence=confidence,
                                signal='neutral',  # Triangles are neutral until breakout
                                timeframe='medium',
                                start_time=timestamps[i-window_size],
                                end_time=timestamps[i+window_size],
                                key_levels=[upper_trend['level'], lower_trend['level']],
                                description=f"{triangle_type} pattern detected in {pair}",
                                reliability=self.pattern_reliability.get(triangle_type, 0.6)
                            )
                            patterns.append(pattern)
        
        except Exception as e:
            self.logger.warning(f"Error detecting triangle patterns: {e}")
        
        return patterns
    
    async def _detect_support_resistance(self, pair: str, high: np.ndarray, low: np.ndarray,
                                       close: np.ndarray, timestamps: pd.DatetimeIndex) -> List[Pattern]:
        """Detect support and resistance levels."""
        patterns = []
        
        try:
            # Use clustering to find support/resistance levels
            price_levels = np.concatenate([high, low])
            
            # Normalize prices for clustering
            scaler = MinMaxScaler()
            normalized_prices = scaler.fit_transform(price_levels.reshape(-1, 1))
            
            # Apply optimized DBSCAN clustering (reduced complexity)
            clustering = DBSCAN(eps=0.02, min_samples=2, n_jobs=-1).fit(normalized_prices)
            
            # Find significant levels
            unique_labels = set(clustering.labels_)
            
            for label in unique_labels:
                if label == -1:  # Noise
                    continue
                
                cluster_prices = price_levels[clustering.labels_ == label]
                level = np.mean(cluster_prices)
                
                # Determine if it's support or resistance
                touches = np.sum(np.abs(price_levels - level) < (level * 0.01))  # 1% tolerance
                
                if touches >= 3:  # Minimum 3 touches
                    # Check if it's currently relevant
                    current_price = close[-1]
                    
                    if abs(current_price - level) / current_price < 0.05:  # Within 5%
                        level_type = 'resistance' if level > current_price else 'support'
                        
                        confidence = min(touches / 10, 1.0)  # More touches = higher confidence
                        
                        pattern = Pattern(
                            name=f"{level_type}_level",
                            pattern_type='support_resistance',
                            confidence=confidence,
                            signal='neutral',
                            timeframe='long',
                            start_time=timestamps[0],
                            end_time=timestamps[-1],
                            key_levels=[level],
                            description=f"{level_type.title()} level at {level:.2f} for {pair}",
                            reliability=self.pattern_reliability.get('support_resistance', 0.58)
                        )
                        patterns.append(pattern)
        
        except Exception as e:
            self.logger.warning(f"Error detecting support/resistance: {e}")
        
        return patterns
    
    async def _detect_breakout_patterns(self, pair: str, high: np.ndarray, low: np.ndarray,
                                      close: np.ndarray, volume: np.ndarray, 
                                      timestamps: pd.DatetimeIndex) -> List[Pattern]:
        """Detect breakout patterns with volume confirmation."""
        patterns = []
        
        try:
            # Calculate moving averages for trend detection
            df_temp = pd.DataFrame({'close': close, 'volume': volume})
            sma_20 = ta.sma(df_temp['close'], length=20)
            sma_50 = ta.sma(df_temp['close'], length=50)
            
            # Calculate volume moving average
            volume_ma = ta.sma(df_temp['volume'], length=20)
            
            # Convert to numpy arrays and handle NaN values
            sma_20 = sma_20.fillna(0).values if sma_20 is not None else np.zeros_like(close)
            sma_50 = sma_50.fillna(0).values if sma_50 is not None else np.zeros_like(close)
            volume_ma = volume_ma.fillna(0).values if volume_ma is not None else np.zeros_like(volume)
            
            # Look for breakouts
            for i in range(50, len(close) - 1):
                current_price = close[i]
                prev_high = np.max(high[i-20:i])
                prev_low = np.min(low[i-20:i])
                
                # Volume spike check
                volume_spike = volume[i] > volume_ma[i] * 1.5
                
                # Upward breakout
                if current_price > prev_high * 1.02 and volume_spike:  # 2% breakout with volume
                    confidence = self._calculate_breakout_confidence(close[i-20:i+1], volume[i-20:i+1], 'up')
                    
                    if confidence > self.confidence_threshold:
                        pattern = Pattern(
                            name='upward_breakout',
                            pattern_type='breakout',
                            confidence=confidence,
                            signal='bullish',
                            timeframe='short',
                            start_time=timestamps[i-20],
                            end_time=timestamps[i],
                            key_levels=[prev_high, current_price],
                            description=f"Upward breakout detected in {pair}",
                            reliability=self.pattern_reliability.get('breakout', 0.55)
                        )
                        patterns.append(pattern)
                
                # Downward breakout
                elif current_price < prev_low * 0.98 and volume_spike:  # 2% breakdown with volume
                    confidence = self._calculate_breakout_confidence(close[i-20:i+1], volume[i-20:i+1], 'down')
                    
                    if confidence > self.confidence_threshold:
                        pattern = Pattern(
                            name='downward_breakout',
                            pattern_type='breakout',
                            confidence=confidence,
                            signal='bearish',
                            timeframe='short',
                            start_time=timestamps[i-20],
                            end_time=timestamps[i],
                            key_levels=[prev_low, current_price],
                            description=f"Downward breakout detected in {pair}",
                            reliability=self.pattern_reliability.get('breakout', 0.55)
                        )
                        patterns.append(pattern)
        
        except Exception as e:
            self.logger.warning(f"Error detecting breakout patterns: {e}")
        
        return patterns
    
    def _detect_head_and_shoulders(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                                 peaks: np.ndarray, troughs: np.ndarray, 
                                 timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect head and shoulders patterns."""
        patterns = []
        
        # Need at least 3 peaks for head and shoulders
        if len(peaks) < 3:
            return patterns
        
        for i in range(len(peaks) - 2):
            left_shoulder = peaks[i]
            head = peaks[i + 1]
            right_shoulder = peaks[i + 2]
            
            # Check if middle peak is highest (head)
            if high[head] > high[left_shoulder] and high[head] > high[right_shoulder]:
                # Check if shoulders are roughly equal
                shoulder_diff = abs(high[left_shoulder] - high[right_shoulder]) / high[head]
                
                if shoulder_diff < 0.05:  # Shoulders within 5% of each other
                    # Find neckline (troughs between shoulders and head)
                    relevant_troughs = troughs[(troughs > left_shoulder) & (troughs < right_shoulder)]
                    
                    if len(relevant_troughs) >= 1:
                        neckline = np.mean(low[relevant_troughs])
                        
                        # Calculate confidence
                        confidence = 1 - shoulder_diff  # Lower difference = higher confidence
                        confidence *= 0.8  # Base confidence for H&S
                        
                        if confidence > self.confidence_threshold:
                            pattern = Pattern(
                                name='head_and_shoulders',
                                pattern_type='reversal',
                                confidence=confidence,
                                signal='bearish',
                                timeframe='medium',
                                start_time=timestamps[left_shoulder],
                                end_time=timestamps[right_shoulder],
                                key_levels=[high[head], neckline],
                                description=f"Head and shoulders pattern in {pair}",
                                reliability=self.pattern_reliability.get('head_and_shoulders', 0.75)
                            )
                            patterns.append(pattern)
        
        return patterns
    
    def _detect_double_patterns(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                              peaks: np.ndarray, troughs: np.ndarray,
                              timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect double top and double bottom patterns."""
        patterns = []
        
        # Double tops
        if len(peaks) >= 2:
            for i in range(len(peaks) - 1):
                peak1 = peaks[i]
                peak2 = peaks[i + 1]
                
                # Check if peaks are roughly equal
                height_diff = abs(high[peak1] - high[peak2]) / max(high[peak1], high[peak2])
                
                if height_diff < 0.03:  # Within 3%
                    # Find valley between peaks
                    valley_idx = np.argmin(low[peak1:peak2+1]) + peak1
                    valley_level = low[valley_idx]
                    
                    confidence = 1 - height_diff
                    confidence *= 0.7  # Base confidence for double top
                    
                    if confidence > self.confidence_threshold:
                        pattern = Pattern(
                            name='double_top',
                            pattern_type='reversal',
                            confidence=confidence,
                            signal='bearish',
                            timeframe='medium',
                            start_time=timestamps[peak1],
                            end_time=timestamps[peak2],
                            key_levels=[high[peak1], valley_level],
                            description=f"Double top pattern in {pair}",
                            reliability=self.pattern_reliability.get('double_top', 0.70)
                        )
                        patterns.append(pattern)
        
        # Double bottoms
        if len(troughs) >= 2:
            for i in range(len(troughs) - 1):
                trough1 = troughs[i]
                trough2 = troughs[i + 1]
                
                # Check if troughs are roughly equal
                depth_diff = abs(low[trough1] - low[trough2]) / min(low[trough1], low[trough2])
                
                if depth_diff < 0.03:  # Within 3%
                    # Find peak between troughs
                    peak_idx = np.argmax(high[trough1:trough2+1]) + trough1
                    peak_level = high[peak_idx]
                    
                    confidence = 1 - depth_diff
                    confidence *= 0.7  # Base confidence for double bottom
                    
                    if confidence > self.confidence_threshold:
                        pattern = Pattern(
                            name='double_bottom',
                            pattern_type='reversal',
                            confidence=confidence,
                            signal='bullish',
                            timeframe='medium',
                            start_time=timestamps[trough1],
                            end_time=timestamps[trough2],
                            key_levels=[low[trough1], peak_level],
                            description=f"Double bottom pattern in {pair}",
                            reliability=self.pattern_reliability.get('double_bottom', 0.70)
                        )
                        patterns.append(pattern)
        
        return patterns
    
    def _detect_triple_patterns(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                              peaks: np.ndarray, troughs: np.ndarray,
                              timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect triple top and triple bottom patterns."""
        patterns = []
        
        # Triple tops (need at least 3 peaks)
        if len(peaks) >= 3:
            for i in range(len(peaks) - 2):
                peak1, peak2, peak3 = peaks[i], peaks[i+1], peaks[i+2]
                
                # Check if all three peaks are roughly equal
                heights = [high[peak1], high[peak2], high[peak3]]
                max_height = max(heights)
                height_variations = [abs(h - max_height) / max_height for h in heights]
                
                if all(var < 0.02 for var in height_variations):  # Within 2%
                    confidence = 1 - max(height_variations)
                    confidence *= 0.65  # Base confidence for triple top
                    
                    if confidence > self.confidence_threshold:
                        pattern = Pattern(
                            name='triple_top',
                            pattern_type='reversal',
                            confidence=confidence,
                            signal='bearish',
                            timeframe='long',
                            start_time=timestamps[peak1],
                            end_time=timestamps[peak3],
                            key_levels=[max_height],
                            description=f"Triple top pattern in {pair}",
                            reliability=self.pattern_reliability.get('triple_top', 0.65)
                        )
                        patterns.append(pattern)
        
        return patterns
    
    def _detect_flag_patterns(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                            timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect flag patterns."""
        patterns = []
        
        # Look for flag patterns (rectangular consolidation after strong move)
        window = 20
        
        for i in range(window, len(close) - window):
            # Check for strong prior move
            prior_move = (close[i] - close[i-window]) / close[i-window]
            
            if abs(prior_move) > 0.05:  # At least 5% move
                # Check for consolidation (flag)
                flag_high = np.max(high[i:i+window])
                flag_low = np.min(low[i:i+window])
                flag_range = (flag_high - flag_low) / flag_low
                
                if flag_range < 0.03:  # Tight consolidation (3%)
                    confidence = 0.7 * (1 - flag_range / 0.03)  # Tighter = higher confidence
                    
                    if confidence > self.confidence_threshold:
                        signal = 'bullish' if prior_move > 0 else 'bearish'
                        
                        pattern = Pattern(
                            name='flag',
                            pattern_type='continuation',
                            confidence=confidence,
                            signal=signal,
                            timeframe='short',
                            start_time=timestamps[i],
                            end_time=timestamps[i+window],
                            key_levels=[flag_high, flag_low],
                            description=f"Flag pattern in {pair}",
                            reliability=self.pattern_reliability.get('flag', 0.72)
                        )
                        patterns.append(pattern)
        
        return patterns
    
    def _detect_pennant_patterns(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                               timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect pennant patterns."""
        patterns = []
        
        # Similar to flags but with converging trend lines
        window = 15
        
        for i in range(window, len(close) - window):
            # Check for strong prior move
            prior_move = (close[i] - close[i-window]) / close[i-window]
            
            if abs(prior_move) > 0.08:  # At least 8% move for pennant
                # Check for converging lines
                segment_high = high[i:i+window]
                segment_low = low[i:i+window]
                
                # Fit trend lines
                x = np.arange(len(segment_high))
                high_slope, _, high_r, _, _ = linregress(x, segment_high)
                low_slope, _, low_r, _, _ = linregress(x, segment_low)
                
                # Check if lines are converging
                if high_slope < 0 and low_slope > 0 and abs(high_r) > 0.7 and abs(low_r) > 0.7:
                    confidence = min(abs(high_r), abs(low_r)) * 0.8
                    
                    if confidence > self.confidence_threshold:
                        signal = 'bullish' if prior_move > 0 else 'bearish'
                        
                        pattern = Pattern(
                            name='pennant',
                            pattern_type='continuation',
                            confidence=confidence,
                            signal=signal,
                            timeframe='short',
                            start_time=timestamps[i],
                            end_time=timestamps[i+window],
                            key_levels=[segment_high[0], segment_low[0]],
                            description=f"Pennant pattern in {pair}",
                            reliability=self.pattern_reliability.get('pennant', 0.70)
                        )
                        patterns.append(pattern)
        
        return patterns
    
    def _detect_wedge_patterns(self, high: np.ndarray, low: np.ndarray, close: np.ndarray,
                             timestamps: pd.DatetimeIndex, pair: str) -> List[Pattern]:
        """Detect wedge patterns."""
        patterns = []
        
        window = 30
        
        for i in range(window, len(close) - 5):
            segment_high = high[i-window:i]
            segment_low = low[i-window:i]
            
            # Fit trend lines
            x = np.arange(len(segment_high))
            high_slope, _, high_r, _, _ = linregress(x, segment_high)
            low_slope, _, low_r, _, _ = linregress(x, segment_low)
            
            # Rising wedge (both lines rising, but upper line less steep)
            if (high_slope > 0 and low_slope > 0 and high_slope < low_slope and 
                abs(high_r) > 0.6 and abs(low_r) > 0.6):
                
                confidence = min(abs(high_r), abs(low_r)) * 0.7
                
                if confidence > self.confidence_threshold:
                    pattern = Pattern(
                        name='wedge_rising',
                        pattern_type='reversal',
                        confidence=confidence,
                        signal='bearish',
                        timeframe='medium',
                        start_time=timestamps[i-window],
                        end_time=timestamps[i],
                        key_levels=[segment_high[-1], segment_low[-1]],
                        description=f"Rising wedge pattern in {pair}",
                        reliability=self.pattern_reliability.get('wedge_rising', 0.65)
                    )
                    patterns.append(pattern)
            
            # Falling wedge (both lines falling, but lower line steeper)
            elif (high_slope < 0 and low_slope < 0 and high_slope > low_slope and
                  abs(high_r) > 0.6 and abs(low_r) > 0.6):
                
                confidence = min(abs(high_r), abs(low_r)) * 0.7
                
                if confidence > self.confidence_threshold:
                    pattern = Pattern(
                        name='wedge_falling',
                        pattern_type='reversal',
                        confidence=confidence,
                        signal='bullish',
                        timeframe='medium',
                        start_time=timestamps[i-window],
                        end_time=timestamps[i],
                        key_levels=[segment_high[-1], segment_low[-1]],
                        description=f"Falling wedge pattern in {pair}",
                        reliability=self.pattern_reliability.get('wedge_falling', 0.65)
                    )
                    patterns.append(pattern)
        
        return patterns
    
    def _find_trend_line(self, prices: np.ndarray, line_type: str) -> Optional[Dict]:
        """Find trend line in price data."""
        try:
            x = np.arange(len(prices))
            slope, intercept, r_value, _, _ = linregress(x, prices)
            
            if abs(r_value) > 0.6:  # Good correlation
                return {
                    'slope': slope,
                    'intercept': intercept,
                    'r_value': r_value,
                    'level': prices[-1]  # Current level
                }
        except Exception:
            pass
        
        return None
    
    def _classify_triangle(self, upper_trend: Dict, lower_trend: Dict) -> Optional[str]:
        """Classify triangle type based on trend lines."""
        upper_slope = upper_trend['slope']
        lower_slope = lower_trend['slope']
        
        # Ascending triangle (flat top, rising bottom)
        if abs(upper_slope) < 0.1 and lower_slope > 0.1:
            return 'ascending_triangle'
        
        # Descending triangle (falling top, flat bottom)
        elif upper_slope < -0.1 and abs(lower_slope) < 0.1:
            return 'descending_triangle'
        
        # Symmetrical triangle (converging lines)
        elif upper_slope < 0 and lower_slope > 0:
            return 'symmetrical_triangle'
        
        return None
    
    def _calculate_triangle_confidence(self, high: np.ndarray, low: np.ndarray,
                                     upper_trend: Dict, lower_trend: Dict) -> float:
        """Calculate confidence for triangle pattern."""
        # Base confidence on trend line correlation
        upper_r = abs(upper_trend['r_value'])
        lower_r = abs(lower_trend['r_value'])
        
        confidence = (upper_r + lower_r) / 2
        
        # Adjust for convergence
        convergence = abs(upper_trend['slope'] - lower_trend['slope'])
        confidence *= min(convergence / 0.5, 1.0)
        
        return confidence
    
    def _calculate_breakout_confidence(self, prices: np.ndarray, volumes: np.ndarray, 
                                     direction: str) -> float:
        """Calculate confidence for breakout pattern."""
        # Volume confirmation
        avg_volume = np.mean(volumes[:-1])
        breakout_volume = volumes[-1]
        volume_ratio = breakout_volume / avg_volume
        
        # Price momentum
        price_change = abs(prices[-1] - prices[-2]) / prices[-2]
        
        # Combine factors
        confidence = min(volume_ratio / 2, 1.0) * 0.6  # Volume weight
        confidence += min(price_change / 0.05, 1.0) * 0.4  # Price momentum weight
        
        return min(confidence, 1.0)
    
    def _calculate_overall_signal(self, patterns: List[Pattern]) -> Tuple[float, float]:
        """Calculate overall signal from detected patterns."""
        if not patterns:
            return 0, 0
        
        bullish_score = 0
        bearish_score = 0
        total_weight = 0
        
        for pattern in patterns:
            weight = pattern.confidence * pattern.reliability
            
            if pattern.signal == 'bullish':
                bullish_score += weight
            elif pattern.signal == 'bearish':
                bearish_score += weight
            
            total_weight += weight
        
        if total_weight == 0:
            return 0, 0
        
        # Calculate net signal
        net_signal = (bullish_score - bearish_score) / total_weight
        
        # Calculate confidence based on pattern agreement
        confidence = min(total_weight / len(patterns), 1.0)
        
        return net_signal, confidence
    
    def _analyze_market_structure(self, patterns: List[Pattern]) -> Dict[str, Any]:
        """Analyze overall market structure from patterns."""
        structure = {
            'trend': 'neutral',
            'strength': 0,
            'key_levels': [],
            'pattern_distribution': {},
            'last_update': datetime.now()
        }
        
        if not patterns:
            return structure
        
        # Count pattern types
        pattern_counts = {}
        for pattern in patterns:
            pattern_type = pattern.pattern_type
            if pattern_type not in pattern_counts:
                pattern_counts[pattern_type] = 0
            pattern_counts[pattern_type] += 1
        
        structure['pattern_distribution'] = pattern_counts
        
        # Determine trend from signals
        bullish_patterns = [p for p in patterns if p.signal == 'bullish']
        bearish_patterns = [p for p in patterns if p.signal == 'bearish']
        
        if len(bullish_patterns) > len(bearish_patterns):
            structure['trend'] = 'bullish'
        elif len(bearish_patterns) > len(bullish_patterns):
            structure['trend'] = 'bearish'
        
        # Calculate strength
        avg_confidence = np.mean([p.confidence for p in patterns])
        structure['strength'] = avg_confidence
        
        # Extract key levels
        all_levels = []
        for pattern in patterns:
            all_levels.extend(pattern.key_levels)
        
        if all_levels:
            # Remove duplicates and sort
            unique_levels = sorted(list(set(all_levels)))
            structure['key_levels'] = unique_levels
        
        return structure
    
    def _update_pattern_history(self, patterns: List[Pattern]):
        """Update pattern history for learning."""
        for pattern in patterns:
            self.pattern_history.append({
                'timestamp': datetime.now(),
                'pattern_name': pattern.name,
                'confidence': pattern.confidence,
                'signal': pattern.signal,
                'reliability': pattern.reliability
            })
        
        # Keep only recent history
        if len(self.pattern_history) > self.max_history_size:
            self.pattern_history = self.pattern_history[-self.max_history_size:]
    
    def _get_fallback_patterns(self) -> Dict[str, Any]:
        """Return fallback pattern analysis in case of errors."""
        return {
            'timestamp': datetime.now(),
            'patterns': [],
            'overall_signal': 0,
            'confidence': 0,
            'market_structure': {
                'trend': 'neutral',
                'strength': 0,
                'key_levels': [],
                'pattern_distribution': {}
            },
            'pattern_count': 0,
            'detection_duration': 0,
            'error': True
        }
    
    def get_pattern_history(self) -> List[Dict]:
        """Get pattern detection history."""
        return self.pattern_history.copy()
    
    def get_market_structure(self) -> Dict[str, Any]:
        """Get current market structure analysis."""
        return self.market_structure.copy()