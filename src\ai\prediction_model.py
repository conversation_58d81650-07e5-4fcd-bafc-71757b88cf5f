#!/usr/bin/env python3
"""
Prediction Model - AI-powered price prediction for cryptocurrency trading

Author: inkbytefo
Description: Machine learning models for price prediction and trend forecasting
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler, MinMaxScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import joblib
import pandas_ta as ta
from dataclasses import dataclass
import warnings
warnings.filterwarnings('ignore')

# Performance optimization imports
from ..utils.performance_optimization import (
    profile, memoize, cached, BatchProcessor, MemoryOptimizer,
    AsyncPoolExecutor, CacheStrategy
)


@dataclass
class Prediction:
    """Price prediction data structure."""
    symbol: str
    timeframe: str
    prediction_type: str  # 'price', 'direction', 'volatility'
    predicted_value: float
    confidence: float  # 0-1
    prediction_horizon: int  # minutes
    features_used: List[str]
    model_accuracy: float
    timestamp: datetime
    supporting_factors: List[str]


class PredictionModel:
    """AI-powered prediction model for cryptocurrency markets."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model configuration
        self.prediction_horizons = config.get('prediction_horizons', [5, 15, 30, 60])  # minutes
        self.min_training_samples = config.get('min_training_samples', 100)
        self.retrain_interval = config.get('retrain_interval', 3600)  # seconds
        
        # Optimized models for different prediction types (reduced complexity for speed)
        self.models = {
            'price': {
                'short': RandomForestRegressor(n_estimators=50, max_depth=10, n_jobs=-1, random_state=42),
                'medium': GradientBoostingRegressor(n_estimators=50, max_depth=6, random_state=42),
                'long': LinearRegression(n_jobs=-1)
            },
            'direction': {
                'short': RandomForestRegressor(n_estimators=25, max_depth=8, n_jobs=-1, random_state=42),
                'medium': GradientBoostingRegressor(n_estimators=25, max_depth=5, random_state=42)
            },
            'volatility': {
                'short': RandomForestRegressor(n_estimators=25, max_depth=6, n_jobs=-1, random_state=42)
            }
        }
        
        # Scalers for feature normalization
        self.scalers = {
            'features': StandardScaler(),
            'price': MinMaxScaler(),
            'volatility': StandardScaler()
        }
        
        # Model performance tracking
        self.model_performance = {
            'accuracy_history': [],
            'prediction_errors': [],
            'last_training': None,
            'training_samples': 0
        }
        
        # Feature importance tracking
        self.feature_importance = {}
        
        # Training data cache
        self.training_data = {
            'features': [],
            'targets': {},
            'timestamps': []
        }
        
        # Prediction cache
        self.prediction_cache = {}
        self.cache_duration = 300  # 5 minutes
        
        # Model status
        self.is_trained = False
        
        # Performance optimization components
        self.memory_optimizer = MemoryOptimizer()
        self.batch_processor = BatchProcessor(batch_size=50, max_wait_time=0.5)
        self.executor = AsyncPoolExecutor(max_workers=2, executor_type='thread')
        
        # Feature cache for faster repeated calculations
        self.feature_cache = {}
        self.cache_duration = 60  # 1 minute cache
        self.last_retrain = None
    
    async def start(self):
        """Start the prediction model."""
        self.logger.info("Starting Prediction Model...")
        
        # Try to load pre-trained models
        await self._load_models()
        
        self.logger.info("Prediction Model started")
    
    async def stop(self):
        """Stop the prediction model."""
        self.logger.info("Stopping Prediction Model...")
        
        # Save models
        await self._save_models()
        
        self.logger.info("Prediction Model stopped")
    
    async def predict(self, market_data: Dict[str, Any], 
                     technical_data: Dict[str, Any],
                     sentiment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate predictions for cryptocurrency prices."""
        prediction_start = datetime.now()
        
        try:
            predictions = []
            overall_confidence = 0
            
            # Check if models need retraining
            if await self._should_retrain():
                await self._retrain_models(market_data, technical_data, sentiment_data)
            
            # Generate predictions for each trading pair
            if 'pairs' in technical_data:
                for symbol, pair_data in technical_data['pairs'].items():
                    try:
                        # Prepare features for this pair
                        features = await self._prepare_features(symbol, market_data, technical_data, sentiment_data)
                        
                        if features is not None and self.is_trained:
                            # Generate predictions for different horizons
                            pair_predictions = await self._predict_pair(symbol, features)
                            predictions.extend(pair_predictions)
                    
                    except (ValueError, TypeError) as e:
                        self.logger.warning(f"Data format error for {symbol}, skipping: {e}")
                    except KeyError as e:
                        self.logger.warning(f"Missing required data for {symbol}, skipping: {e}")
                    except Exception as e:
                        self.logger.error(f"Unexpected error predicting for {symbol}: {e}", exc_info=True)
            
            # Calculate overall confidence
            if predictions:
                overall_confidence = np.mean([p.confidence for p in predictions])
            
            # Generate market outlook
            market_outlook = self._generate_market_outlook(predictions)
            
            result = {
                'timestamp': prediction_start,
                'predictions': predictions,
                'overall_confidence': overall_confidence,
                'market_outlook': market_outlook,
                'model_status': {
                    'is_trained': self.is_trained,
                    'last_training': self.last_retrain,
                    'training_samples': self.model_performance['training_samples']
                },
                'prediction_count': len(predictions),
                'prediction_duration': (datetime.now() - prediction_start).total_seconds()
            }
            
            self.logger.debug(f"Prediction completed in {result['prediction_duration']:.2f}s")
            return result
            
        except FileNotFoundError as e:
            self.logger.warning(f"Model file not found, using fallback predictions: {e}")
            return self._get_fallback_predictions()
        except (ValueError, TypeError) as e:
            self.logger.warning(f"Data format error in prediction, using fallback: {e}")
            return self._get_fallback_predictions()
        except ImportError as e:
            self.logger.error(f"Missing required library for prediction: {e}")
            return self._get_fallback_predictions()
        except MemoryError as e:
            self.logger.error(f"Insufficient memory for prediction: {e}")
            return self._get_fallback_predictions()
        except Exception as e:
            self.logger.critical(f"Unexpected error in prediction model: {e}", exc_info=True)
            return self._get_fallback_predictions()
    
    async def _prepare_features(self, symbol: str, market_data: Dict[str, Any],
                              technical_data: Dict[str, Any], 
                              sentiment_data: Dict[str, Any]) -> Optional[np.ndarray]:
        """Prepare feature vector for prediction."""
        try:
            features = []
            
            # Technical indicators
            if symbol in technical_data.get('pairs', {}):
                pair_data = technical_data['pairs'][symbol]
                
                # Price-based features
                if 'sma_20' in pair_data:
                    features.extend([
                        pair_data.get('sma_20', 0),
                        pair_data.get('sma_50', 0),
                        pair_data.get('ema_12', 0),
                        pair_data.get('ema_26', 0)
                    ])
                
                # Momentum indicators
                features.extend([
                    pair_data.get('rsi', 50),
                    pair_data.get('macd', 0),
                    pair_data.get('macd_signal', 0),
                    pair_data.get('macd_histogram', 0)
                ])
                
                # Volatility indicators
                features.extend([
                    pair_data.get('bb_upper', 0),
                    pair_data.get('bb_middle', 0),
                    pair_data.get('bb_lower', 0),
                    pair_data.get('atr', 0)
                ])
                
                # Volume indicators
                features.extend([
                    pair_data.get('volume_sma', 0),
                    pair_data.get('volume_ratio', 1)
                ])
            
            # Market data features
            if 'summary' in market_data:
                summary = market_data['summary']
                features.extend([
                    summary.get('total_volume_24h', 0),
                    summary.get('market_trend', 0),
                    summary.get('volatility_index', 0),
                    summary.get('average_spread', 0)
                ])
            
            # Sentiment features
            if 'overall_sentiment' in sentiment_data:
                sentiment = sentiment_data['overall_sentiment']
                features.extend([
                    sentiment.get('score', 0),
                    sentiment.get('confidence', 0),
                    sentiment.get('news_sentiment', 0),
                    sentiment.get('social_sentiment', 0)
                ])
            
            # Time-based features
            now = datetime.now()
            features.extend([
                now.hour,
                now.weekday(),
                now.day,
                now.month
            ])
            
            if len(features) > 0:
                return np.array(features).reshape(1, -1)
            
            return None
            
        except Exception as e:
            self.logger.warning(f"Error preparing features for {symbol}: {e}")
            return None
    
    async def _predict_pair(self, symbol: str, features: np.ndarray) -> List[Prediction]:
        """Generate predictions for a trading pair."""
        predictions = []
        
        try:
            # Check cache first
            cache_key = f"{symbol}_{hash(features.tobytes())}"
            if cache_key in self.prediction_cache:
                cache_entry = self.prediction_cache[cache_key]
                if (datetime.now() - cache_entry['timestamp']).seconds < self.cache_duration:
                    return cache_entry['predictions']
            
            # Normalize features
            if hasattr(self.scalers['features'], 'mean_'):
                features_scaled = self.scalers['features'].transform(features)
            else:
                features_scaled = features
            
            # Price predictions
            for horizon in self.prediction_horizons:
                timeframe = self._get_timeframe_category(horizon)
                
                if timeframe in self.models['price']:
                    model = self.models['price'][timeframe]
                    
                    if hasattr(model, 'predict'):
                        try:
                            # Price prediction
                            price_pred = model.predict(features_scaled)[0]
                            
                            # Calculate confidence based on model performance
                            confidence = self._calculate_prediction_confidence(model, timeframe, 'price')
                            
                            # Get supporting factors
                            supporting_factors = self._get_supporting_factors(features, model, timeframe)
                            
                            prediction = Prediction(
                                symbol=symbol,
                                timeframe=timeframe,
                                prediction_type='price',
                                predicted_value=price_pred,
                                confidence=confidence,
                                prediction_horizon=horizon,
                                features_used=self._get_feature_names(),
                                model_accuracy=self._get_model_accuracy(timeframe, 'price'),
                                timestamp=datetime.now(),
                                supporting_factors=supporting_factors
                            )
                            predictions.append(prediction)
                            
                        except Exception as e:
                            self.logger.warning(f"Error predicting price for {symbol} at {horizon}min: {e}")
            
            # Direction predictions
            for horizon in self.prediction_horizons[:2]:  # Only short and medium term
                timeframe = self._get_timeframe_category(horizon)
                
                if timeframe in self.models['direction']:
                    model = self.models['direction'][timeframe]
                    
                    if hasattr(model, 'predict'):
                        try:
                            # Direction prediction (1 for up, -1 for down)
                            direction_pred = model.predict(features_scaled)[0]
                            direction_value = 1 if direction_pred > 0 else -1
                            
                            confidence = self._calculate_prediction_confidence(model, timeframe, 'direction')
                            supporting_factors = self._get_supporting_factors(features, model, timeframe)
                            
                            prediction = Prediction(
                                symbol=symbol,
                                timeframe=timeframe,
                                prediction_type='direction',
                                predicted_value=direction_value,
                                confidence=confidence,
                                prediction_horizon=horizon,
                                features_used=self._get_feature_names(),
                                model_accuracy=self._get_model_accuracy(timeframe, 'direction'),
                                timestamp=datetime.now(),
                                supporting_factors=supporting_factors
                            )
                            predictions.append(prediction)
                            
                        except Exception as e:
                            self.logger.warning(f"Error predicting direction for {symbol} at {horizon}min: {e}")
            
            # Volatility prediction
            if 'short' in self.models['volatility']:
                model = self.models['volatility']['short']
                
                if hasattr(model, 'predict'):
                    try:
                        volatility_pred = model.predict(features_scaled)[0]
                        confidence = self._calculate_prediction_confidence(model, 'short', 'volatility')
                        supporting_factors = self._get_supporting_factors(features, model, 'short')
                        
                        prediction = Prediction(
                            symbol=symbol,
                            timeframe='short',
                            prediction_type='volatility',
                            predicted_value=volatility_pred,
                            confidence=confidence,
                            prediction_horizon=15,
                            features_used=self._get_feature_names(),
                            model_accuracy=self._get_model_accuracy('short', 'volatility'),
                            timestamp=datetime.now(),
                            supporting_factors=supporting_factors
                        )
                        predictions.append(prediction)
                        
                    except Exception as e:
                        self.logger.warning(f"Error predicting volatility for {symbol}: {e}")
            
            # Cache predictions
            self.prediction_cache[cache_key] = {
                'predictions': predictions,
                'timestamp': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"Error in pair prediction for {symbol}: {e}")
        
        return predictions
    
    async def _should_retrain(self) -> bool:
        """Check if models should be retrained."""
        # The live agent is designed to only use pre-trained models.
        return False
    
    async def _retrain_models(self, market_data: Dict[str, Any],
                            technical_data: Dict[str, Any],
                            sentiment_data: Dict[str, Any]):
        """Retrain prediction models with new data."""
        # This method is intended for offline execution on a dedicated training machine.
        # The live agent only loads pre-trained models.
        self.logger.warning("On-server model retraining is disabled for this low-resource environment.")
        pass
    
    async def _collect_training_data(self, market_data: Dict[str, Any],
                                   technical_data: Dict[str, Any],
                                   sentiment_data: Dict[str, Any]) -> Tuple[List, Dict]:
        """Collect and prepare training data."""
        features = []
        targets = {'price': [], 'direction': [], 'volatility': []}
        
        try:
            # This would typically collect historical data
            # For now, we'll simulate some training data
            
            # Generate synthetic training data based on current features
            if 'pairs' in technical_data:
                for symbol, pair_data in technical_data['pairs'].items():
                    # Prepare features
                    feature_vector = await self._prepare_features(symbol, market_data, technical_data, sentiment_data)
                    
                    if feature_vector is not None:
                        features.append(feature_vector.flatten())
                        
                        # Simulate targets (in real implementation, these would be actual historical outcomes)
                        current_price = pair_data.get('close', 100)
                        
                        # Price target (simulate future price)
                        price_change = np.random.normal(0, 0.02)  # 2% volatility
                        future_price = current_price * (1 + price_change)
                        targets['price'].append(future_price)
                        
                        # Direction target
                        direction = 1 if price_change > 0 else -1
                        targets['direction'].append(direction)
                        
                        # Volatility target
                        volatility = abs(price_change)
                        targets['volatility'].append(volatility)
            
            # Add to training data cache
            self.training_data['features'].extend(features)
            for key, values in targets.items():
                if key not in self.training_data['targets']:
                    self.training_data['targets'][key] = []
                self.training_data['targets'][key].extend(values)
            
            # Keep only recent data
            max_samples = 1000
            if len(self.training_data['features']) > max_samples:
                self.training_data['features'] = self.training_data['features'][-max_samples:]
                for key in self.training_data['targets']:
                    self.training_data['targets'][key] = self.training_data['targets'][key][-max_samples:]
            
        except Exception as e:
            self.logger.error(f"Error collecting training data: {e}")
        
        return features, targets
    
    def _get_timeframe_category(self, horizon_minutes: int) -> str:
        """Categorize prediction horizon into timeframe."""
        if horizon_minutes <= 15:
            return 'short'
        elif horizon_minutes <= 60:
            return 'medium'
        else:
            return 'long'
    
    def _calculate_prediction_confidence(self, model, timeframe: str, pred_type: str) -> float:
        """Calculate confidence for a prediction."""
        try:
            # Base confidence on model performance
            model_key = f"{pred_type}_{timeframe}"
            
            # Get recent performance
            recent_performance = [p for p in self.model_performance['accuracy_history'] 
                                if p['model_type'] == model_key]
            
            if recent_performance:
                latest_perf = recent_performance[-1]
                r2_score = latest_perf.get('r2', 0)
                confidence = max(0, min(1, r2_score))  # Clamp between 0 and 1
            else:
                confidence = 0.5  # Default confidence
            
            # Adjust based on feature importance
            if hasattr(model, 'feature_importances_'):
                # Higher feature importance spread = lower confidence
                importance_std = np.std(model.feature_importances_)
                confidence *= (1 - importance_std * 0.5)
            
            return max(0.1, min(0.95, confidence))  # Keep within reasonable bounds
            
        except Exception:
            return 0.5
    
    def _get_supporting_factors(self, features: np.ndarray, model, timeframe: str) -> List[str]:
        """Get supporting factors for a prediction."""
        factors = []
        
        try:
            if hasattr(model, 'feature_importances_'):
                feature_names = self._get_feature_names()
                importances = model.feature_importances_
                
                # Get top 3 most important features
                top_indices = np.argsort(importances)[-3:]
                
                for idx in top_indices:
                    if idx < len(feature_names):
                        factors.append(feature_names[idx])
            
            if not factors:
                factors = ['Technical indicators', 'Market sentiment', 'Volume analysis']
            
        except Exception:
            factors = ['Model analysis', 'Historical patterns']
        
        return factors
    
    def _get_feature_names(self) -> List[str]:
        """Get feature names for interpretation."""
        return [
            'SMA_20', 'SMA_50', 'EMA_12', 'EMA_26',
            'RSI', 'MACD', 'MACD_Signal', 'MACD_Histogram',
            'BB_Upper', 'BB_Middle', 'BB_Lower', 'ATR',
            'Volume_SMA', 'Volume_Ratio',
            'Total_Volume_24h', 'Market_Trend', 'Volatility_Index', 'Average_Spread',
            'Overall_Sentiment', 'Sentiment_Confidence', 'News_Sentiment', 'Social_Sentiment',
            'Hour', 'Weekday', 'Day', 'Month'
        ]
    
    def _get_model_accuracy(self, timeframe: str, pred_type: str) -> float:
        """Get model accuracy for a specific timeframe and prediction type."""
        try:
            model_key = f"{pred_type}_{timeframe}"
            recent_performance = [p for p in self.model_performance['accuracy_history'] 
                                if p['model_type'] == model_key]
            
            if recent_performance:
                return recent_performance[-1].get('r2', 0)
            
        except Exception:
            pass
        
        return 0.5
    
    def _generate_market_outlook(self, predictions: List[Prediction]) -> Dict[str, Any]:
        """Generate overall market outlook from predictions."""
        outlook = {
            'trend': 'neutral',
            'confidence': 0,
            'key_predictions': [],
            'risk_level': 'medium',
            'recommended_action': 'hold'
        }
        
        if not predictions:
            return outlook
        
        try:
            # Analyze direction predictions
            direction_preds = [p for p in predictions if p.prediction_type == 'direction']
            
            if direction_preds:
                bullish_count = sum(1 for p in direction_preds if p.predicted_value > 0)
                bearish_count = len(direction_preds) - bullish_count
                
                if bullish_count > bearish_count:
                    outlook['trend'] = 'bullish'
                    outlook['recommended_action'] = 'buy'
                elif bearish_count > bullish_count:
                    outlook['trend'] = 'bearish'
                    outlook['recommended_action'] = 'sell'
            
            # Calculate overall confidence
            avg_confidence = np.mean([p.confidence for p in predictions])
            outlook['confidence'] = avg_confidence
            
            # Determine risk level based on volatility predictions
            volatility_preds = [p for p in predictions if p.prediction_type == 'volatility']
            if volatility_preds:
                avg_volatility = np.mean([p.predicted_value for p in volatility_preds])
                if avg_volatility > 0.05:  # 5% volatility
                    outlook['risk_level'] = 'high'
                elif avg_volatility < 0.02:  # 2% volatility
                    outlook['risk_level'] = 'low'
            
            # Get key predictions (highest confidence)
            sorted_predictions = sorted(predictions, key=lambda x: x.confidence, reverse=True)
            outlook['key_predictions'] = sorted_predictions[:3]
            
        except Exception as e:
            self.logger.warning(f"Error generating market outlook: {e}")
        
        return outlook
    
    async def _load_models(self):
        """Load pre-trained models from disk."""
        import os
        
        try:
            models_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), 'models')
            
            if not os.path.exists(models_dir):
                self.logger.critical(f"Models directory not found: {models_dir}")
                self.is_trained = False
                return
            
            # Expected model files
            model_files = {
                'price_short': 'price_short_model.joblib',
                'price_medium': 'price_medium_model.joblib', 
                'price_long': 'price_long_model.joblib',
                'direction_short': 'direction_short_model.joblib',
                'direction_medium': 'direction_medium_model.joblib',
                'volatility_short': 'volatility_short_model.joblib'
            }
            
            scaler_files = {
                'features': 'features_scaler.joblib',
                'price': 'price_scaler.joblib',
                'volatility': 'volatility_scaler.joblib'
            }
            
            # Check if all required files exist
            missing_files = []
            for model_key, filename in model_files.items():
                filepath = os.path.join(models_dir, filename)
                if not os.path.exists(filepath):
                    missing_files.append(filename)
            
            for scaler_key, filename in scaler_files.items():
                filepath = os.path.join(models_dir, filename)
                if not os.path.exists(filepath):
                    missing_files.append(filename)
            
            if missing_files:
                self.logger.critical(f"Missing model files: {missing_files}")
                self.logger.critical("Cannot proceed without pre-trained models. Please run train_model.py first.")
                self.is_trained = False
                return
            
            # Load models
            loaded_models = 0
            for model_key, filename in model_files.items():
                filepath = os.path.join(models_dir, filename)
                try:
                    model = joblib.load(filepath)
                    
                    # Parse model key to get prediction type and timeframe
                    parts = model_key.split('_')
                    pred_type = parts[0]
                    timeframe = parts[1]
                    
                    self.models[pred_type][timeframe] = model
                    loaded_models += 1
                    
                    # Get model info
                    model_info = f"{type(model).__name__}"
                    if hasattr(model, 'n_estimators'):
                        model_info += f" (n_estimators={model.n_estimators})"
                    
                    self.logger.info(f"Loaded {pred_type}_{timeframe} model: {model_info}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to load model {filename}: {e}")
                    self.is_trained = False
                    return
            
            # Load scalers
            loaded_scalers = 0
            for scaler_key, filename in scaler_files.items():
                filepath = os.path.join(models_dir, filename)
                try:
                    scaler = joblib.load(filepath)
                    self.scalers[scaler_key] = scaler
                    loaded_scalers += 1
                    
                    scaler_info = f"{type(scaler).__name__}"
                    self.logger.info(f"Loaded {scaler_key} scaler: {scaler_info}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to load scaler {filename}: {e}")
                    self.is_trained = False
                    return
            
            # Set training status
            self.is_trained = True
            self.last_retrain = datetime.now()
            
            self.logger.info(f"Successfully loaded {loaded_models} models and {loaded_scalers} scalers")
            self.logger.info("Prediction model is ready for inference")
            
        except Exception as e:
            self.logger.critical(f"Critical error loading models: {e}")
            self.is_trained = False
    
    async def _save_models(self):
        """Save trained models to disk."""
        try:
            # In a real implementation, this would save models to files
            # For now, we'll just log the action
            self.logger.debug("Model saving skipped - models not persisted")
            
        except Exception as e:
            self.logger.warning(f"Error saving models: {e}")
    
    def _get_fallback_predictions(self) -> Dict[str, Any]:
        """Return fallback predictions in case of errors."""
        return {
            'timestamp': datetime.now(),
            'predictions': [],
            'overall_confidence': 0,
            'market_outlook': {
                'trend': 'neutral',
                'confidence': 0,
                'key_predictions': [],
                'risk_level': 'medium',
                'recommended_action': 'hold'
            },
            'model_status': {
                'is_trained': False,
                'last_training': None,
                'training_samples': 0
            },
            'prediction_count': 0,
            'prediction_duration': 0,
            'error': True
        }
    
    def get_model_performance(self) -> Dict[str, Any]:
        """Get model performance metrics."""
        return self.model_performance.copy()
    
    def get_feature_importance(self) -> Dict[str, Any]:
        """Get feature importance for all models."""
        return self.feature_importance.copy()
    
    def clear_cache(self):
        """Clear prediction cache."""
        self.prediction_cache.clear()
        self.logger.debug("Prediction cache cleared")