#!/usr/bin/env python3
"""
Sentiment Analyzer - AI-powered sentiment analysis for news and social media

Author: inkbytefo
Description: Analyzes sentiment from news articles and social media posts using NLP
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta
import re
import numpy as np
from textblob import TextBlob
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
# from transformers import pipeline, AutoTokenizer, AutoModelForSequenceClassification
# import torch


class SentimentAnalyzer:
    """AI-powered sentiment analyzer for cryptocurrency market sentiment."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Sentiment analysis models
        self.vader_analyzer = SentimentIntensityAnalyzer()
        self.finbert_pipeline = None
        self.crypto_sentiment_pipeline = None
        
        # Configuration
        self.use_finbert = config.get('use_finbert', True)
        self.use_crypto_model = config.get('use_crypto_model', False)
        self.sentiment_threshold = config.get('sentiment_threshold', 0.1)
        
        # Cryptocurrency-specific keywords and weights
        self.crypto_keywords = {
            'positive': {
                'moon': 2.0, 'bullish': 1.8, 'pump': 1.5, 'hodl': 1.2,
                'buy': 1.0, 'long': 1.0, 'green': 1.0, 'profit': 1.3,
                'gains': 1.4, 'rally': 1.6, 'breakout': 1.5, 'surge': 1.7,
                'adoption': 1.2, 'partnership': 1.3, 'upgrade': 1.1
            },
            'negative': {
                'dump': -1.5, 'bearish': -1.8, 'crash': -2.0, 'sell': -1.0,
                'short': -1.0, 'red': -1.0, 'loss': -1.3, 'drop': -1.2,
                'decline': -1.1, 'correction': -1.0, 'fear': -1.4,
                'panic': -1.8, 'scam': -2.5, 'hack': -2.0, 'regulation': -1.2
            }
        }
        
        # Market impact weights
        self.source_weights = {
            'news': 1.5,      # News articles have higher weight
            'reddit': 1.0,    # Reddit posts standard weight
            'twitter': 0.8,   # Twitter less weight due to noise
            'telegram': 0.6   # Telegram lowest weight
        }
        
        # Sentiment history for trend analysis
        self.sentiment_history = []
        self.max_history_size = 100
    
    async def start(self):
        """Initialize sentiment analysis models."""
        self.logger.info("Starting Sentiment Analyzer...")
        
        try:
            # Initialize FinBERT for financial sentiment analysis
            # Temporarily disabled due to TensorFlow DLL issues
            # if self.use_finbert:
            #     self.logger.info("Loading FinBERT model...")
            #     model_name = "ProsusAI/finbert"
            #     self.finbert_pipeline = pipeline(
            #         "sentiment-analysis",
            #         model=model_name,
            #         tokenizer=model_name,
            #         device=0 if torch.cuda.is_available() else -1
            #     )
            #     self.logger.info("FinBERT model loaded successfully")
            
            # Initialize crypto-specific sentiment model (if available)
            # Temporarily disabled due to TensorFlow DLL issues
            # if self.use_crypto_model:
            #     try:
            #         self.logger.info("Loading crypto sentiment model...")
            #         # This would be a custom crypto-trained model
            #         # For now, we'll use a general sentiment model
            #         self.crypto_sentiment_pipeline = pipeline(
            #             "sentiment-analysis",
            #             model="cardiffnlp/twitter-roberta-base-sentiment-latest",
            #             device=0 if torch.cuda.is_available() else -1
            #         )
            #         self.logger.info("Crypto sentiment model loaded successfully")
            #     except Exception as e:
            #         self.logger.warning(f"Failed to load crypto model: {e}")
            #         self.use_crypto_model = False
            
            # Temporarily using only basic sentiment analysis
            pass
            
        except Exception as e:
            self.logger.error(f"Error initializing sentiment models: {e}")
            self.use_finbert = False
            self.use_crypto_model = False
        
        self.logger.info("Sentiment Analyzer started")
    
    async def stop(self):
        """Stop the sentiment analyzer."""
        self.logger.info("Stopping Sentiment Analyzer...")
        
        # Clear model pipelines to free memory
        self.finbert_pipeline = None
        self.crypto_sentiment_pipeline = None
        
        self.logger.info("Sentiment Analyzer stopped")
    
    async def analyze(self, news_data: Dict[str, Any], social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment from news and social media data."""
        analysis_start = datetime.now()
        
        try:
            # Analyze news sentiment
            news_sentiment = await self._analyze_news_sentiment(news_data)
            
            # Analyze social media sentiment
            social_sentiment = await self._analyze_social_sentiment(social_data)
            
            # Combine sentiments
            combined_sentiment = self._combine_sentiments(news_sentiment, social_sentiment)
            
            # Calculate trend analysis
            trend_analysis = self._analyze_sentiment_trend(combined_sentiment)
            
            # Store in history
            self._update_sentiment_history(combined_sentiment)
            
            result = {
                'timestamp': analysis_start,
                'overall_score': combined_sentiment['overall_score'],
                'confidence': combined_sentiment['confidence'],
                'sentiment_label': combined_sentiment['sentiment_label'],
                'news_sentiment': news_sentiment,
                'social_sentiment': social_sentiment,
                'trend_analysis': trend_analysis,
                'keyword_analysis': combined_sentiment['keyword_analysis'],
                'analysis_duration': (datetime.now() - analysis_start).total_seconds()
            }
            
            self.logger.debug(f"Sentiment analysis completed in {result['analysis_duration']:.2f}s")
            return result
            
        except Exception as e:
            self.logger.error(f"Error in sentiment analysis: {e}")
            return self._get_fallback_sentiment()
    
    async def _analyze_news_sentiment(self, news_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment from news articles."""
        if not news_data or 'articles' not in news_data:
            return {'score': 0, 'confidence': 0, 'article_count': 0, 'sentiments': []}
        
        articles = news_data['articles']
        if not articles:
            return {'score': 0, 'confidence': 0, 'article_count': 0, 'sentiments': []}
        
        article_sentiments = []
        
        for article in articles[:50]:  # Limit to 50 most recent articles
            try:
                # Combine title and content
                text = f"{article.get('title', '')} {article.get('content', '')}"
                text = self._clean_text(text)
                
                if len(text.strip()) < 10:  # Skip very short texts
                    continue
                
                # Analyze with multiple methods
                sentiment_scores = await self._analyze_text_sentiment(text, 'news')
                
                # Weight by article relevance and recency
                weight = self._calculate_article_weight(article)
                
                article_sentiments.append({
                    'title': article.get('title', ''),
                    'sentiment_score': sentiment_scores['combined_score'],
                    'confidence': sentiment_scores['confidence'],
                    'weight': weight,
                    'weighted_score': sentiment_scores['combined_score'] * weight,
                    'published_at': article.get('published_at')
                })
                
            except Exception as e:
                self.logger.warning(f"Error analyzing article sentiment: {e}")
        
        if not article_sentiments:
            return {'score': 0, 'confidence': 0, 'article_count': 0, 'sentiments': []}
        
        # Calculate weighted average sentiment
        total_weighted_score = sum(a['weighted_score'] for a in article_sentiments)
        total_weight = sum(a['weight'] for a in article_sentiments)
        
        overall_score = total_weighted_score / total_weight if total_weight > 0 else 0
        
        # Calculate confidence based on agreement and sample size
        confidences = [a['confidence'] for a in article_sentiments]
        avg_confidence = np.mean(confidences) if confidences else 0
        
        # Adjust confidence based on sample size
        sample_size_factor = min(len(article_sentiments) / 20, 1.0)
        final_confidence = avg_confidence * sample_size_factor
        
        return {
            'score': overall_score,
            'confidence': final_confidence,
            'article_count': len(article_sentiments),
            'sentiments': article_sentiments
        }
    
    async def _analyze_social_sentiment(self, social_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze sentiment from social media posts."""
        if not social_data:
            return {'score': 0, 'confidence': 0, 'post_count': 0, 'platform_breakdown': {}}
        
        platform_sentiments = {}
        all_posts = []
        
        # Analyze sentiment for each platform
        for platform, posts in social_data.items():
            if not posts or not isinstance(posts, list):
                continue
            
            platform_scores = []
            
            for post in posts[:100]:  # Limit posts per platform
                try:
                    # Extract text content
                    text = self._extract_post_text(post)
                    if len(text.strip()) < 5:
                        continue
                    
                    # Analyze sentiment
                    sentiment_scores = await self._analyze_text_sentiment(text, platform)
                    
                    # Weight by engagement
                    engagement_weight = self._calculate_engagement_weight(post)
                    
                    weighted_score = sentiment_scores['combined_score'] * engagement_weight
                    platform_scores.append(weighted_score)
                    
                    all_posts.append({
                        'platform': platform,
                        'text': text[:200],  # Truncate for storage
                        'sentiment_score': sentiment_scores['combined_score'],
                        'engagement_weight': engagement_weight,
                        'weighted_score': weighted_score
                    })
                    
                except Exception as e:
                    self.logger.warning(f"Error analyzing {platform} post: {e}")
            
            if platform_scores:
                platform_sentiments[platform] = {
                    'score': np.mean(platform_scores),
                    'post_count': len(platform_scores),
                    'weight': self.source_weights.get(platform, 1.0)
                }
        
        if not platform_sentiments:
            return {'score': 0, 'confidence': 0, 'post_count': 0, 'platform_breakdown': {}}
        
        # Calculate overall social sentiment
        weighted_scores = []
        total_posts = 0
        
        for platform, data in platform_sentiments.items():
            weighted_score = data['score'] * data['weight']
            weighted_scores.append(weighted_score)
            total_posts += data['post_count']
        
        overall_score = np.mean(weighted_scores) if weighted_scores else 0
        
        # Calculate confidence
        confidence = min(total_posts / 100, 1.0) * 0.8  # Social media less reliable
        
        return {
            'score': overall_score,
            'confidence': confidence,
            'post_count': total_posts,
            'platform_breakdown': platform_sentiments
        }
    
    async def _analyze_text_sentiment(self, text: str, source_type: str) -> Dict[str, Any]:
        """Analyze sentiment of a single text using multiple methods."""
        sentiments = {}
        
        try:
            # VADER sentiment (good for social media)
            vader_scores = self.vader_analyzer.polarity_scores(text)
            sentiments['vader'] = vader_scores['compound']
            
            # TextBlob sentiment
            blob = TextBlob(text)
            sentiments['textblob'] = blob.sentiment.polarity
            
            # FinBERT sentiment (for financial texts)
            # Temporarily disabled due to TensorFlow DLL issues
            # if self.finbert_pipeline and source_type in ['news']:
            #     try:
            #         finbert_result = self.finbert_pipeline(text[:512])  # Limit length
            #         if finbert_result:
            #             label = finbert_result[0]['label'].lower()
            #             score = finbert_result[0]['score']
            #             
            #             if label == 'positive':
            #                 sentiments['finbert'] = score
            #             elif label == 'negative':
            #                 sentiments['finbert'] = -score
            #             else:
            #                 sentiments['finbert'] = 0
            #     except Exception as e:
            #         self.logger.debug(f"FinBERT analysis failed: {e}")
            
            # Crypto-specific sentiment model
            # Temporarily disabled due to TensorFlow DLL issues
            # if self.crypto_sentiment_pipeline and source_type in ['reddit', 'twitter']:
            #     try:
            #         crypto_result = self.crypto_sentiment_pipeline(text[:512])
            #         if crypto_result:
            #             label = crypto_result[0]['label'].lower()
            #             score = crypto_result[0]['score']
            #             
            #             if 'positive' in label:
            #                 sentiments['crypto_model'] = score
            #             elif 'negative' in label:
            #                 sentiments['crypto_model'] = -score
            #             else:
            #                 sentiments['crypto_model'] = 0
            #     except Exception as e:
            #         self.logger.debug(f"Crypto model analysis failed: {e}")
            
            # Keyword-based sentiment adjustment
            keyword_sentiment = self._analyze_crypto_keywords(text)
            sentiments['keywords'] = keyword_sentiment
            
        except Exception as e:
            self.logger.warning(f"Error in text sentiment analysis: {e}")
        
        # Combine sentiment scores
        if sentiments:
            # Weight different methods based on source type
            if source_type == 'news':
                weights = {'vader': 0.2, 'textblob': 0.2, 'finbert': 0.4, 'keywords': 0.2}
            else:
                weights = {'vader': 0.3, 'textblob': 0.2, 'crypto_model': 0.3, 'keywords': 0.2}
            
            combined_score = 0
            total_weight = 0
            
            for method, score in sentiments.items():
                if method in weights and not np.isnan(score):
                    combined_score += score * weights[method]
                    total_weight += weights[method]
            
            if total_weight > 0:
                combined_score /= total_weight
            
            # Calculate confidence based on agreement between methods
            scores = [s for s in sentiments.values() if not np.isnan(s)]
            if len(scores) > 1:
                agreement = 1 - (np.std(scores) / 2)  # Lower std = higher agreement
                confidence = max(0.1, min(agreement, 1.0))
            else:
                confidence = 0.5
        else:
            combined_score = 0
            confidence = 0.1
        
        return {
            'combined_score': combined_score,
            'confidence': confidence,
            'individual_scores': sentiments
        }
    
    def _analyze_crypto_keywords(self, text: str) -> float:
        """Analyze sentiment based on cryptocurrency-specific keywords."""
        text_lower = text.lower()
        sentiment_score = 0
        
        # Check positive keywords
        for keyword, weight in self.crypto_keywords['positive'].items():
            count = text_lower.count(keyword)
            sentiment_score += count * weight * 0.1  # Scale down
        
        # Check negative keywords
        for keyword, weight in self.crypto_keywords['negative'].items():
            count = text_lower.count(keyword)
            sentiment_score += count * weight * 0.1  # weight is already negative
        
        # Normalize to [-1, 1] range
        return np.tanh(sentiment_score)
    
    def _combine_sentiments(self, news_sentiment: Dict, social_sentiment: Dict) -> Dict[str, Any]:
        """Combine news and social media sentiments."""
        news_score = news_sentiment['score']
        news_confidence = news_sentiment['confidence']
        social_score = social_sentiment['score']
        social_confidence = social_sentiment['confidence']
        
        # Weight news higher than social media
        news_weight = 0.7
        social_weight = 0.3
        
        # Adjust weights based on confidence
        if news_confidence > social_confidence:
            news_weight = 0.8
            social_weight = 0.2
        elif social_confidence > news_confidence:
            news_weight = 0.6
            social_weight = 0.4
        
        # Calculate combined score
        combined_score = (news_score * news_weight + social_score * social_weight)
        
        # Calculate combined confidence
        combined_confidence = (news_confidence * news_weight + social_confidence * social_weight)
        
        # Determine sentiment label
        if combined_score > self.sentiment_threshold:
            sentiment_label = 'positive'
        elif combined_score < -self.sentiment_threshold:
            sentiment_label = 'negative'
        else:
            sentiment_label = 'neutral'
        
        # Keyword analysis summary
        keyword_analysis = {
            'positive_mentions': 0,
            'negative_mentions': 0,
            'trending_keywords': []
        }
        
        return {
            'overall_score': combined_score,
            'confidence': combined_confidence,
            'sentiment_label': sentiment_label,
            'news_weight': news_weight,
            'social_weight': social_weight,
            'keyword_analysis': keyword_analysis
        }
    
    def _analyze_sentiment_trend(self, current_sentiment: Dict) -> Dict[str, Any]:
        """Analyze sentiment trend over time."""
        if len(self.sentiment_history) < 3:
            return {
                'trend': 'insufficient_data',
                'trend_strength': 0,
                'volatility': 0
            }
        
        # Get recent sentiment scores
        recent_scores = [s['overall_score'] for s in self.sentiment_history[-10:]]
        recent_scores.append(current_sentiment['overall_score'])
        
        # Calculate trend
        if len(recent_scores) >= 3:
            # Simple linear trend
            x = np.arange(len(recent_scores))
            slope = np.polyfit(x, recent_scores, 1)[0]
            
            if slope > 0.05:
                trend = 'improving'
            elif slope < -0.05:
                trend = 'declining'
            else:
                trend = 'stable'
            
            trend_strength = abs(slope)
            volatility = np.std(recent_scores)
        else:
            trend = 'stable'
            trend_strength = 0
            volatility = 0
        
        return {
            'trend': trend,
            'trend_strength': trend_strength,
            'volatility': volatility,
            'recent_scores': recent_scores[-5:]  # Last 5 scores
        }
    
    def _update_sentiment_history(self, sentiment: Dict):
        """Update sentiment history for trend analysis."""
        self.sentiment_history.append({
            'timestamp': datetime.now(),
            'overall_score': sentiment['overall_score'],
            'confidence': sentiment['confidence']
        })
        
        # Keep only recent history
        if len(self.sentiment_history) > self.max_history_size:
            self.sentiment_history.pop(0)
    
    def _clean_text(self, text: str) -> str:
        """Clean and preprocess text for sentiment analysis."""
        if not text:
            return ""
        
        # Remove URLs
        text = re.sub(r'http\S+|www\S+|https\S+', '', text, flags=re.MULTILINE)
        
        # Remove special characters but keep basic punctuation
        text = re.sub(r'[^\w\s.,!?-]', '', text)
        
        # Remove extra whitespace
        text = ' '.join(text.split())
        
        return text.strip()
    
    def _extract_post_text(self, post: Dict) -> str:
        """Extract text content from social media post."""
        text_parts = []
        
        # Try different text fields
        for field in ['title', 'content', 'text', 'body', 'selftext']:
            if field in post and post[field]:
                text_parts.append(str(post[field]))
        
        return ' '.join(text_parts)
    
    def _calculate_article_weight(self, article: Dict) -> float:
        """Calculate weight for news article based on recency and relevance."""
        weight = 1.0
        
        # Recency weight (newer articles get higher weight)
        if 'published_at' in article:
            try:
                pub_time = article['published_at']
                if isinstance(pub_time, str):
                    pub_time = datetime.fromisoformat(pub_time.replace('Z', '+00:00'))
                
                hours_old = (datetime.now() - pub_time.replace(tzinfo=None)).total_seconds() / 3600
                recency_weight = max(0.5, 1 - (hours_old / 168))  # Decay over a week
                weight *= recency_weight
            except Exception:
                pass
        
        # Source credibility (could be expanded)
        source = article.get('source', {}).get('name', '').lower()
        if any(credible in source for credible in ['reuters', 'bloomberg', 'coindesk', 'cointelegraph']):
            weight *= 1.2
        
        return weight
    
    def _calculate_engagement_weight(self, post: Dict) -> float:
        """Calculate weight based on social media engagement."""
        weight = 1.0
        
        # Reddit-specific engagement
        if 'score' in post:
            score = post['score']
            weight *= min(1 + (score / 100), 3)  # Cap at 3x weight
        
        if 'num_comments' in post:
            comments = post['num_comments']
            weight *= min(1 + (comments / 50), 2)  # Cap at 2x weight
        
        # Twitter-specific engagement
        if 'retweet_count' in post:
            retweets = post['retweet_count']
            weight *= min(1 + (retweets / 100), 2.5)
        
        if 'like_count' in post:
            likes = post['like_count']
            weight *= min(1 + (likes / 200), 2)
        
        return min(weight, 5)  # Cap total weight at 5x
    
    def _get_fallback_sentiment(self) -> Dict[str, Any]:
        """Return fallback sentiment in case of errors."""
        return {
            'timestamp': datetime.now(),
            'overall_score': 0,
            'confidence': 0.1,
            'sentiment_label': 'neutral',
            'news_sentiment': {'score': 0, 'confidence': 0, 'article_count': 0},
            'social_sentiment': {'score': 0, 'confidence': 0, 'post_count': 0},
            'trend_analysis': {'trend': 'stable', 'trend_strength': 0, 'volatility': 0},
            'keyword_analysis': {'positive_mentions': 0, 'negative_mentions': 0},
            'analysis_duration': 0,
            'error': True
        }
    
    def get_sentiment_history(self) -> List[Dict]:
        """Get sentiment history for analysis."""
        return self.sentiment_history.copy()