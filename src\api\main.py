#!/usr/bin/env python3
"""
AI Trading System - API Main

Flask REST API for the AI Trading System.

Author: inkbytefo
"""

import os
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from flask import Flask, jsonify, request
from flask_cors import CORS
import logging
from datetime import datetime

from config.settings import Settings
from utils.logger import setup_logging


def create_app():
    """Create Flask application"""
    app = Flask(__name__)
    
    # Setup CORS
    CORS(app)
    
    # Setup logging
    logger = setup_logging()
    
    # Load settings
    settings = Settings()
    
    @app.route('/health', methods=['GET'])
    def health_check():
        """Health check endpoint"""
        return jsonify({
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'service': 'AI Trading System API'
        })
    
    @app.route('/api/v1/status', methods=['GET'])
    def get_status():
        """Get system status"""
        return jsonify({
            'status': 'running',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'components': {
                'api': 'healthy',
                'database': 'healthy',
                'trading_engine': 'healthy'
            }
        })
    
    @app.route('/api/v1/portfolio', methods=['GET'])
    def get_portfolio():
        """Get portfolio information"""
        return jsonify({
            'portfolio': {
                'total_value': 10000.0,
                'available_balance': 5000.0,
                'positions': [],
                'performance': {
                    'daily_pnl': 0.0,
                    'total_pnl': 0.0,
                    'win_rate': 0.0
                }
            },
            'timestamp': datetime.utcnow().isoformat()
        })
    
    @app.route('/api/v1/trades', methods=['GET'])
    def get_trades():
        """Get recent trades"""
        return jsonify({
            'trades': [],
            'total': 0,
            'timestamp': datetime.utcnow().isoformat()
        })
    
    @app.route('/api/v1/strategies', methods=['GET'])
    def get_strategies():
        """Get available strategies"""
        return jsonify({
            'strategies': [
                {
                    'id': 'ai_momentum',
                    'name': 'AI Momentum Strategy',
                    'status': 'active',
                    'performance': {
                        'daily_return': 0.0,
                        'total_return': 0.0,
                        'sharpe_ratio': 0.0
                    }
                }
            ],
            'timestamp': datetime.utcnow().isoformat()
        })
    
    @app.errorhandler(404)
    def not_found(error):
        return jsonify({'error': 'Not found'}), 404
    
    @app.errorhandler(500)
    def internal_error(error):
        return jsonify({'error': 'Internal server error'}), 500
    
    return app


if __name__ == '__main__':
    app = create_app()
    
    # Get configuration
    host = os.getenv('FLASK_HOST', '0.0.0.0')
    port = int(os.getenv('FLASK_PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    
    print(f"Starting AI Trading System API on {host}:{port}")
    app.run(host=host, port=port, debug=debug)