"""Configuration Manager for AI Trading Agent.

This module provides comprehensive configuration management with validation,
environment-specific settings, and dynamic configuration updates.

Author: inkbytefo
"""

import os
import json
import yaml
import logging
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, List, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime
import threading
from copy import deepcopy

try:
    import toml
except ImportError:
    toml = None

try:
    from cryptography.fernet import Fernet
except ImportError:
    Fernet = None


class ConfigFormat(Enum):
    """Configuration file formats."""
    JSON = "json"
    YAML = "yaml"
    TOML = "toml"
    ENV = "env"


class ConfigSection(Enum):
    """Configuration sections."""
    GENERAL = "general"
    TRADING = "trading"
    AI = "ai"
    RISK = "risk"
    DATA = "data"
    EXECUTION = "execution"
    MONITORING = "monitoring"
    ALERTS = "alerts"
    LOGGING = "logging"
    SECURITY = "security"
    PERFORMANCE = "performance"
    INTEGRATIONS = "integrations"


class ConfigValidationError(Exception):
    """Configuration validation error."""
    pass


@dataclass
class ConfigSchema:
    """Configuration schema definition."""
    section: ConfigSection
    required_fields: List[str] = field(default_factory=list)
    optional_fields: List[str] = field(default_factory=list)
    field_types: Dict[str, type] = field(default_factory=dict)
    field_validators: Dict[str, Callable] = field(default_factory=dict)
    default_values: Dict[str, Any] = field(default_factory=dict)
    description: str = ""


@dataclass
class ConfigChange:
    """Configuration change record."""
    timestamp: datetime
    section: str
    key: str
    old_value: Any
    new_value: Any
    source: str
    user: Optional[str] = None


class ConfigManager:
    """Comprehensive configuration management system."""
    
    def __init__(self, config_dir: str = "config", environment: str = "development"):
        """Initialize configuration manager."""
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.logger = logging.getLogger(__name__)
        
        # Configuration storage
        self.config: Dict[str, Dict[str, Any]] = {}
        self.schemas: Dict[ConfigSection, ConfigSchema] = {}
        self.encrypted_keys: List[str] = []
        
        # Change tracking
        self.change_history: List[ConfigChange] = []
        self.change_callbacks: Dict[str, List[Callable]] = {}
        
        # File watching
        self.file_watchers: Dict[str, Any] = {}
        self.auto_reload = True
        
        # Caching and performance
        self.cache_enabled = True
        self.cached_configs: Dict[str, Any] = {}
        self.cache_timestamps: Dict[str, datetime] = {}
        
        # Thread safety
        self.lock = threading.RLock()
        
        # Encryption
        self.encryption_key = None
        self._setup_encryption()
        
        # Initialize schemas
        self._setup_schemas()
        
        # Load configurations
        self._load_all_configs()
        
        self.logger.info(f"ConfigManager initialized for environment: {environment}")

    def get_settings(self) -> 'Settings':
        """Return a populated Settings object from the configuration."""
        from .settings import Settings, GeneralSettings, TradingSettings, AISettings, RiskSettings, DataSettings, ExecutionSettings, MonitoringSettings

        return Settings(
            general=GeneralSettings(**self.get_section(ConfigSection.GENERAL)),
            trading=TradingSettings(**self.get_section(ConfigSection.TRADING)),
            ai=AISettings(**self.get_section(ConfigSection.AI)),
            risk=RiskSettings(**self.get_section(ConfigSection.RISK)),
            data=DataSettings(**self.get_section(ConfigSection.DATA)),
            execution=ExecutionSettings(**self.get_section(ConfigSection.EXECUTION)),
            monitoring=MonitoringSettings(**self.get_section(ConfigSection.MONITORING)),
        )
    
    def get_config(self) -> Dict[str, Any]:
        """Get the complete configuration dictionary."""
        return self.config.copy()
    
    def _setup_encryption(self):
        """Setup configuration encryption."""
        try:
            if not Fernet:
                self.logger.warning("Cryptography library not available, encryption disabled")
                return
            
            # Try to load existing key
            key_file = self.config_dir / ".config_key"
            if key_file.exists():
                with open(key_file, 'rb') as f:
                    self.encryption_key = f.read()
            else:
                # Generate new key
                self.encryption_key = Fernet.generate_key()
                
                # Save key securely
                key_file.parent.mkdir(parents=True, exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(self.encryption_key)
                
                # Set restrictive permissions
                os.chmod(key_file, 0o600)
            
        except Exception as e:
            self.logger.error(f"Error setting up encryption: {e}")
            self.encryption_key = None
    
    def _setup_schemas(self):
        """Setup configuration schemas."""
        try:
            # General configuration schema
            self.schemas[ConfigSection.GENERAL] = ConfigSchema(
                section=ConfigSection.GENERAL,
                required_fields=["app_name", "version"],
                optional_fields=["debug", "log_level", "timezone"],
                field_types={
                    "app_name": str,
                    "version": str,
                    "debug": bool,
                    "log_level": str,
                    "timezone": str
                },
                default_values={
                    "debug": False,
                    "log_level": "INFO",
                    "timezone": "UTC"
                },
                description="General application settings"
            )
            
            # Trading configuration schema
            self.schemas[ConfigSection.TRADING] = ConfigSchema(
                section=ConfigSection.TRADING,
                required_fields=["default_exchange", "base_currency"],
                optional_fields=["max_positions", "default_order_size", "slippage_tolerance"],
                field_types={
                    "default_exchange": str,
                    "base_currency": str,
                    "max_positions": int,
                    "default_order_size": float,
                    "slippage_tolerance": float
                },
                default_values={
                    "max_positions": 10,
                    "default_order_size": 1000.0,
                    "slippage_tolerance": 0.001
                },
                description="Trading system settings"
            )
            
            # AI configuration schema
            self.schemas[ConfigSection.AI] = ConfigSchema(
                section=ConfigSection.AI,
                required_fields=["model_type", "prediction_horizon"],
                optional_fields=["confidence_threshold", "retrain_interval", "feature_count"],
                field_types={
                    "model_type": str,
                    "prediction_horizon": int,
                    "confidence_threshold": float,
                    "retrain_interval": int,
                    "feature_count": int
                },
                default_values={
                    "confidence_threshold": 0.7,
                    "retrain_interval": 24,
                    "feature_count": 50
                },
                description="AI model settings"
            )
            
            # Risk management schema
            self.schemas[ConfigSection.RISK] = ConfigSchema(
                section=ConfigSection.RISK,
                required_fields=["max_portfolio_risk", "max_position_risk"],
                optional_fields=["var_confidence", "stress_test_scenarios", "correlation_threshold"],
                field_types={
                    "max_portfolio_risk": float,
                    "max_position_risk": float,
                    "var_confidence": float,
                    "stress_test_scenarios": int,
                    "correlation_threshold": float
                },
                default_values={
                    "var_confidence": 0.95,
                    "stress_test_scenarios": 1000,
                    "correlation_threshold": 0.8
                },
                description="Risk management settings"
            )
            
            # Data configuration schema
            self.schemas[ConfigSection.DATA] = ConfigSchema(
                section=ConfigSection.DATA,
                required_fields=["primary_source", "update_frequency"],
                optional_fields=["backup_sources", "cache_duration", "data_quality_checks"],
                field_types={
                    "primary_source": str,
                    "update_frequency": int,
                    "backup_sources": list,
                    "cache_duration": int,
                    "data_quality_checks": dict
                },
                default_values={
                    "backup_sources": [],
                    "cache_duration": 300,
                    "data_quality_checks": {
                        "completeness": True,
                        "accuracy": True,
                        "consistency": True,
                        "timeliness": True,
                        "validity": True
                    }
                },
                description="Data source settings"
            )
            
            # Execution configuration schema
            self.schemas[ConfigSection.EXECUTION] = ConfigSchema(
                section=ConfigSection.EXECUTION,
                required_fields=["default_venue", "order_timeout"],
                optional_fields=["enable_smart_routing", "execution_algorithm", "commission_model"],
                field_types={
                    "default_venue": str,
                    "order_timeout": int,
                    "enable_smart_routing": bool,
                    "execution_algorithm": str,
                    "commission_model": str
                },
                default_values={
                    "enable_smart_routing": True,
                    "execution_algorithm": "TWAP",
                    "commission_model": "percentage"
                },
                description="Trade execution settings"
            )
            
            # Monitoring configuration schema
            self.schemas[ConfigSection.MONITORING] = ConfigSchema(
                section=ConfigSection.MONITORING,
                required_fields=["metrics_interval", "health_check_interval"],
                optional_fields=["performance_tracking", "system_monitoring", "alert_thresholds"],
                field_types={
                    "metrics_interval": int,
                    "health_check_interval": int,
                    "performance_tracking": bool,
                    "system_monitoring": bool,
                    "alert_thresholds": dict
                },
                default_values={
                    "performance_tracking": True,
                    "system_monitoring": True,
                    "alert_thresholds": {}
                },
                description="System monitoring settings"
            )
            
        except Exception as e:
            self.logger.error(f"Error setting up schemas: {e}")
    
    def _load_all_configs(self):
        """Load all configuration files."""
        try:
            # Ensure config directory exists
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # Load base configuration
            self._load_config_file("base.yaml", ConfigFormat.YAML)
            
            # Load environment-specific configuration
            env_file = f"{self.environment}.yaml"
            self._load_config_file(env_file, ConfigFormat.YAML)
            
            # Load local overrides if they exist
            local_file = "local.yaml"
            if (self.config_dir / local_file).exists():
                self._load_config_file(local_file, ConfigFormat.YAML)
            
            # Load environment variables
            self._load_environment_variables()
            
            # Validate all configurations
            self._validate_all_configs()
            
            # Apply default values
            self._apply_default_values()
            
        except Exception as e:
            self.logger.error(f"Error loading configurations: {e}")
            # Load minimal default configuration
            self._load_default_config()
    
    def _load_config_file(self, filename: str, format_type: ConfigFormat):
        """Load configuration from file."""
        try:
            filepath = self.config_dir / filename
            
            if not filepath.exists():
                self.logger.debug(f"Config file not found: {filename}")
                return
            
            with open(filepath, 'r', encoding='utf-8') as f:
                if format_type == ConfigFormat.JSON:
                    data = json.load(f)
                elif format_type == ConfigFormat.YAML:
                    data = yaml.safe_load(f)
                elif format_type == ConfigFormat.TOML and toml:
                    data = toml.load(f)
                else:
                    self.logger.error(f"Unsupported config format: {format_type}")
                    return
            
            # Merge with existing configuration
            self._merge_config(data)
            
            self.logger.info(f"Loaded configuration from: {filename}")
            
        except Exception as e:
            self.logger.error(f"Error loading config file {filename}: {e}")
    
    def _load_environment_variables(self):
        """Load configuration from environment variables."""
        try:
            env_prefix = "TRADING_AGENT_"
            
            for key, value in os.environ.items():
                if key.startswith(env_prefix):
                    # Convert environment variable to config path
                    config_key = key[len(env_prefix):].lower()
                    
                    # Handle nested keys (e.g., TRADING_AGENT_AI__MODEL_TYPE)
                    if "__" in config_key:
                        parts = config_key.split("__")
                        section = parts[0]
                        field = "__".join(parts[1:])
                        
                        if section not in self.config:
                            self.config[section] = {}
                        
                        # Try to convert value to appropriate type
                        converted_value = self._convert_env_value(value)
                        self.config[section][field] = converted_value
                    else:
                        # Top-level configuration
                        converted_value = self._convert_env_value(value)
                        if "general" not in self.config:
                            self.config["general"] = {}
                        self.config["general"][config_key] = converted_value
            
        except Exception as e:
            self.logger.error(f"Error loading environment variables: {e}")
    
    def _convert_env_value(self, value: str) -> Any:
        """Convert environment variable value to appropriate type."""
        try:
            # Try boolean
            if value.lower() in ('true', 'false'):
                return value.lower() == 'true'
            
            # Try integer
            try:
                return int(value)
            except ValueError:
                pass
            
            # Try float
            try:
                return float(value)
            except ValueError:
                pass
            
            # Try JSON (for lists, dicts)
            try:
                return json.loads(value)
            except (json.JSONDecodeError, ValueError):
                pass
            
            # Return as string
            return value
            
        except Exception:
            return value
    
    def _merge_config(self, new_config: Dict[str, Any]):
        """Merge new configuration with existing."""
        try:
            with self.lock:
                for section, values in new_config.items():
                    if section not in self.config:
                        self.config[section] = {}
                    
                    if isinstance(values, dict):
                        self.config[section].update(values)
                    else:
                        self.config[section] = values
                
                # Clear cache after merge
                if self.cache_enabled:
                    self.cached_configs.clear()
                    self.cache_timestamps.clear()
            
        except Exception as e:
            self.logger.error(f"Error merging configuration: {e}")
    
    def _validate_all_configs(self):
        """Validate all configurations against schemas."""
        try:
            for section_enum, schema in self.schemas.items():
                section_name = section_enum.value
                
                if section_name in self.config:
                    self._validate_section(section_name, self.config[section_name], schema)
            
        except Exception as e:
            self.logger.error(f"Error validating configurations: {e}")
    
    def _validate_section(self, section_name: str, config_data: Dict[str, Any], schema: ConfigSchema):
        """Validate configuration section against schema."""
        try:
            # Check required fields
            for field in schema.required_fields:
                if field not in config_data:
                    raise ConfigValidationError(f"Required field '{field}' missing in section '{section_name}'")
            
            # Validate field types and values
            for field, value in config_data.items():
                # Check type
                if field in schema.field_types:
                    expected_type = schema.field_types[field]
                    if not isinstance(value, expected_type):
                        try:
                            # Try to convert
                            if expected_type == bool and isinstance(value, str):
                                config_data[field] = value.lower() in ('true', '1', 'yes', 'on')
                            elif expected_type in (int, float):
                                config_data[field] = expected_type(value)
                            else:
                                raise ConfigValidationError(
                                    f"Field '{field}' in section '{section_name}' must be of type {expected_type.__name__}, got {type(value).__name__}"
                                )
                        except (ValueError, TypeError):
                            raise ConfigValidationError(
                                f"Field '{field}' in section '{section_name}' cannot be converted to {expected_type.__name__}"
                            )
                
                # Custom validation
                if field in schema.field_validators:
                    validator = schema.field_validators[field]
                    if not validator(value):
                        raise ConfigValidationError(f"Validation failed for field '{field}' in section '{section_name}'")
            
        except ConfigValidationError:
            raise
        except Exception as e:
            raise ConfigValidationError(f"Error validating section '{section_name}': {e}")
    
    def _apply_default_values(self):
        """Apply default values for missing optional fields."""
        try:
            with self.lock:
                for section_enum, schema in self.schemas.items():
                    section_name = section_enum.value
                    
                    if section_name not in self.config:
                        self.config[section_name] = {}
                    
                    # Apply defaults for missing fields
                    for field, default_value in schema.default_values.items():
                        if field not in self.config[section_name]:
                            self.config[section_name][field] = deepcopy(default_value)
            
        except Exception as e:
            self.logger.error(f"Error applying default values: {e}")
    
    def _load_default_config(self):
        """Load minimal default configuration."""
        try:
            self.config = {
                "general": {
                    "app_name": "AI Trading Agent",
                    "version": "1.0.0",
                    "debug": False,
                    "log_level": "INFO"
                },
                "trading": {
                    "default_exchange": "binance",
                    "base_currency": "USDT",
                    "max_positions": 10
                },
                "ai": {
                    "model_type": "ensemble",
                    "prediction_horizon": 24,
                    "confidence_threshold": 0.7
                },
                "risk": {
                    "max_portfolio_risk": 0.02,
                    "max_position_risk": 0.005
                }
            }
            
            self.logger.info("Loaded default configuration")
            
        except Exception as e:
            self.logger.error(f"Error loading default configuration: {e}")
    
    # Public interface methods
    def get(self, section: Union[str, ConfigSection], key: Optional[str] = None, default: Any = None) -> Any:
        """Get configuration value."""
        try:
            with self.lock:
                section_name = section.value if isinstance(section, ConfigSection) else section
                
                # Check cache first
                cache_key = f"{section_name}.{key}" if key else section_name
                if self.cache_enabled and cache_key in self.cached_configs:
                    return self.cached_configs[cache_key]
                
                if section_name not in self.config:
                    return default
                
                if key is None:
                    result = self.config[section_name]
                else:
                    result = self.config[section_name].get(key, default)
                
                # Cache result
                if self.cache_enabled:
                    self.cached_configs[cache_key] = result
                    self.cache_timestamps[cache_key] = datetime.now()
                
                return result
                
        except Exception as e:
            self.logger.error(f"Error getting configuration: {e}")
            return default
    
    def set(self, section: Union[str, ConfigSection], key: str, value: Any, 
           source: str = "manual", user: Optional[str] = None, validate: bool = True) -> bool:
        """Set configuration value."""
        try:
            with self.lock:
                section_name = section.value if isinstance(section, ConfigSection) else section
                
                # Get old value for change tracking
                old_value = self.get(section_name, key)
                
                # Validate if requested
                if validate and section_name in [s.value for s in self.schemas]:
                    schema = next(s for s in self.schemas.values() if s.section.value == section_name)
                    
                    # Create temporary config for validation
                    temp_config = self.config.get(section_name, {}).copy()
                    temp_config[key] = value
                    
                    self._validate_section(section_name, temp_config, schema)
                
                # Set value
                if section_name not in self.config:
                    self.config[section_name] = {}
                
                self.config[section_name][key] = value
                
                # Record change
                change = ConfigChange(
                    timestamp=datetime.now(),
                    section=section_name,
                    key=key,
                    old_value=old_value,
                    new_value=value,
                    source=source,
                    user=user
                )
                self.change_history.append(change)
                
                # Clear cache
                if self.cache_enabled:
                    cache_key = f"{section_name}.{key}"
                    self.cached_configs.pop(cache_key, None)
                    self.cache_timestamps.pop(cache_key, None)
                
                # Trigger callbacks
                self._trigger_change_callbacks(section_name, key, old_value, value)
                
                self.logger.info(f"Configuration updated: {section_name}.{key} = {value}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error setting configuration: {e}")
            return False
    
    def update_section(self, section: Union[str, ConfigSection], updates: Dict[str, Any], 
                      source: str = "manual", user: Optional[str] = None) -> bool:
        """Update multiple values in a section."""
        try:
            section_name = section.value if isinstance(section, ConfigSection) else section
            
            success = True
            for key, value in updates.items():
                if not self.set(section_name, key, value, source, user):
                    success = False
            
            return success
            
        except Exception as e:
            self.logger.error(f"Error updating section: {e}")
            return False
    
    def delete(self, section: Union[str, ConfigSection], key: str) -> bool:
        """Delete configuration value."""
        try:
            with self.lock:
                section_name = section.value if isinstance(section, ConfigSection) else section
                
                if section_name not in self.config or key not in self.config[section_name]:
                    return False
                
                old_value = self.config[section_name][key]
                del self.config[section_name][key]
                
                # Record change
                change = ConfigChange(
                    timestamp=datetime.now(),
                    section=section_name,
                    key=key,
                    old_value=old_value,
                    new_value=None,
                    source="deletion"
                )
                self.change_history.append(change)
                
                # Clear cache
                if self.cache_enabled:
                    cache_key = f"{section_name}.{key}"
                    self.cached_configs.pop(cache_key, None)
                    self.cache_timestamps.pop(cache_key, None)
                
                self.logger.info(f"Configuration deleted: {section_name}.{key}")
                return True
                
        except Exception as e:
            self.logger.error(f"Error deleting configuration: {e}")
            return False
    
    def register_change_callback(self, section: str, callback: Callable[[str, str, Any, Any], None]):
        """Register callback for configuration changes."""
        try:
            if section not in self.change_callbacks:
                self.change_callbacks[section] = []
            
            self.change_callbacks[section].append(callback)
            
        except Exception as e:
            self.logger.error(f"Error registering change callback: {e}")
    
    def _trigger_change_callbacks(self, section: str, key: str, old_value: Any, new_value: Any):
        """Trigger change callbacks."""
        try:
            # Section-specific callbacks
            if section in self.change_callbacks:
                for callback in self.change_callbacks[section]:
                    try:
                        callback(section, key, old_value, new_value)
                    except Exception as e:
                        self.logger.error(f"Error in change callback: {e}")
            
            # Global callbacks
            if "*" in self.change_callbacks:
                for callback in self.change_callbacks["*"]:
                    try:
                        callback(section, key, old_value, new_value)
                    except Exception as e:
                        self.logger.error(f"Error in global change callback: {e}")
            
        except Exception as e:
            self.logger.error(f"Error triggering change callbacks: {e}")
    
    def save_to_file(self, filename: str, format_type: ConfigFormat = ConfigFormat.YAML, 
                    sections: Optional[List[str]] = None) -> bool:
        """Save configuration to file."""
        try:
            filepath = self.config_dir / filename
            
            # Prepare data to save
            if sections:
                data = {section: self.config.get(section, {}) for section in sections}
            else:
                data = self.config.copy()
            
            # Encrypt sensitive data if needed
            if self.encryption_key and self.encrypted_keys:
                data = self._encrypt_sensitive_data(data)
            
            # Save to file
            with open(filepath, 'w', encoding='utf-8') as f:
                if format_type == ConfigFormat.JSON:
                    json.dump(data, f, indent=2, default=str)
                elif format_type == ConfigFormat.YAML:
                    yaml.dump(data, f, default_flow_style=False, allow_unicode=True)
                elif format_type == ConfigFormat.TOML and toml:
                    toml.dump(data, f)
                else:
                    raise ValueError(f"Unsupported format: {format_type}")
            
            self.logger.info(f"Configuration saved to: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving configuration: {e}")
            return False
    
    def _encrypt_sensitive_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Encrypt sensitive configuration data."""
        try:
            if not self.encryption_key or not Fernet:
                return data
            
            fernet = Fernet(self.encryption_key)
            encrypted_data = deepcopy(data)
            
            for key_path in self.encrypted_keys:
                parts = key_path.split('.')
                
                # Navigate to the value
                current = encrypted_data
                for part in parts[:-1]:
                    if part in current and isinstance(current[part], dict):
                        current = current[part]
                    else:
                        break
                else:
                    # Encrypt the value
                    final_key = parts[-1]
                    if final_key in current:
                        value = str(current[final_key])
                        encrypted_value = fernet.encrypt(value.encode()).decode()
                        current[final_key] = f"encrypted:{encrypted_value}"
            
            return encrypted_data
            
        except Exception as e:
            self.logger.error(f"Error encrypting sensitive data: {e}")
            return data
    
    def reload(self) -> bool:
        """Reload configuration from files."""
        try:
            # Clear current configuration
            with self.lock:
                self.config.clear()
                self.cached_configs.clear()
                self.cache_timestamps.clear()
            
            # Reload all configurations
            self._load_all_configs()
            
            self.logger.info("Configuration reloaded")
            return True
            
        except Exception as e:
            self.logger.error(f"Error reloading configuration: {e}")
            return False
    
    def get_change_history(self, section: Optional[str] = None, limit: int = 100) -> List[Dict[str, Any]]:
        """Get configuration change history."""
        try:
            changes = self.change_history
            
            if section:
                changes = [c for c in changes if c.section == section]
            
            # Sort by timestamp (most recent first) and limit
            changes = sorted(changes, key=lambda x: x.timestamp, reverse=True)[:limit]
            
            return [
                {
                    'timestamp': change.timestamp.isoformat(),
                    'section': change.section,
                    'key': change.key,
                    'old_value': change.old_value,
                    'new_value': change.new_value,
                    'source': change.source,
                    'user': change.user
                }
                for change in changes
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting change history: {e}")
            return []
    
    def validate_config(self, section: Optional[str] = None) -> Dict[str, List[str]]:
        """Validate configuration and return any errors."""
        try:
            errors = {}
            
            sections_to_validate = [section] if section else list(self.config.keys())
            
            for section_name in sections_to_validate:
                if section_name in self.config:
                    section_errors = []
                    
                    # Find matching schema
                    schema = None
                    for schema_enum, schema_obj in self.schemas.items():
                        if schema_enum.value == section_name:
                            schema = schema_obj
                            break
                    
                    if schema:
                        try:
                            self._validate_section(section_name, self.config[section_name], schema)
                        except ConfigValidationError as e:
                            section_errors.append(str(e))
                    
                    if section_errors:
                        errors[section_name] = section_errors
            
            return errors
            
        except Exception as e:
            self.logger.error(f"Error validating configuration: {e}")
            return {"validation_error": [str(e)]}
    
    def export_config(self, include_defaults: bool = True, include_sensitive: bool = False) -> Dict[str, Any]:
        """Export current configuration."""
        try:
            with self.lock:
                exported = deepcopy(self.config)
            
            if not include_sensitive and self.encrypted_keys:
                # Remove sensitive data
                for key_path in self.encrypted_keys:
                    parts = key_path.split('.')
                    current = exported
                    
                    for part in parts[:-1]:
                        if part in current and isinstance(current[part], dict):
                            current = current[part]
                        else:
                            break
                    else:
                        final_key = parts[-1]
                        if final_key in current:
                            current[final_key] = "[REDACTED]"
            
            # Add metadata
            exported['_metadata'] = {
                'export_timestamp': datetime.now().isoformat(),
                'environment': self.environment,
                'include_defaults': include_defaults,
                'include_sensitive': include_sensitive,
                'total_changes': len(self.change_history)
            }
            
            return exported
            
        except Exception as e:
            self.logger.error(f"Error exporting configuration: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get configuration manager status."""
        try:
            return {
                'environment': self.environment,
                'config_sections': list(self.config.keys()),
                'total_settings': sum(len(section) for section in self.config.values()),
                'schemas_loaded': len(self.schemas),
                'change_history_count': len(self.change_history),
                'cache_enabled': self.cache_enabled,
                'cached_items': len(self.cached_configs),
                'auto_reload': self.auto_reload,
                'encryption_enabled': self.encryption_key is not None,
                'encrypted_keys_count': len(self.encrypted_keys)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting status: {e}")
            return {}
    
    def get_section(self, section: ConfigSection) -> Dict[str, Any]:
        """Get configuration section by ConfigSection enum."""
        try:
            section_name = section.value
            return self.config.get(section_name, {})
        except Exception as e:
            self.logger.error(f"Error getting section {section}: {e}")
            return {}