"""Environment Management for AI Trading Agent.

This module provides environment-specific configuration management,
environment detection, and environment-based feature toggles.

Author: inkbytefo
"""

import os
import sys
import platform
import logging
from typing import Dict, Any, Optional, List, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
import json
import yaml
from datetime import datetime


class EnvironmentType(Enum):
    """Environment types."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"
    LOCAL = "local"
    DOCKER = "docker"
    KUBERNETES = "kubernetes"
    CLOUD = "cloud"


class DeploymentPlatform(Enum):
    """Deployment platforms."""
    LOCAL_MACHINE = "local_machine"
    DOCKER_CONTAINER = "docker_container"
    KUBERNETES_POD = "kubernetes_pod"
    AWS_EC2 = "aws_ec2"
    AWS_ECS = "aws_ecs"
    AWS_LAMBDA = "aws_lambda"
    GOOGLE_CLOUD = "google_cloud"
    AZURE = "azure"
    HEROKU = "heroku"
    DIGITAL_OCEAN = "digital_ocean"
    UNKNOWN = "unknown"


class FeatureFlag(Enum):
    """Feature flags for environment-based toggles."""
    DEBUG_MODE = "debug_mode"
    VERBOSE_LOGGING = "verbose_logging"
    PERFORMANCE_MONITORING = "performance_monitoring"
    ADVANCED_ANALYTICS = "advanced_analytics"
    EXPERIMENTAL_FEATURES = "experimental_features"
    LIVE_TRADING = "live_trading"
    PAPER_TRADING = "paper_trading"
    BACKTESTING = "backtesting"
    API_RATE_LIMITING = "api_rate_limiting"
    DATA_VALIDATION = "data_validation"
    SECURITY_CHECKS = "security_checks"
    AUTOMATED_DEPLOYMENT = "automated_deployment"
    HEALTH_CHECKS = "health_checks"
    METRICS_COLLECTION = "metrics_collection"
    ALERT_NOTIFICATIONS = "alert_notifications"


@dataclass
class SystemInfo:
    """System information."""
    platform: str
    architecture: str
    python_version: str
    hostname: str
    username: str
    cpu_count: int
    memory_total: Optional[int] = None
    disk_space: Optional[int] = None
    network_interfaces: List[str] = field(default_factory=list)
    environment_variables: Dict[str, str] = field(default_factory=dict)
    
    @classmethod
    def detect(cls) -> 'SystemInfo':
        """Detect current system information."""
        try:
            import psutil
            memory_total = psutil.virtual_memory().total
            disk_space = psutil.disk_usage('/').total if os.name != 'nt' else psutil.disk_usage('C:').total
            network_interfaces = list(psutil.net_if_addrs().keys())
        except ImportError:
            memory_total = None
            disk_space = None
            network_interfaces = []
        
        return cls(
            platform=platform.platform(),
            architecture=platform.architecture()[0],
            python_version=sys.version,
            hostname=platform.node(),
            username=os.getenv('USER', os.getenv('USERNAME', 'unknown')),
            cpu_count=os.cpu_count() or 1,
            memory_total=memory_total,
            disk_space=disk_space,
            network_interfaces=network_interfaces,
            environment_variables=dict(os.environ)
        )


@dataclass
class EnvironmentConfig:
    """Environment-specific configuration."""
    name: str
    type: EnvironmentType
    platform: DeploymentPlatform
    description: str = ""
    debug_enabled: bool = False
    testing_enabled: bool = False
    monitoring_enabled: bool = True
    logging_level: str = "INFO"
    feature_flags: Dict[FeatureFlag, bool] = field(default_factory=dict)
    resource_limits: Dict[str, Any] = field(default_factory=dict)
    security_settings: Dict[str, Any] = field(default_factory=dict)
    integration_settings: Dict[str, Any] = field(default_factory=dict)
    custom_settings: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize default feature flags if not provided."""
        if not self.feature_flags:
            self.feature_flags = self._get_default_feature_flags()
    
    def _get_default_feature_flags(self) -> Dict[FeatureFlag, bool]:
        """Get default feature flags based on environment type."""
        defaults = {
            FeatureFlag.DEBUG_MODE: False,
            FeatureFlag.VERBOSE_LOGGING: False,
            FeatureFlag.PERFORMANCE_MONITORING: True,
            FeatureFlag.ADVANCED_ANALYTICS: True,
            FeatureFlag.EXPERIMENTAL_FEATURES: False,
            FeatureFlag.LIVE_TRADING: False,
            FeatureFlag.PAPER_TRADING: True,
            FeatureFlag.BACKTESTING: True,
            FeatureFlag.API_RATE_LIMITING: True,
            FeatureFlag.DATA_VALIDATION: True,
            FeatureFlag.SECURITY_CHECKS: True,
            FeatureFlag.AUTOMATED_DEPLOYMENT: False,
            FeatureFlag.HEALTH_CHECKS: True,
            FeatureFlag.METRICS_COLLECTION: True,
            FeatureFlag.ALERT_NOTIFICATIONS: True
        }
        
        # Adjust defaults based on environment type
        if self.type == EnvironmentType.DEVELOPMENT:
            defaults.update({
                FeatureFlag.DEBUG_MODE: True,
                FeatureFlag.VERBOSE_LOGGING: True,
                FeatureFlag.EXPERIMENTAL_FEATURES: True,
                FeatureFlag.LIVE_TRADING: False,
                FeatureFlag.PAPER_TRADING: True
            })
        elif self.type == EnvironmentType.TESTING:
            defaults.update({
                FeatureFlag.DEBUG_MODE: True,
                FeatureFlag.VERBOSE_LOGGING: True,
                FeatureFlag.LIVE_TRADING: False,
                FeatureFlag.PAPER_TRADING: True,
                FeatureFlag.BACKTESTING: True
            })
        elif self.type == EnvironmentType.STAGING:
            defaults.update({
                FeatureFlag.DEBUG_MODE: False,
                FeatureFlag.VERBOSE_LOGGING: False,
                FeatureFlag.EXPERIMENTAL_FEATURES: False,
                FeatureFlag.LIVE_TRADING: False,
                FeatureFlag.PAPER_TRADING: True
            })
        elif self.type == EnvironmentType.PRODUCTION:
            defaults.update({
                FeatureFlag.DEBUG_MODE: False,
                FeatureFlag.VERBOSE_LOGGING: False,
                FeatureFlag.EXPERIMENTAL_FEATURES: False,
                FeatureFlag.LIVE_TRADING: True,
                FeatureFlag.PAPER_TRADING: False,
                FeatureFlag.AUTOMATED_DEPLOYMENT: True
            })
        
        return defaults
    
    def is_feature_enabled(self, feature: FeatureFlag) -> bool:
        """Check if a feature is enabled."""
        return self.feature_flags.get(feature, False)
    
    def enable_feature(self, feature: FeatureFlag):
        """Enable a feature flag."""
        self.feature_flags[feature] = True
    
    def disable_feature(self, feature: FeatureFlag):
        """Disable a feature flag."""
        self.feature_flags[feature] = False
    
    def toggle_feature(self, feature: FeatureFlag):
        """Toggle a feature flag."""
        self.feature_flags[feature] = not self.feature_flags.get(feature, False)


class EnvironmentManager:
    """Environment management system."""
    
    def __init__(self, config_dir: str = "config"):
        """Initialize environment manager."""
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        
        # Environment detection
        self.current_environment: Optional[EnvironmentConfig] = None
        self.system_info: SystemInfo = SystemInfo.detect()
        self.deployment_platform: DeploymentPlatform = self._detect_deployment_platform()
        
        # Environment configurations
        self.environments: Dict[str, EnvironmentConfig] = {}
        
        # Load environment configurations
        self._load_environment_configs()
        
        # Detect and set current environment
        self._detect_current_environment()
        
        self.logger.info(f"Environment manager initialized. Current environment: {self.get_current_environment_name()}")
    
    def _detect_deployment_platform(self) -> DeploymentPlatform:
        """Detect the current deployment platform."""
        try:
            # Check for Docker
            if os.path.exists('/.dockerenv') or os.getenv('DOCKER_CONTAINER'):
                return DeploymentPlatform.DOCKER_CONTAINER
            
            # Check for Kubernetes
            if (os.getenv('KUBERNETES_SERVICE_HOST') or 
                os.path.exists('/var/run/secrets/kubernetes.io')):
                return DeploymentPlatform.KUBERNETES_POD
            
            # Check for AWS
            if os.getenv('AWS_EXECUTION_ENV'):
                if 'lambda' in os.getenv('AWS_EXECUTION_ENV', '').lower():
                    return DeploymentPlatform.AWS_LAMBDA
                elif 'ecs' in os.getenv('AWS_EXECUTION_ENV', '').lower():
                    return DeploymentPlatform.AWS_ECS
                else:
                    return DeploymentPlatform.AWS_EC2
            
            # Check for Google Cloud
            if (os.getenv('GOOGLE_CLOUD_PROJECT') or 
                os.getenv('GCLOUD_PROJECT') or
                os.path.exists('/var/secrets/google')):
                return DeploymentPlatform.GOOGLE_CLOUD
            
            # Check for Azure
            if os.getenv('WEBSITE_SITE_NAME'):  # Azure App Service
                return DeploymentPlatform.AZURE
            
            # Check for Heroku
            if os.getenv('DYNO'):
                return DeploymentPlatform.HEROKU
            
            # Check for DigitalOcean
            if os.getenv('DIGITALOCEAN_APP_NAME'):
                return DeploymentPlatform.DIGITAL_OCEAN
            
            # Default to local machine
            return DeploymentPlatform.LOCAL_MACHINE
            
        except Exception as e:
            self.logger.warning(f"Error detecting deployment platform: {e}")
            return DeploymentPlatform.UNKNOWN
    
    def _load_environment_configs(self):
        """Load environment configurations from files."""
        try:
            # Ensure config directory exists
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            # Load default environments
            self._create_default_environments()
            
            # Load custom environment configurations
            env_config_file = self.config_dir / "environments.yaml"
            if env_config_file.exists():
                with open(env_config_file, 'r', encoding='utf-8') as f:
                    env_data = yaml.safe_load(f)
                
                for env_name, env_config in env_data.items():
                    self.environments[env_name] = EnvironmentConfig(
                        name=env_name,
                        type=EnvironmentType(env_config.get('type', 'development')),
                        platform=DeploymentPlatform(env_config.get('platform', 'local_machine')),
                        description=env_config.get('description', ''),
                        debug_enabled=env_config.get('debug_enabled', False),
                        testing_enabled=env_config.get('testing_enabled', False),
                        monitoring_enabled=env_config.get('monitoring_enabled', True),
                        logging_level=env_config.get('logging_level', 'INFO'),
                        feature_flags={FeatureFlag(k): v for k, v in env_config.get('feature_flags', {}).items()},
                        resource_limits=env_config.get('resource_limits', {}),
                        security_settings=env_config.get('security_settings', {}),
                        integration_settings=env_config.get('integration_settings', {}),
                        custom_settings=env_config.get('custom_settings', {})
                    )
            
        except Exception as e:
            self.logger.error(f"Error loading environment configurations: {e}")
    
    def _create_default_environments(self):
        """Create default environment configurations."""
        try:
            # Development environment
            self.environments['development'] = EnvironmentConfig(
                name='development',
                type=EnvironmentType.DEVELOPMENT,
                platform=self.deployment_platform,
                description='Development environment for local testing',
                debug_enabled=True,
                testing_enabled=True,
                monitoring_enabled=True,
                logging_level='DEBUG',
                resource_limits={
                    'max_memory_mb': 2048,
                    'max_cpu_percent': 80,
                    'max_disk_mb': 10240
                },
                security_settings={
                    'enable_encryption': False,
                    'require_authentication': False,
                    'enable_rate_limiting': False
                }
            )
            
            # Testing environment
            self.environments['testing'] = EnvironmentConfig(
                name='testing',
                type=EnvironmentType.TESTING,
                platform=self.deployment_platform,
                description='Testing environment for automated tests',
                debug_enabled=True,
                testing_enabled=True,
                monitoring_enabled=True,
                logging_level='INFO',
                resource_limits={
                    'max_memory_mb': 1024,
                    'max_cpu_percent': 60,
                    'max_disk_mb': 5120
                },
                security_settings={
                    'enable_encryption': True,
                    'require_authentication': False,
                    'enable_rate_limiting': True
                }
            )
            
            # Staging environment
            self.environments['staging'] = EnvironmentConfig(
                name='staging',
                type=EnvironmentType.STAGING,
                platform=self.deployment_platform,
                description='Staging environment for pre-production testing',
                debug_enabled=False,
                testing_enabled=False,
                monitoring_enabled=True,
                logging_level='INFO',
                resource_limits={
                    'max_memory_mb': 4096,
                    'max_cpu_percent': 70,
                    'max_disk_mb': 20480
                },
                security_settings={
                    'enable_encryption': True,
                    'require_authentication': True,
                    'enable_rate_limiting': True
                }
            )
            
            # Production environment
            self.environments['production'] = EnvironmentConfig(
                name='production',
                type=EnvironmentType.PRODUCTION,
                platform=self.deployment_platform,
                description='Production environment for live trading',
                debug_enabled=False,
                testing_enabled=False,
                monitoring_enabled=True,
                logging_level='WARNING',
                resource_limits={
                    'max_memory_mb': 8192,
                    'max_cpu_percent': 80,
                    'max_disk_mb': 51200
                },
                security_settings={
                    'enable_encryption': True,
                    'require_authentication': True,
                    'enable_rate_limiting': True,
                    'enable_audit_logging': True,
                    'enable_intrusion_detection': True
                },
                integration_settings={
                    'enable_monitoring_alerts': True,
                    'enable_performance_tracking': True,
                    'enable_error_reporting': True
                }
            )
            
        except Exception as e:
            self.logger.error(f"Error creating default environments: {e}")
    
    def _detect_current_environment(self):
        """Detect the current environment."""
        try:
            # Check environment variable first
            env_name = os.getenv('TRADING_AGENT_ENV', os.getenv('ENVIRONMENT', os.getenv('ENV')))
            
            if env_name and env_name in self.environments:
                self.current_environment = self.environments[env_name]
                return
            
            # Auto-detect based on system characteristics
            if self.deployment_platform == DeploymentPlatform.LOCAL_MACHINE:
                # Check if we're in a development setup
                if (os.path.exists('.git') or 
                    os.path.exists('requirements.txt') or 
                    os.path.exists('setup.py') or
                    'dev' in os.getcwd().lower()):
                    env_name = 'development'
                else:
                    env_name = 'production'
            elif self.deployment_platform in [DeploymentPlatform.DOCKER_CONTAINER, 
                                             DeploymentPlatform.KUBERNETES_POD]:
                # Check for staging indicators
                if (os.getenv('STAGE') == 'staging' or 
                    'staging' in os.getenv('HOSTNAME', '').lower()):
                    env_name = 'staging'
                else:
                    env_name = 'production'
            else:
                # Cloud platforms default to production
                env_name = 'production'
            
            # Set the detected environment
            if env_name in self.environments:
                self.current_environment = self.environments[env_name]
            else:
                # Fallback to development
                self.current_environment = self.environments['development']
            
        except Exception as e:
            self.logger.error(f"Error detecting current environment: {e}")
            # Fallback to development environment
            self.current_environment = self.environments.get('development')
    
    def get_current_environment(self) -> Optional[EnvironmentConfig]:
        """Get the current environment configuration."""
        return self.current_environment
    
    def get_current_environment_name(self) -> str:
        """Get the current environment name."""
        return self.current_environment.name if self.current_environment else 'unknown'
    
    def set_environment(self, environment_name: str) -> bool:
        """Set the current environment."""
        try:
            if environment_name in self.environments:
                self.current_environment = self.environments[environment_name]
                self.logger.info(f"Environment changed to: {environment_name}")
                return True
            else:
                self.logger.error(f"Environment '{environment_name}' not found")
                return False
        except Exception as e:
            self.logger.error(f"Error setting environment: {e}")
            return False
    
    def is_feature_enabled(self, feature: FeatureFlag) -> bool:
        """Check if a feature is enabled in the current environment."""
        if self.current_environment:
            return self.current_environment.is_feature_enabled(feature)
        return False
    
    def get_environment_setting(self, key: str, default: Any = None) -> Any:
        """Get an environment-specific setting."""
        if not self.current_environment:
            return default
        
        # Check custom settings first
        if key in self.current_environment.custom_settings:
            return self.current_environment.custom_settings[key]
        
        # Check standard settings
        if hasattr(self.current_environment, key):
            return getattr(self.current_environment, key)
        
        return default
    
    def get_resource_limit(self, resource: str) -> Optional[Any]:
        """Get a resource limit for the current environment."""
        if self.current_environment:
            return self.current_environment.resource_limits.get(resource)
        return None
    
    def get_security_setting(self, setting: str) -> Optional[Any]:
        """Get a security setting for the current environment."""
        if self.current_environment:
            return self.current_environment.security_settings.get(setting)
        return None
    
    def get_integration_setting(self, setting: str) -> Optional[Any]:
        """Get an integration setting for the current environment."""
        if self.current_environment:
            return self.current_environment.integration_settings.get(setting)
        return None
    
    def is_development(self) -> bool:
        """Check if running in development environment."""
        return (self.current_environment and 
                self.current_environment.type == EnvironmentType.DEVELOPMENT)
    
    def is_testing(self) -> bool:
        """Check if running in testing environment."""
        return (self.current_environment and 
                self.current_environment.type == EnvironmentType.TESTING)
    
    def is_staging(self) -> bool:
        """Check if running in staging environment."""
        return (self.current_environment and 
                self.current_environment.type == EnvironmentType.STAGING)
    
    def is_production(self) -> bool:
        """Check if running in production environment."""
        return (self.current_environment and 
                self.current_environment.type == EnvironmentType.PRODUCTION)
    
    def is_debug_enabled(self) -> bool:
        """Check if debug mode is enabled."""
        return (self.current_environment and 
                self.current_environment.debug_enabled)
    
    def get_logging_level(self) -> str:
        """Get the logging level for the current environment."""
        if self.current_environment:
            return self.current_environment.logging_level
        return 'INFO'
    
    def add_environment(self, config: EnvironmentConfig) -> bool:
        """Add a new environment configuration."""
        try:
            self.environments[config.name] = config
            self.logger.info(f"Environment '{config.name}' added")
            return True
        except Exception as e:
            self.logger.error(f"Error adding environment: {e}")
            return False
    
    def remove_environment(self, environment_name: str) -> bool:
        """Remove an environment configuration."""
        try:
            if environment_name in self.environments:
                # Don't allow removing the current environment
                if (self.current_environment and 
                    self.current_environment.name == environment_name):
                    self.logger.error("Cannot remove the current environment")
                    return False
                
                del self.environments[environment_name]
                self.logger.info(f"Environment '{environment_name}' removed")
                return True
            else:
                self.logger.error(f"Environment '{environment_name}' not found")
                return False
        except Exception as e:
            self.logger.error(f"Error removing environment: {e}")
            return False
    
    def list_environments(self) -> List[str]:
        """List all available environments."""
        return list(self.environments.keys())
    
    def save_environments(self) -> bool:
        """Save environment configurations to file."""
        try:
            env_config_file = self.config_dir / "environments.yaml"
            
            # Prepare data for saving
            env_data = {}
            for name, config in self.environments.items():
                env_data[name] = {
                    'type': config.type.value,
                    'platform': config.platform.value,
                    'description': config.description,
                    'debug_enabled': config.debug_enabled,
                    'testing_enabled': config.testing_enabled,
                    'monitoring_enabled': config.monitoring_enabled,
                    'logging_level': config.logging_level,
                    'feature_flags': {flag.value: enabled for flag, enabled in config.feature_flags.items()},
                    'resource_limits': config.resource_limits,
                    'security_settings': config.security_settings,
                    'integration_settings': config.integration_settings,
                    'custom_settings': config.custom_settings
                }
            
            # Save to file
            with open(env_config_file, 'w', encoding='utf-8') as f:
                yaml.dump(env_data, f, default_flow_style=False, allow_unicode=True)
            
            self.logger.info("Environment configurations saved")
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving environment configurations: {e}")
            return False
    
    def get_environment_info(self) -> Dict[str, Any]:
        """Get comprehensive environment information."""
        try:
            current_env = self.current_environment
            
            return {
                'current_environment': {
                    'name': current_env.name if current_env else 'unknown',
                    'type': current_env.type.value if current_env else 'unknown',
                    'platform': current_env.platform.value if current_env else 'unknown',
                    'description': current_env.description if current_env else '',
                    'debug_enabled': current_env.debug_enabled if current_env else False,
                    'logging_level': current_env.logging_level if current_env else 'INFO'
                },
                'deployment_platform': self.deployment_platform.value,
                'system_info': {
                    'platform': self.system_info.platform,
                    'architecture': self.system_info.architecture,
                    'python_version': self.system_info.python_version,
                    'hostname': self.system_info.hostname,
                    'username': self.system_info.username,
                    'cpu_count': self.system_info.cpu_count,
                    'memory_total': self.system_info.memory_total,
                    'disk_space': self.system_info.disk_space
                },
                'available_environments': list(self.environments.keys()),
                'feature_flags': {
                    flag.value: current_env.is_feature_enabled(flag) if current_env else False
                    for flag in FeatureFlag
                },
                'resource_limits': current_env.resource_limits if current_env else {},
                'security_settings': current_env.security_settings if current_env else {},
                'integration_settings': current_env.integration_settings if current_env else {}
            }
            
        except Exception as e:
            self.logger.error(f"Error getting environment info: {e}")
            return {}
    
    def validate_environment(self, environment_name: Optional[str] = None) -> List[str]:
        """Validate environment configuration."""
        try:
            errors = []
            
            env_config = (self.environments.get(environment_name) if environment_name 
                         else self.current_environment)
            
            if not env_config:
                errors.append(f"Environment '{environment_name or 'current'}' not found")
                return errors
            
            # Validate resource limits
            if env_config.resource_limits:
                max_memory = env_config.resource_limits.get('max_memory_mb')
                if max_memory and self.system_info.memory_total:
                    available_memory_mb = self.system_info.memory_total / (1024 * 1024)
                    if max_memory > available_memory_mb:
                        errors.append(f"Memory limit ({max_memory}MB) exceeds available memory ({available_memory_mb:.0f}MB)")
            
            # Validate feature flag combinations
            if (env_config.is_feature_enabled(FeatureFlag.LIVE_TRADING) and 
                env_config.is_feature_enabled(FeatureFlag.PAPER_TRADING)):
                errors.append("Live trading and paper trading cannot both be enabled")
            
            if (env_config.type == EnvironmentType.PRODUCTION and 
                env_config.is_feature_enabled(FeatureFlag.DEBUG_MODE)):
                errors.append("Debug mode should not be enabled in production")
            
            return errors
            
        except Exception as e:
            return [f"Validation error: {str(e)}"]


# Global environment manager instance
_environment_manager: Optional[EnvironmentManager] = None


def get_environment_manager() -> EnvironmentManager:
    """Get the global environment manager instance."""
    global _environment_manager
    if _environment_manager is None:
        _environment_manager = EnvironmentManager()
    return _environment_manager


def get_current_environment() -> Optional[EnvironmentConfig]:
    """Get the current environment configuration."""
    return get_environment_manager().get_current_environment()


def is_feature_enabled(feature: FeatureFlag) -> bool:
    """Check if a feature is enabled in the current environment."""
    return get_environment_manager().is_feature_enabled(feature)


def is_development() -> bool:
    """Check if running in development environment."""
    return get_environment_manager().is_development()


def is_production() -> bool:
    """Check if running in production environment."""
    return get_environment_manager().is_production()


def get_logging_level() -> str:
    """Get the logging level for the current environment."""
    return get_environment_manager().get_logging_level()