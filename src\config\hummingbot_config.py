"""Hummingbot Configuration Module.

This module provides configuration management for Hummingbot integration,
including API settings, exchange credentials, and trading parameters.

Author: inkbytefo
"""

import os
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from pathlib import Path
import yaml
from ..utils.logger import get_logger


@dataclass
class HummingbotAPIConfig:
    """Hummingbot API configuration."""
    host: str = "localhost"
    port: int = 8000
    username: str = "admin"
    password: str = "admin"
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_ssl: bool = False
    enable_ssl_verify: bool = False
    enable_auth: bool = True
    sandbox_mode: bool = False
    
    @property
    def base_url(self) -> str:
        """Get base API URL."""
        protocol = "https" if self.enable_ssl else "http"
        return f"{protocol}://{self.host}:{self.port}"


@dataclass
class GatewayConfig:
    """Hummingbot Gateway configuration."""
    host: str = "localhost"
    port: int = 15888
    enable_ssl: bool = True
    cert_path: Optional[str] = None
    passphrase: Optional[str] = None
    timeout: int = 30
    
    @property
    def base_url(self) -> str:
        """Get base Gateway URL."""
        protocol = "https" if self.enable_ssl else "http"
        return f"{protocol}://{self.host}:{self.port}"


@dataclass
class ExchangeConfig:
    """Exchange configuration."""
    name: str
    connector_type: str  # spot, perpetual, paper
    api_key: str
    api_secret: str
    passphrase: Optional[str] = None
    sandbox_mode: bool = False
    rate_limit: int = 1000
    enabled: bool = True
    trading_pairs: List[str] = field(default_factory=list)
    

@dataclass
class TradingConfig:
    """Trading configuration."""
    max_position_size: float = 1000.0
    max_orders_per_pair: int = 5
    min_order_amount: float = 10.0
    max_order_amount: float = 1000.0
    default_leverage: float = 1.0
    stop_loss_percentage: float = 0.05
    take_profit_percentage: float = 0.10
    order_refresh_time: int = 30
    enable_paper_trading: bool = False
    


    

class HummingbotConfigManager:
    """Hummingbot configuration manager."""
    
    def __init__(self, config_dir: Optional[str] = None):
        self.logger = get_logger(__name__)
        self.config_dir = Path(config_dir) if config_dir else Path("config")
        self.config_dir.mkdir(exist_ok=True)
        
        # Configuration files
        self.api_config_file = self.config_dir / "hummingbot_api.yaml"
        self.gateway_config_file = self.config_dir / "gateway.yaml"
        self.exchanges_config_file = self.config_dir / "exchanges.yaml"
        self.trading_config_file = self.config_dir / "trading.yaml"
        
        # Load configurations
        self.api_config = self._load_api_config()
        self.gateway_config = self._load_gateway_config()
        self.exchanges = self._load_exchanges_config()
        self.trading_config = self._load_trading_config()
    
    def _load_api_config(self) -> HummingbotAPIConfig:
        """Load API configuration."""
        if self.api_config_file.exists():
            try:
                with open(self.api_config_file, 'r') as f:
                    data = yaml.safe_load(f)
                return HummingbotAPIConfig(**data)
            except Exception as e:
                self.logger.error(f"Failed to load API config: {e}")
        
        # Create default config
        config = HummingbotAPIConfig()
        self._save_api_config(config)
        return config
    
    def _load_gateway_config(self) -> GatewayConfig:
        """Load Gateway configuration."""
        if self.gateway_config_file.exists():
            try:
                with open(self.gateway_config_file, 'r') as f:
                    data = yaml.safe_load(f)
                return GatewayConfig(**data)
            except Exception as e:
                self.logger.error(f"Failed to load Gateway config: {e}")
        
        # Create default config
        config = GatewayConfig()
        self._save_gateway_config(config)
        return config
    
    def _load_exchanges_config(self) -> Dict[str, ExchangeConfig]:
        """Load exchanges configuration."""
        if self.exchanges_config_file.exists():
            try:
                with open(self.exchanges_config_file, 'r') as f:
                    data = yaml.safe_load(f)
                
                # Override API credentials with environment variables for security
                exchanges = {}
                for name, config in data.items():
                    # Use dynamic environment variable names
                    env_api_key = os.getenv(f"{name.upper()}_API_KEY", "")
                    env_api_secret = os.getenv(f"{name.upper()}_API_SECRET", "")
                    
                    # If environment variables exist, use them; otherwise use config values
                    config['api_key'] = env_api_key if env_api_key else config.get('api_key', '')
                    config['api_secret'] = env_api_secret if env_api_secret else config.get('api_secret', '')
                    
                    exchanges[name] = ExchangeConfig(**config)
                
                return exchanges
            except Exception as e:
                self.logger.error(f"Failed to load exchanges config: {e}")
        
        # Create default config with environment variables
        exchanges = {
            "binance": ExchangeConfig(
                name="binance",
                connector_type="spot",
                api_key=os.getenv("BINANCE_API_KEY", ""),
                api_secret=os.getenv("BINANCE_API_SECRET", ""),
                trading_pairs=["BTC-USDT", "ETH-USDT"]
            )
        }
        self._save_exchanges_config(exchanges)
        return exchanges
    
    def _load_trading_config(self) -> TradingConfig:
        """Load trading configuration."""
        if self.trading_config_file.exists():
            try:
                with open(self.trading_config_file, 'r') as f:
                    data = yaml.safe_load(f)
                return TradingConfig(**data)
            except Exception as e:
                self.logger.error(f"Failed to load trading config: {e}")
        
        # Create default config
        config = TradingConfig()
        self._save_trading_config(config)
        return config
    

    
    def _save_api_config(self, config: HummingbotAPIConfig):
        """Save API configuration."""
        try:
            with open(self.api_config_file, 'w') as f:
                yaml.dump(config.__dict__, f, default_flow_style=False)
        except Exception as e:
            self.logger.error(f"Failed to save API config: {e}")
    
    def _save_gateway_config(self, config: GatewayConfig):
        """Save Gateway configuration."""
        try:
            with open(self.gateway_config_file, 'w') as f:
                yaml.dump(config.__dict__, f, default_flow_style=False)
        except Exception as e:
            self.logger.error(f"Failed to save Gateway config: {e}")
    
    def _save_exchanges_config(self, exchanges: Dict[str, ExchangeConfig]):
        """Save exchanges configuration."""
        try:
            data = {name: config.__dict__ for name, config in exchanges.items()}
            with open(self.exchanges_config_file, 'w') as f:
                yaml.dump(data, f, default_flow_style=False)
        except Exception as e:
            self.logger.error(f"Failed to save exchanges config: {e}")
    
    def _save_trading_config(self, config: TradingConfig):
        """Save trading configuration."""
        try:
            with open(self.trading_config_file, 'w') as f:
                yaml.dump(config.__dict__, f, default_flow_style=False)
        except Exception as e:
            self.logger.error(f"Failed to save trading config: {e}")
    

    
    def add_exchange(self, exchange_config: ExchangeConfig):
        """Add new exchange configuration."""
        self.exchanges[exchange_config.name] = exchange_config
        self._save_exchanges_config(self.exchanges)
        self.logger.info(f"Added exchange configuration: {exchange_config.name}")
    
    def remove_exchange(self, exchange_name: str):
        """Remove exchange configuration."""
        if exchange_name in self.exchanges:
            del self.exchanges[exchange_name]
            self._save_exchanges_config(self.exchanges)
            self.logger.info(f"Removed exchange configuration: {exchange_name}")
    
    def update_api_config(self, **kwargs):
        """Update API configuration."""
        for key, value in kwargs.items():
            if hasattr(self.api_config, key):
                setattr(self.api_config, key, value)
        self._save_api_config(self.api_config)
        self.logger.info("Updated API configuration")
    
    def update_trading_config(self, **kwargs):
        """Update trading configuration."""
        for key, value in kwargs.items():
            if hasattr(self.trading_config, key):
                setattr(self.trading_config, key, value)
        self._save_trading_config(self.trading_config)
        self.logger.info("Updated trading configuration")
    

    
    def get_api_config(self) -> HummingbotAPIConfig:
        """Get API configuration."""
        return self.api_config
    
    def get_gateway_config(self) -> GatewayConfig:
        """Get Gateway configuration."""
        return self.gateway_config
    
    def get_exchange_config(self, exchange_name: str) -> Optional[ExchangeConfig]:
        """Get exchange configuration by name."""
        return self.exchanges.get(exchange_name)
    
    def get_enabled_exchanges(self) -> List[ExchangeConfig]:
        """Get list of enabled exchanges."""
        return [config for config in self.exchanges.values() if config.enabled]
    
    def validate_config(self) -> bool:
        """Validate all configurations."""
        try:
            # Validate API config
            if not self.api_config.host or not self.api_config.port:
                self.logger.error("Invalid API configuration")
                return False
            
            # Validate exchanges
            for name, config in self.exchanges.items():
                if config.enabled and (not config.api_key or not config.api_secret):
                    self.logger.error(f"Invalid credentials for exchange: {name}")
                    return False
            
            # Validate trading config
            if self.trading_config.max_position_size <= 0:
                self.logger.error("Invalid trading configuration")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Configuration validation failed: {e}")
            return False
    
    def export_config(self, output_file: str):
        """Export all configurations to a single file."""
        try:
            config_data = {
                "api": self.api_config.__dict__,
                "gateway": self.gateway_config.__dict__,
                "exchanges": {name: config.__dict__ for name, config in self.exchanges.items()},
                "trading": self.trading_config.__dict__
            }
            
            with open(output_file, 'w') as f:
                yaml.dump(config_data, f, default_flow_style=False)
            
            self.logger.info(f"Configuration exported to: {output_file}")
            
        except Exception as e:
            self.logger.error(f"Failed to export configuration: {e}")


@dataclass
class RiskConfig:
    """Risk management configuration."""
    max_position_size: float = 1000.0
    max_daily_loss: float = 500.0
    max_drawdown: float = 0.1
    stop_loss_percentage: float = 0.02
    take_profit_percentage: float = 0.04
    max_open_orders: int = 5
    risk_per_trade: float = 0.01
    enable_risk_management: bool = True
    emergency_stop: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "max_position_size": self.max_position_size,
            "max_daily_loss": self.max_daily_loss,
            "max_drawdown": self.max_drawdown,
            "stop_loss_percentage": self.stop_loss_percentage,
            "take_profit_percentage": self.take_profit_percentage,
            "max_open_orders": self.max_open_orders,
            "risk_per_trade": self.risk_per_trade,
            "enable_risk_management": self.enable_risk_management,
            "emergency_stop": self.emergency_stop
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RiskConfig':
        """Create from dictionary."""
        return cls(
            max_position_size=data.get("max_position_size", 1000.0),
            max_daily_loss=data.get("max_daily_loss", 500.0),
            max_drawdown=data.get("max_drawdown", 0.1),
            stop_loss_percentage=data.get("stop_loss_percentage", 0.02),
            take_profit_percentage=data.get("take_profit_percentage", 0.04),
            max_open_orders=data.get("max_open_orders", 5),
            risk_per_trade=data.get("risk_per_trade", 0.01),
            enable_risk_management=data.get("enable_risk_management", True),
            emergency_stop=data.get("emergency_stop", False)
        )