"""Settings and Configuration Classes for AI Trading Agent.

This module provides structured settings classes for different components
of the trading system with validation and type safety.

Author: inkbytefo
"""

import os
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path


class Environment(Enum):
    """Application environments."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(Enum):
    """Logging levels."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ExchangeType(Enum):
    """Supported exchange types."""
    BINANCE = "binance"
    COINBASE = "coinbase"
    KRAKEN = "kraken"
    BYBIT = "bybit"
    OKEX = "okex"
    HUOBI = "huobi"
    KUCOIN = "kucoin"
    BITFINEX = "bitfinex"
    BITMEX = "bitmex"
    FTX = "ftx"
    DERIBIT = "deribit"
    GATE = "gate"


class ModelType(Enum):
    """AI model types."""
    LSTM = "lstm"
    TRANSFORMER = "transformer"
    RANDOM_FOREST = "random_forest"
    GRADIENT_BOOSTING = "gradient_boosting"
    ENSEMBLE = "ensemble"
    NEURAL_NETWORK = "neural_network"
    SVM = "svm"
    LINEAR_REGRESSION = "linear_regression"
    ARIMA = "arima"
    PROPHET = "prophet"


class DataSource(Enum):
    """Data source types."""
    EXCHANGE_API = "exchange_api"
    WEBSOCKET = "websocket"
    REST_API = "rest_api"
    DATABASE = "database"
    FILE = "file"
    EXTERNAL_API = "external_api"
    BLOOMBERG = "bloomberg"
    REUTERS = "reuters"
    ALPHA_VANTAGE = "alpha_vantage"
    YAHOO_FINANCE = "yahoo_finance"


class ExecutionAlgorithm(Enum):
    """Order execution algorithms."""
    MARKET = "market"
    LIMIT = "limit"
    TWAP = "twap"
    VWAP = "vwap"
    IMPLEMENTATION_SHORTFALL = "implementation_shortfall"
    ARRIVAL_PRICE = "arrival_price"
    PARTICIPATION_RATE = "participation_rate"
    ICEBERG = "iceberg"
    HIDDEN = "hidden"
    SMART_ROUTING = "smart_routing"


@dataclass
class GeneralSettings:
    """General application settings."""
    app_name: str = "AI Trading Agent"
    version: str = "1.0.0"
    environment: Environment = Environment.DEVELOPMENT
    debug: bool = False
    log_level: LogLevel = LogLevel.INFO
    timezone: str = "UTC"
    config_dir: str = "config"
    data_dir: str = "data"
    logs_dir: str = "logs"
    temp_dir: str = "temp"
    max_workers: int = 4
    enable_metrics: bool = True
    enable_monitoring: bool = True
    enable_alerts: bool = True
    loop_interval: int = 60  # Main loop interval in seconds
    
    def __post_init__(self):
        """Validate settings after initialization."""
        if self.max_workers < 1:
            raise ValueError("max_workers must be at least 1")
        
        # Ensure directories exist
        for dir_attr in ['config_dir', 'data_dir', 'logs_dir', 'temp_dir']:
            dir_path = Path(getattr(self, dir_attr))
            dir_path.mkdir(parents=True, exist_ok=True)


@dataclass
class TradingSettings:
    """Trading system settings."""
    default_exchange: ExchangeType = ExchangeType.BINANCE
    base_currency: str = "USDT"
    quote_currencies: List[str] = field(default_factory=lambda: ["BTC", "ETH", "BNB", "ADA", "DOT"])
    max_positions: int = 10
    max_position_size: float = 10000.0
    min_position_size: float = 10.0
    default_order_size: float = 1000.0
    slippage_tolerance: float = 0.001  # 0.1%
    commission_rate: float = 0.001  # 0.1%
    enable_short_selling: bool = False
    enable_margin_trading: bool = False
    enable_futures_trading: bool = False
    enable_options_trading: bool = False
    enable_paper_trading: bool = True
    trading_hours: Dict[str, str] = field(default_factory=lambda: {
        "start": "00:00",
        "end": "23:59",
        "timezone": "UTC"
    })
    blacklisted_symbols: List[str] = field(default_factory=list)
    whitelisted_symbols: List[str] = field(default_factory=list)
    
    # Decision making settings
    min_confidence_threshold: float = 0.6
    max_daily_trades: int = 20
    risk_tolerance: str = "medium"
    technical_weight: float = 0.35
    sentiment_weight: float = 0.25
    pattern_weight: float = 0.20
    prediction_weight: float = 0.20
    risk_weight: float = 0.30
    
    def __post_init__(self):
        """Validate trading settings."""
        if self.max_positions < 1:
            raise ValueError("max_positions must be at least 1")
        
        if self.max_position_size <= self.min_position_size:
            raise ValueError("max_position_size must be greater than min_position_size")
        
        if self.slippage_tolerance < 0 or self.slippage_tolerance > 1:
            raise ValueError("slippage_tolerance must be between 0 and 1")
        
        if self.commission_rate < 0 or self.commission_rate > 1:
            raise ValueError("commission_rate must be between 0 and 1")


@dataclass
class AISettings:
    """AI model and prediction settings."""
    model_type: ModelType = ModelType.ENSEMBLE
    prediction_horizon: int = 24  # hours
    confidence_threshold: float = 0.7
    retrain_interval: int = 24  # hours
    feature_count: int = 50
    lookback_period: int = 168  # hours (1 week)
    validation_split: float = 0.2
    test_split: float = 0.1
    batch_size: int = 32
    epochs: int = 100
    learning_rate: float = 0.001
    dropout_rate: float = 0.2
    early_stopping_patience: int = 10
    model_save_path: str = "models"
    enable_feature_selection: bool = True
    enable_hyperparameter_tuning: bool = True
    enable_ensemble_voting: bool = True
    ensemble_models: List[ModelType] = field(default_factory=lambda: [
        ModelType.LSTM,
        ModelType.RANDOM_FOREST,
        ModelType.GRADIENT_BOOSTING
    ])
    feature_engineering: Dict[str, bool] = field(default_factory=lambda: {
        "technical_indicators": True,
        "price_patterns": True,
        "volume_analysis": True,
        "sentiment_analysis": False,
        "news_analysis": False,
        "social_media": False
    })
    
    # Sentiment analysis settings
    sentiment: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "model_type": "vader",
        "confidence_threshold": 0.6,
        "sources": ["news", "social"],
        "update_interval": 300
    })
    
    # Pattern detection settings
    patterns: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "types": ["head_and_shoulders", "double_top", "double_bottom", "triangle", "flag"],
        "min_confidence": 0.7,
        "lookback_periods": 50,
        "timeframes": ["1h", "4h", "1d"]
    })
    
    # Prediction model settings
    prediction: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "model_types": ["lstm", "random_forest", "gradient_boosting"],
        "prediction_horizon": 24,
        "confidence_threshold": 0.7,
        "retrain_interval": 24,
        "feature_count": 50
    })
    
    # Analysis weights
    sentiment_weight: float = 0.25
    technical_weight: float = 0.35
    pattern_weight: float = 0.20
    prediction_weight: float = 0.20
    signal_cooldown_minutes: int = 15
    
    def __post_init__(self):
        """Validate AI settings."""
        if self.prediction_horizon < 1:
            raise ValueError("prediction_horizon must be at least 1")
        
        if self.confidence_threshold < 0 or self.confidence_threshold > 1:
            raise ValueError("confidence_threshold must be between 0 and 1")
        
        if self.validation_split < 0 or self.validation_split > 1:
            raise ValueError("validation_split must be between 0 and 1")
        
        if self.test_split < 0 or self.test_split > 1:
            raise ValueError("test_split must be between 0 and 1")
        
        if (self.validation_split + self.test_split) >= 1:
            raise ValueError("validation_split + test_split must be less than 1")


@dataclass
class RiskSettings:
    """Risk management settings."""
    max_portfolio_risk: float = 0.02  # 2% of portfolio
    max_position_risk: float = 0.005  # 0.5% of portfolio
    max_daily_loss: float = 0.01  # 1% of portfolio
    max_drawdown: float = 0.05  # 5% of portfolio
    position_size_limit: float = 0.20  # 20% max position size relative to portfolio
    max_open_orders: int = 10  # Maximum number of open orders
    max_concentration: float = 0.20  # 20% max single position concentration
    max_correlation: float = 0.80  # 80% max correlation between positions
    min_liquidity: float = 0.70  # 70% minimum liquidity score
    max_leverage: float = 2.0  # 2x max leverage
    var_confidence: float = 0.95  # 95% VaR
    var_horizon: int = 1  # days
    stress_test_scenarios: int = 1000
    correlation_threshold: float = 0.8
    concentration_limit: float = 0.3  # 30% max in single asset
    leverage_limit: float = 1.0  # No leverage by default
    stop_loss_percentage: float = 0.02  # 2%
    take_profit_percentage: float = 0.04  # 4%
    enable_stop_loss: bool = True
    enable_take_profit: bool = True
    enable_trailing_stop: bool = False
    enable_position_sizing: bool = True
    enable_correlation_check: bool = True
    enable_concentration_check: bool = True
    risk_free_rate: float = 0.02  # 2% annual
    
    def __post_init__(self):
        """Validate risk settings."""
        if self.max_portfolio_risk <= 0 or self.max_portfolio_risk > 1:
            raise ValueError("max_portfolio_risk must be between 0 and 1")
        
        if self.max_position_risk <= 0 or self.max_position_risk > self.max_portfolio_risk:
            raise ValueError("max_position_risk must be between 0 and max_portfolio_risk")
        
        if self.position_size_limit <= 0 or self.position_size_limit > 1:
            raise ValueError("position_size_limit must be between 0 and 1")
        
        if self.max_open_orders <= 0:
            raise ValueError("max_open_orders must be positive")
        
        if self.max_concentration <= 0 or self.max_concentration > 1:
            raise ValueError("max_concentration must be between 0 and 1")
        
        if self.max_correlation <= 0 or self.max_correlation > 1:
            raise ValueError("max_correlation must be between 0 and 1")
        
        if self.min_liquidity <= 0 or self.min_liquidity > 1:
            raise ValueError("min_liquidity must be between 0 and 1")
        
        if self.max_leverage <= 0:
            raise ValueError("max_leverage must be positive")
        
        if self.var_confidence <= 0 or self.var_confidence >= 1:
            raise ValueError("var_confidence must be between 0 and 1")
        
        if self.correlation_threshold <= 0 or self.correlation_threshold > 1:
            raise ValueError("correlation_threshold must be between 0 and 1")


@dataclass
class DataSettings:
    """Data source and management settings."""
    primary_source: DataSource = DataSource.EXCHANGE_API
    backup_sources: List[DataSource] = field(default_factory=lambda: [
        DataSource.WEBSOCKET,
        DataSource.REST_API
    ])
    update_frequency: int = 60  # seconds
    cache_duration: int = 300  # seconds
    max_cache_size: int = 1000  # number of records
    enable_data_validation: bool = True
    enable_data_cleaning: bool = True
    enable_outlier_detection: bool = True
    data_retention_days: int = 365
    database_url: Optional[str] = None
    redis_url: Optional[str] = None
    enable_compression: bool = True
    compression_algorithm: str = "gzip"
    data_quality_checks: Dict[str, bool] = field(default_factory=lambda: {
        "completeness": True,
        "accuracy": True,
        "consistency": True,
        "timeliness": True,
        "validity": True
    })
    api_rate_limits: Dict[str, int] = field(default_factory=lambda: {
        "requests_per_second": 10,
        "requests_per_minute": 600,
        "requests_per_hour": 36000
    })
    
    # Data source specific settings
    news: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "sources": {
            "coindesk": True,
            "cointelegraph": True,
            "decrypt": True
        },
        "update_interval": 300,
        "max_articles": 100
    })
    
    market: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "symbols": ["BTC/USDT", "ETH/USDT", "BNB/USDT"],
        "timeframes": ["1m", "5m", "15m", "1h", "4h", "1d"],
        "update_interval": 60
    })
    
    social: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "platforms": ["twitter", "reddit", "telegram"],
        "keywords": ["bitcoin", "ethereum", "crypto"],
        "update_interval": 180
    })
    
    technical: Dict[str, Any] = field(default_factory=lambda: {
        "enabled": True,
        "indicators": ["RSI", "MACD", "BB", "SMA", "EMA"],
        "update_interval": 120
    })
    
    # Update intervals for different data sources
    update_intervals: Dict[str, int] = field(default_factory=lambda: {
        "news": 300,
        "market": 60,
        "social": 180,
        "technical": 120
    })
    

    
    def __post_init__(self):
        """Validate data settings."""
        if self.update_frequency < 1:
            raise ValueError("update_frequency must be at least 1 second")
        
        if self.cache_duration < 0:
            raise ValueError("cache_duration must be non-negative")
        
        if self.data_retention_days < 1:
            raise ValueError("data_retention_days must be at least 1")


@dataclass
class ExecutionSettings:
    """Trade execution settings."""
    default_venue: ExchangeType = ExchangeType.BINANCE
    backup_venues: List[ExchangeType] = field(default_factory=lambda: [
        ExchangeType.COINBASE,
        ExchangeType.KRAKEN
    ])
    execution_algorithm: ExecutionAlgorithm = ExecutionAlgorithm.TWAP
    order_timeout: int = 300  # seconds
    max_order_size: float = 100000.0
    min_order_size: float = 10.0
    enable_smart_routing: bool = True
    enable_order_splitting: bool = True
    max_order_splits: int = 10
    commission_model: str = "percentage"
    enable_pre_trade_checks: bool = True
    enable_post_trade_analysis: bool = True
    sandbox_mode: bool = True
    latency_threshold: float = 100.0  # milliseconds
    fill_ratio_threshold: float = 0.95  # 95%
    slippage_threshold: float = 0.002  # 0.2%
    venue_selection_criteria: Dict[str, float] = field(default_factory=lambda: {
        "liquidity": 0.4,
        "spread": 0.3,
        "latency": 0.2,
        "fees": 0.1
    })
    
    # Additional execution parameters
    max_slippage: float = 0.005  # 0.5%
    order_timeout_seconds: int = 300
    retry_attempts: int = 3
    execution_delay_seconds: float = 0.1
    venue_timeout_seconds: int = 30
    smart_routing_enabled: bool = True
    
    # Risk limits
    max_daily_volume: float = 1000000.0  # $1M daily volume limit
    max_position_size: float = 100000.0  # $100K max position size
    max_orders_per_minute: int = 60
    max_venues_per_order: int = 3
    
    def __post_init__(self):
        """Validate execution settings."""
        if self.order_timeout < 1:
            raise ValueError("order_timeout must be at least 1 second")
        
        if self.max_order_size <= self.min_order_size:
            raise ValueError("max_order_size must be greater than min_order_size")
        
        if self.max_order_splits < 1:
            raise ValueError("max_order_splits must be at least 1")
        
        # Validate venue selection criteria weights sum to 1
        total_weight = sum(self.venue_selection_criteria.values())
        if abs(total_weight - 1.0) > 0.001:
            raise ValueError("venue_selection_criteria weights must sum to 1.0")


@dataclass
class MonitoringSettings:
    """System monitoring settings."""
    metrics_interval: int = 60  # seconds
    health_check_interval: int = 30  # seconds
    performance_tracking: bool = True
    system_monitoring: bool = True
    enable_profiling: bool = False
    enable_memory_monitoring: bool = True
    enable_cpu_monitoring: bool = True
    enable_disk_monitoring: bool = True
    enable_network_monitoring: bool = True
    max_log_file_size: int = 100  # MB
    max_log_files: int = 10
    log_rotation_interval: str = "daily"
    metrics_retention_days: int = 30
    alert_thresholds: Dict[str, float] = field(default_factory=lambda: {
        "cpu_usage": 80.0,
        "memory_usage": 85.0,
        "disk_usage": 90.0,
        "error_rate": 5.0,
        "latency_p95": 1000.0  # milliseconds
    })
    dashboard_refresh_interval: int = 5  # seconds
    
    # Additional monitoring parameters
    monitoring_interval_seconds: int = 60
    health_check_interval_seconds: int = 30
    cleanup_interval_hours: int = 24
    retention_days: int = 30
    cpu_warning_threshold: float = 70.0
    cpu_critical_threshold: float = 90.0
    memory_warning_threshold: float = 75.0
    memory_critical_threshold: float = 90.0
    disk_warning_threshold: float = 80.0
    disk_critical_threshold: float = 95.0
    response_time_warning_ms: int = 500
    response_time_critical_ms: int = 1000
    error_rate_warning_threshold: float = 5.0
    error_rate_critical_threshold: float = 10.0
    gpu_warning_threshold: float = 80.0
    gpu_critical_threshold: float = 95.0
    alert_cooldown_seconds: int = 300
    
    def __post_init__(self):
        """Validate monitoring settings."""
        if self.metrics_interval < 1:
            raise ValueError("metrics_interval must be at least 1 second")
        
        if self.health_check_interval < 1:
            raise ValueError("health_check_interval must be at least 1 second")
        
        if self.max_log_file_size < 1:
            raise ValueError("max_log_file_size must be at least 1 MB")


@dataclass
class AlertSettings:
    """Alert and notification settings."""
    enable_email_alerts: bool = False
    enable_sms_alerts: bool = False
    enable_slack_alerts: bool = False
    enable_webhook_alerts: bool = False
    enable_console_alerts: bool = True
    email_smtp_server: Optional[str] = None
    email_smtp_port: int = 587
    email_username: Optional[str] = None
    email_password: Optional[str] = None
    email_recipients: List[str] = field(default_factory=list)
    slack_webhook_url: Optional[str] = None
    slack_channel: str = "#trading-alerts"
    webhook_urls: List[str] = field(default_factory=list)
    alert_cooldown: int = 300  # seconds
    max_alerts_per_hour: int = 10
    alert_severity_levels: List[str] = field(default_factory=lambda: [
        "LOW", "MEDIUM", "HIGH", "CRITICAL"
    ])
    escalation_rules: Dict[str, int] = field(default_factory=lambda: {
        "HIGH": 900,  # 15 minutes
        "CRITICAL": 300  # 5 minutes
    })
    
    def __post_init__(self):
        """Validate alert settings."""
        if self.alert_cooldown < 0:
            raise ValueError("alert_cooldown must be non-negative")
        
        if self.max_alerts_per_hour < 1:
            raise ValueError("max_alerts_per_hour must be at least 1")


@dataclass
class SecuritySettings:
    """Security and authentication settings."""
    enable_encryption: bool = True
    encryption_algorithm: str = "AES-256"
    enable_api_key_rotation: bool = True
    api_key_rotation_interval: int = 30  # days
    enable_rate_limiting: bool = True
    max_requests_per_minute: int = 100
    enable_ip_whitelist: bool = False
    allowed_ips: List[str] = field(default_factory=list)
    enable_2fa: bool = False
    session_timeout: int = 3600  # seconds
    password_min_length: int = 12
    password_require_special_chars: bool = True
    password_require_numbers: bool = True
    password_require_uppercase: bool = True
    enable_audit_logging: bool = True
    audit_log_retention_days: int = 90
    
    def __post_init__(self):
        """Validate security settings."""
        if self.api_key_rotation_interval < 1:
            raise ValueError("api_key_rotation_interval must be at least 1 day")
        
        if self.session_timeout < 60:
            raise ValueError("session_timeout must be at least 60 seconds")
        
        if self.password_min_length < 8:
            raise ValueError("password_min_length must be at least 8")


@dataclass
class PerformanceSettings:
    """Performance optimization settings."""
    enable_caching: bool = True
    cache_size_limit: int = 1000  # MB
    enable_connection_pooling: bool = True
    max_connections: int = 100
    connection_timeout: int = 30  # seconds
    enable_compression: bool = True
    enable_async_processing: bool = True
    max_async_workers: int = 10
    enable_batch_processing: bool = True
    batch_size: int = 100
    enable_lazy_loading: bool = True
    enable_query_optimization: bool = True
    enable_memory_optimization: bool = True
    gc_threshold: int = 1000  # objects
    
    def __post_init__(self):
        """Validate performance settings."""
        if self.cache_size_limit < 1:
            raise ValueError("cache_size_limit must be at least 1 MB")
        
        if self.max_connections < 1:
            raise ValueError("max_connections must be at least 1")
        
        if self.max_async_workers < 1:
            raise ValueError("max_async_workers must be at least 1")


@dataclass
class IntegrationSettings:
    """External integration settings."""
    enable_hummingbot: bool = False
    hummingbot_host: str = "localhost"
    hummingbot_port: int = 8080
    hummingbot_api_key: Optional[str] = None
    enable_tradingview: bool = False
    tradingview_webhook_url: Optional[str] = None
    enable_discord_bot: bool = False
    discord_bot_token: Optional[str] = None
    discord_channel_id: Optional[str] = None
    enable_telegram_bot: bool = False
    telegram_bot_token: Optional[str] = None
    telegram_chat_id: Optional[str] = None
    enable_mt4_bridge: bool = False
    mt4_server_host: str = "localhost"
    mt4_server_port: int = 9090
    enable_api_server: bool = True
    api_server_host: str = "0.0.0.0"
    api_server_port: int = 8000
    enable_web_interface: bool = True
    web_interface_port: int = 3000
    
    def __post_init__(self):
        """Validate integration settings."""
        if self.hummingbot_port < 1 or self.hummingbot_port > 65535:
            raise ValueError("hummingbot_port must be between 1 and 65535")
        
        if self.api_server_port < 1 or self.api_server_port > 65535:
            raise ValueError("api_server_port must be between 1 and 65535")
        
        if self.web_interface_port < 1 or self.web_interface_port > 65535:
            raise ValueError("web_interface_port must be between 1 and 65535")


@dataclass
class Settings:
    """Complete application settings."""
    general: GeneralSettings = field(default_factory=GeneralSettings)
    trading: TradingSettings = field(default_factory=TradingSettings)
    ai: AISettings = field(default_factory=AISettings)
    risk: RiskSettings = field(default_factory=RiskSettings)
    data: DataSettings = field(default_factory=DataSettings)
    execution: ExecutionSettings = field(default_factory=ExecutionSettings)
    monitoring: MonitoringSettings = field(default_factory=MonitoringSettings)
    alerts: AlertSettings = field(default_factory=AlertSettings)
    security: SecuritySettings = field(default_factory=SecuritySettings)
    performance: PerformanceSettings = field(default_factory=PerformanceSettings)
    integrations: IntegrationSettings = field(default_factory=IntegrationSettings)
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Settings':
        """Create Settings from dictionary."""
        settings = cls()
        
        # Update each section if present in data
        if 'general' in data:
            settings.general = GeneralSettings(**data['general'])
        
        if 'trading' in data:
            settings.trading = TradingSettings(**data['trading'])
        
        if 'ai' in data:
            settings.ai = AISettings(**data['ai'])
        
        if 'risk' in data:
            settings.risk = RiskSettings(**data['risk'])
        
        if 'data' in data:
            settings.data = DataSettings(**data['data'])
        
        if 'execution' in data:
            settings.execution = ExecutionSettings(**data['execution'])
        
        if 'monitoring' in data:
            settings.monitoring = MonitoringSettings(**data['monitoring'])
        
        if 'alerts' in data:
            settings.alerts = AlertSettings(**data['alerts'])
        
        if 'security' in data:
            settings.security = SecuritySettings(**data['security'])
        
        if 'performance' in data:
            settings.performance = PerformanceSettings(**data['performance'])
        
        if 'integrations' in data:
            settings.integrations = IntegrationSettings(**data['integrations'])
        
        return settings
    
    @classmethod
    def from_config(cls, config: Dict[str, Any]) -> 'Settings':
        """Create Settings from configuration dictionary."""
        return cls.from_dict(config)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert Settings to dictionary."""
        return {
            'general': self.general.__dict__,
            'trading': self.trading.__dict__,
            'ai': self.ai.__dict__,
            'risk': self.risk.__dict__,
            'data': self.data.__dict__,
            'execution': self.execution.__dict__,
            'monitoring': self.monitoring.__dict__,
            'alerts': self.alerts.__dict__,
            'security': self.security.__dict__,
            'performance': self.performance.__dict__,
            'integrations': self.integrations.__dict__
        }
    
    def validate(self) -> List[str]:
        """Validate all settings and return any errors."""
        errors = []
        
        try:
            # Each dataclass validates itself in __post_init__
            # This method can add cross-section validation
            
            # Example: Check if AI prediction horizon is compatible with trading frequency
            if self.ai.prediction_horizon < (self.data.update_frequency / 3600):
                errors.append("AI prediction horizon should be longer than data update frequency")
            
            # Check if risk limits are reasonable
            if self.risk.max_position_risk * self.trading.max_positions > self.risk.max_portfolio_risk:
                errors.append("Maximum total position risk exceeds portfolio risk limit")
            
            # Check if monitoring intervals are reasonable
            if self.monitoring.health_check_interval > self.monitoring.metrics_interval:
                errors.append("Health check interval should not be longer than metrics interval")
            
        except Exception as e:
            errors.append(f"Validation error: {str(e)}")
        
        return errors
    
    def get_environment_overrides(self) -> Dict[str, Any]:
        """Get settings that can be overridden by environment variables."""
        return {
            'TRADING_AGENT_DEBUG': self.general.debug,
            'TRADING_AGENT_LOG_LEVEL': self.general.log_level.value,
            'TRADING_AGENT_ENVIRONMENT': self.general.environment.value,
            'TRADING_AGENT_DEFAULT_EXCHANGE': self.trading.default_exchange.value,
            'TRADING_AGENT_BASE_CURRENCY': self.trading.base_currency,
            'TRADING_AGENT_MAX_POSITIONS': self.trading.max_positions,
            'TRADING_AGENT_AI_MODEL_TYPE': self.ai.model_type.value,
            'TRADING_AGENT_CONFIDENCE_THRESHOLD': self.ai.confidence_threshold,
            'TRADING_AGENT_MAX_PORTFOLIO_RISK': self.risk.max_portfolio_risk,
            'TRADING_AGENT_ENABLE_MONITORING': self.monitoring.system_monitoring,
            'TRADING_AGENT_API_SERVER_PORT': self.integrations.api_server_port
        }
    
    def apply_environment_overrides(self):
        """Apply environment variable overrides to settings."""
        env_overrides = self.get_environment_overrides()
        
        for env_var, current_value in env_overrides.items():
            env_value = os.getenv(env_var)
            if env_value is not None:
                try:
                    # Convert environment value to appropriate type
                    if isinstance(current_value, bool):
                        new_value = env_value.lower() in ('true', '1', 'yes', 'on')
                    elif isinstance(current_value, int):
                        new_value = int(env_value)
                    elif isinstance(current_value, float):
                        new_value = float(env_value)
                    elif isinstance(current_value, Enum):
                        # Handle enum types
                        enum_class = type(current_value)
                        new_value = enum_class(env_value)
                    else:
                        new_value = env_value
                    
                    # Apply the override
                    self._set_nested_value(env_var, new_value)
                    
                except (ValueError, TypeError) as e:
                    print(f"Warning: Could not apply environment override {env_var}={env_value}: {e}")
    
    def _set_nested_value(self, env_var: str, value: Any):
        """Set nested value based on environment variable name."""
        # Map environment variables to nested attributes
        mapping = {
            'TRADING_AGENT_DEBUG': ('general', 'debug'),
            'TRADING_AGENT_LOG_LEVEL': ('general', 'log_level'),
            'TRADING_AGENT_ENVIRONMENT': ('general', 'environment'),
            'TRADING_AGENT_DEFAULT_EXCHANGE': ('trading', 'default_exchange'),
            'TRADING_AGENT_BASE_CURRENCY': ('trading', 'base_currency'),
            'TRADING_AGENT_MAX_POSITIONS': ('trading', 'max_positions'),
            'TRADING_AGENT_AI_MODEL_TYPE': ('ai', 'model_type'),
            'TRADING_AGENT_CONFIDENCE_THRESHOLD': ('ai', 'confidence_threshold'),
            'TRADING_AGENT_MAX_PORTFOLIO_RISK': ('risk', 'max_portfolio_risk'),
            'TRADING_AGENT_ENABLE_MONITORING': ('monitoring', 'system_monitoring'),
            'TRADING_AGENT_API_SERVER_PORT': ('integrations', 'api_server_port')
        }
        
        if env_var in mapping:
            section, attr = mapping[env_var]
            section_obj = getattr(self, section)
            setattr(section_obj, attr, value)


# Default settings instance
default_settings = Settings()