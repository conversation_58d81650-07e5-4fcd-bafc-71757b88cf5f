#!/usr/bin/env python3
"""
AI Trading Agent - Core Agent Class

Author: inkbytefo
Description: Main orchestrator for the AI trading system
"""

import asyncio
import logging
import traceback
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta

from src.data.collector import DataCollector
from src.ai.analysis_engine import AnalysisEngine
from src.decision.decision_manager import DecisionManager
from src.execution.execution_manager import ExecutionManager, ExecutionRequest, OrderSide
from src.monitoring.performance_monitor import PerformanceMonitor
from src.monitoring.risk_monitor import RiskMonitor
from src.monitoring.system_monitor import MonitoringDashboard
from src.decision.risk_manager import RiskLevel
from src.config.settings import Settings
from src.utils.common import get_logger, PerformanceTimer, retry_async
from src.utils.base_classes import BaseComponent, ComponentStatus


class TradingAgent(BaseComponent):
    """Main AI Trading Agent that orchestrates all components."""
    
    def __init__(self, settings: Settings):
        super().__init__()
        self.settings = settings
        self.logger = get_logger(__name__)
        self.is_running = False
        
        # Initialize components
        self._initialize_components()
        
        # State tracking
        self.portfolio_state: Dict[str, Any] = {}
        self.last_analysis: Dict[str, Any] = {}
        self.last_decision: Dict[str, Any] = {}
        
        # Concurrency protection
        self._portfolio_lock = asyncio.Lock()
        
        # Performance tracking
        self._performance_timer = PerformanceTimer()
        
    def _initialize_components(self) -> None:
        """Initialize all trading components."""
        try:
            self.data_collector = DataCollector(self.settings.data)
            self.analysis_engine = AnalysisEngine(self.settings.ai)
            self.decision_manager = DecisionManager(self.settings.trading, self.settings.risk)
            self.execution_manager = ExecutionManager(self.settings.execution)
            self.risk_monitor = RiskMonitor(self.settings.risk)
            
            monitoring_config = (
                self.settings.monitoring.__dict__ 
                if hasattr(self.settings.monitoring, '__dict__') 
                else self.settings.monitoring
            )
            self.monitoring = MonitoringDashboard(monitoring_config)
            self.dashboard = self.monitoring  # Alias for dashboard access
            
            self.status = ComponentStatus.INITIALIZED
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Failed to initialize components: {e}")
            raise
        
    @retry_async(max_attempts=3, delay=1.0)
    async def start(self) -> None:
        """Start the AI trading agent with retry mechanism."""
        if self.status == ComponentStatus.RUNNING:
            self.logger.warning("Agent is already running")
            return
            
        self.logger.info("Starting AI Trading Agent components...")
        self.status = ComponentStatus.STARTING
        
        try:
            with self._performance_timer.measure("agent_startup"):
                # Start all components in order
                components = [
                    ("data_collector", self.data_collector),
                    ("analysis_engine", self.analysis_engine),
                    ("decision_manager", self.decision_manager),
                    ("execution_manager", self.execution_manager),
                    ("risk_monitor", self.risk_monitor),
                    ("monitoring", self.monitoring)
                ]
                
                for name, component in components:
                    self.logger.debug(f"Starting {name}...")
                    await component.start()
                    self.logger.debug(f"{name} started successfully")
                
                # Configure sandbox mode if detected
                await self._configure_sandbox_mode()
                
                self.is_running = True
                self.status = ComponentStatus.RUNNING
                
                # Start main loop
                asyncio.create_task(self._main_loop())
                
                startup_time = self._performance_timer.get_duration("agent_startup")
                self.logger.info(f"AI Trading Agent started successfully in {startup_time:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Failed to start AI Trading Agent: {e}")
            await self._cleanup_on_error()
            raise
    
    async def stop(self) -> None:
        """Stop the AI trading agent gracefully."""
        if self.status == ComponentStatus.STOPPED:
            self.logger.warning("Agent is already stopped")
            return
            
        self.logger.info("Stopping AI Trading Agent...")
        self.status = ComponentStatus.STOPPING
        self.is_running = False
        
        try:
            with self._performance_timer.measure("agent_shutdown"):
                # Stop components in reverse order
                components = [
                    ("monitoring", self.monitoring),
                    ("risk_monitor", self.risk_monitor),
                    ("execution_manager", self.execution_manager),
                    ("decision_manager", self.decision_manager),
                    ("analysis_engine", self.analysis_engine),
                    ("data_collector", self.data_collector)
                ]
                
                for name, component in components:
                    try:
                        self.logger.debug(f"Stopping {name}...")
                        await component.stop()
                        self.logger.debug(f"{name} stopped successfully")
                    except Exception as e:
                        self.logger.error(f"Error stopping {name}: {e}")
                
                self.status = ComponentStatus.STOPPED
                shutdown_time = self._performance_timer.get_duration("agent_shutdown")
                self.logger.info(f"AI Trading Agent stopped in {shutdown_time:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Error during shutdown: {e}")
            raise
    
    async def _main_loop(self) -> None:
        """Main processing loop with enhanced error handling and performance monitoring."""
        self.logger.info("Starting main processing loop")
        loop_count = 0
        
        while self.is_running:
            loop_count += 1
            
            try:
                with self._performance_timer.measure(f"main_loop_{loop_count}"):
                    # Get latest market data
                    market_data = await self._get_market_data()
                    
                    if market_data:
                        # Update portfolio state and assess risk
                        risk_assessment = await self._update_portfolio_and_assess_risk()
                        
                        # Check for emergency conditions
                        emergency_triggered = await self._check_emergency_conditions(risk_assessment)
                        
                        if not emergency_triggered:
                            # Normal trading operations
                            await self._execute_trading_cycle(market_data)
                        
                        # Update monitoring metrics
                        await self._update_monitoring_metrics()
                    
                    # Performance logging every 100 iterations
                    if loop_count % 100 == 0:
                        avg_time = self._performance_timer.get_average_duration("main_loop")
                        self.logger.info(f"Main loop performance: {avg_time:.3f}s average over {loop_count} iterations")
                    
                    self.logger.debug(f"Main loop iteration {loop_count} completed")
                
                # Wait before next iteration
                await asyncio.sleep(self.settings.general.loop_interval)
                
            except Exception as e:
                await self._handle_main_loop_error(e, loop_count)
                

    
    async def _get_market_data(self) -> Optional[Dict[str, Any]]:
        """Get latest market data with error handling."""
        try:
            return await self.data_collector.get_latest_data()
        except Exception as e:
            self.logger.error(f"Failed to get market data: {e}")
            return None
    
    async def _update_portfolio_and_assess_risk(self) -> Any:
        """Update portfolio state and perform risk assessment."""
        async with self._portfolio_lock:
            self.portfolio_state = await self.execution_manager.get_portfolio_state()
            
            # Validate portfolio state synchronization
            portfolio_timestamp = self.portfolio_state.get('timestamp', 'unknown')
            portfolio_total_value = self.portfolio_state.get('total_value', 0.0)
            self.logger.debug(f'Portfolio synchronized at {portfolio_timestamp}. Value: {portfolio_total_value}')
            
            # Perform real-time risk assessment
            return await self.risk_monitor.assess_portfolio_risk(self.portfolio_state)
    
    async def _check_emergency_conditions(self, risk_assessment: Any) -> bool:
        """Check for emergency conditions and trigger halt if necessary."""
        is_risk_limit_breached, reason = self._evaluate_risk_limits(risk_assessment)
        
        # Trigger emergency halt if risk level is critical or limits breached
        if (risk_assessment.overall_level in [RiskLevel.CRITICAL, RiskLevel.EXTREME] or 
            is_risk_limit_breached):
            
            if not reason:
                reason = f"Overall risk level is {risk_assessment.overall_level.name}"
            
            await self._trigger_emergency_halt(reason, risk_assessment.risk_score)
            return True
        
        return False
    
    def _evaluate_risk_limits(self, risk_assessment: Any) -> tuple[bool, str]:
        """Evaluate if any risk limits are breached."""
        risk_settings = self.risk_monitor.risk_settings
        
        # Check for max drawdown breach
        if (hasattr(risk_assessment, 'current_drawdown') and 
            risk_assessment.current_drawdown > risk_settings.max_drawdown):
            return True, (
                f"Max drawdown limit breached. "
                f"Current: {risk_assessment.current_drawdown:.2%}, "
                f"Limit: {risk_settings.max_drawdown:.2%}"
            )
        
        # Check for max daily loss breach
        if (hasattr(risk_assessment, 'daily_loss') and 
            risk_assessment.daily_loss > risk_settings.max_daily_loss):
            return True, (
                f"Max daily loss limit breached. "
                f"Current: {risk_assessment.daily_loss:.2f}, "
                f"Limit: {risk_settings.max_daily_loss:.2f}"
            )
        
        return False, ""
    
    async def _trigger_emergency_halt(self, reason: str, risk_score: float) -> None:
        """Trigger emergency halt procedures."""
        self.logger.critical(
            f"EMERGENCY HALT TRIGGERED: {reason}. "
            f"Risk score: {risk_score}. Cancelling all orders."
        )
        
        # Cancel all open orders immediately
        await self.execution_manager.cancel_all_orders(reason=f"Emergency Halt: {reason}")
        
        # Trigger high-priority alert
        await self.monitoring.trigger_alert({
            'type': 'EMERGENCY_HALT',
            'level': 'CRITICAL',
            'message': reason,
            'risk_score': risk_score,
            'timestamp': datetime.now()
        })
        
        self.logger.warning("Skipping trade execution due to emergency halt.")
    
    async def _execute_trading_cycle(self, market_data: Dict[str, Any]) -> None:
        """Execute normal trading cycle: analyze, decide, execute."""
        # Analyze market data
        analysis_result = await self.analysis_engine.analyze(market_data)
        self.last_analysis = analysis_result
        
        # Make trading decisions
        async with self._portfolio_lock:
            decision = await self.decision_manager.make_decision(
                analysis_result, self.portfolio_state
            )
            self.last_decision = decision
        
        # Execute trades if actions are present
        if decision.get('actions'):
            execution_requests = self._convert_actions_to_requests(decision['actions'])
            await self.execution_manager.execute_trades(execution_requests)
    
    async def _update_monitoring_metrics(self) -> None:
        """Update monitoring system with current metrics."""
        async with self._portfolio_lock:
            await self.monitoring.update_metrics({
                'timestamp': datetime.now(),
                'portfolio': self.portfolio_state,
                'analysis': getattr(self, 'last_analysis', {}),
                'decision': getattr(self, 'last_decision', {})
            })
    
    async def _handle_main_loop_error(self, error: Exception, loop_count: int) -> None:
        """Handle errors in main loop with component-specific analysis."""
        error_source, error_context = self._analyze_error_source(error)
        
        # Log error with appropriate level
        if isinstance(error, (KeyError, TypeError, AttributeError)):
            self.logger.critical(f"Critical error in loop {loop_count}: {error}")
        elif isinstance(error, (ConnectionError, TimeoutError)):
            self.logger.warning(f"Network error in loop {loop_count}: {error}")
        else:
            self.logger.error(f"Error in loop {loop_count}: {error}")
        
        # Send alert if monitoring is available
        await self._send_error_alert(error_source, error, error_context)
        
        # Adaptive sleep based on error type
        sleep_duration = self._get_error_sleep_duration(error)
        await asyncio.sleep(sleep_duration)
    
    def _analyze_error_source(self, error: Exception) -> tuple[str, Dict[str, str]]:
        """Analyze error to determine source component."""
        tb_str = traceback.format_exc()
        
        error_mappings = {
            ('analysis_engine', 'analyze'): ('analysis_engine', 'Check market data quality and AI model status'),
            ('decision_manager', 'make_decision'): ('decision_manager', 'Review decision logic and portfolio state'),
            ('execution_manager', 'execute_trades'): ('execution_manager', 'Check exchange connectivity'),
            ('data_collector', 'get_latest_data'): ('data_collector', 'Check data source connectivity'),
            ('risk_monitor', 'assess_portfolio_risk'): ('risk_monitor', 'Review risk calculation parameters'),
            ('monitoring', 'update_metrics'): ('monitoring', 'Check monitoring system configuration')
        }
        
        for keywords, (component, suggestion) in error_mappings.items():
            if any(keyword in tb_str for keyword in keywords):
                return component, {'component': component, 'suggestion': suggestion}
        
        return 'system', {'component': 'system', 'suggestion': 'Review system resources and configuration'}
    
    async def _send_error_alert(self, error_source: str, error: Exception, context: Dict[str, str]) -> None:
        """Send error alert to monitoring system."""
        try:
            if hasattr(self, 'monitoring') and self.monitoring:
                await self.monitoring.trigger_alert({
                    'type': 'MAIN_LOOP_ERROR',
                    'level': 'ERROR',
                    'message': f"{error_source} error: {str(error)}",
                    'error_type': type(error).__name__,
                    'component': error_source,
                    'context': context,
                    'timestamp': datetime.now()
                })
        except Exception as alert_error:
            self.logger.error(f"Failed to send error alert: {alert_error}")
    
    def _get_error_sleep_duration(self, error: Exception) -> int:
        """Get appropriate sleep duration based on error type."""
        if isinstance(error, (ConnectionError, TimeoutError)):
            return 30  # Network errors - shorter sleep
        elif isinstance(error, (KeyError, TypeError, AttributeError)):
            return 120  # Programming errors - longer sleep
        else:
            return 60  # Default error handling
    
    async def _configure_sandbox_mode(self) -> None:
        """Configure sandbox mode if detected."""
        sandbox_mode = self._check_sandbox_mode()
        if sandbox_mode:
            self.logger.info("Sandbox mode detected - enabling test strategy")
            if hasattr(self.decision_manager, 'set_test_mode'):
                self.decision_manager.set_test_mode(True)
    
    async def _cleanup_on_error(self) -> None:
        """Cleanup resources when startup fails."""
        self.logger.info("Cleaning up resources after startup failure...")
        try:
            if hasattr(self, 'monitoring') and self.monitoring:
                await self.monitoring.stop()
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
    
    def _convert_actions_to_requests(self, actions: List[Dict[str, Any]]) -> List[ExecutionRequest]:
        """Convert decision actions to execution requests."""
        requests = []
        timestamp = datetime.now()
        
        for i, action in enumerate(actions):
            if action.get('type') == 'trade':
                request = ExecutionRequest(
                    request_id=f"req_{timestamp.strftime('%Y%m%d_%H%M%S')}_{i}",
                    symbol=action.get('symbol', 'BTC/USDT'),
                    side=OrderSide.BUY if action.get('side') == 'buy' else OrderSide.SELL,
                    target_quantity=action.get('quantity', 0.0),
                    urgency=action.get('urgency', 'medium'),
                    max_slippage=action.get('max_slippage', 0.01),
                    time_horizon=timedelta(minutes=action.get('time_horizon_minutes', 30)),
                    execution_strategy=action.get('strategy', 'smart'),
                    price_limit=action.get('price_limit'),
                    venue_preference=action.get('venue_preference'),
                    split_orders=action.get('split_orders', True),
                    iceberg_size=action.get('iceberg_size'),
                    metadata=action.get('metadata', {}),
                    created_time=timestamp
                )
                requests.append(request)
        
        return requests
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive agent status."""
        uptime = None
        if hasattr(self, 'start_time') and self.start_time:
            uptime = datetime.now() - self.start_time
        
        # Get component statuses
        component_status = {}
        for component_name in ['data_collector', 'analysis_engine', 'decision_manager', 
                              'execution_manager', 'risk_monitor', 'monitoring']:
            component = getattr(self, component_name, None)
            if component and hasattr(component, 'get_status'):
                try:
                    component_status[component_name] = component.get_status()
                except Exception as e:
                    component_status[component_name] = {'error': str(e)}
            else:
                component_status[component_name] = 'not_available'
        
        return {
            'agent_status': self.status.value,
            'uptime_seconds': uptime.total_seconds() if uptime else None,
            'portfolio': self.portfolio_state or {},
            'last_analysis': getattr(self, 'last_analysis', {}),
            'last_decision': getattr(self, 'last_decision', {}),
            'components': component_status,
            'sandbox_mode': self._check_sandbox_mode(),
            'timestamp': datetime.now()
        }
    
    def _check_sandbox_mode(self) -> bool:
        """Check if running in sandbox mode with multiple fallbacks."""
        # Priority 1: Environment variable
        import os
        if os.getenv('TRADING_SANDBOX', '').lower() in ('true', '1', 'yes'):
            return True
        
        # Priority 2: Hummingbot service configuration
        if hasattr(self, 'hummingbot_service') and self.hummingbot_service:
            try:
                config = self.hummingbot_service.get_config()
                if config.get('paper_trade', False) or config.get('sandbox_mode', False):
                    return True
            except Exception as e:
                self.logger.debug(f"Could not check Hummingbot sandbox mode: {e}")
        
        # Priority 3: Settings configuration
        if hasattr(self, 'settings') and self.settings:
            sandbox_setting = self.settings.get('sandbox_mode', False)
            if isinstance(sandbox_setting, (bool, str)):
                if isinstance(sandbox_setting, str):
                    return sandbox_setting.lower() in ('true', '1', 'yes')
                return sandbox_setting
        
        # Priority 4: Execution manager check
        if hasattr(self, 'execution_manager') and self.execution_manager:
            try:
                if hasattr(self.execution_manager, 'is_sandbox_mode'):
                    return self.execution_manager.is_sandbox_mode()
            except Exception as e:
                self.logger.debug(f"Could not check execution manager sandbox mode: {e}")
        
        return False