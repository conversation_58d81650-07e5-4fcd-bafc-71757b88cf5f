#!/usr/bin/env python3
"""
AI Trading System - Dashboard App

Streamlit dashboard for monitoring and controlling the AI Trading System.

Author: inkbytefo
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from datetime import datetime, timedelta
import requests
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from config.settings import Settings

# Page configuration
st.set_page_config(
    page_title="AI Trading System Dashboard",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS
st.markdown("""
<style>
.metric-card {
    background-color: #f0f2f6;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #1f77b4;
}
.status-healthy {
    color: #28a745;
}
.status-warning {
    color: #ffc107;
}
.status-error {
    color: #dc3545;
}
</style>
""", unsafe_allow_html=True)

def get_api_data(endpoint):
    """Get data from API"""
    try:
        response = requests.get(f"http://localhost:5000{endpoint}", timeout=5)
        if response.status_code == 200:
            return response.json()
        return None
    except:
        return None

def main():
    """Main dashboard function"""
    
    # Header
    st.title("🤖 AI Trading System Dashboard")
    st.markdown("---")
    
    # Sidebar
    with st.sidebar:
        st.header("Navigation")
        page = st.selectbox(
            "Select Page",
            ["Overview", "Portfolio", "Trades", "Strategies", "Settings"]
        )
        
        st.markdown("---")
        
        # System Status
        st.header("System Status")
        status_data = get_api_data("/api/v1/status")
        
        if status_data:
            st.markdown(f"**Status:** <span class='status-healthy'>✅ {status_data['status'].title()}</span>", unsafe_allow_html=True)
            st.markdown(f"**Version:** {status_data.get('version', 'Unknown')}")
            
            # Component status
            components = status_data.get('components', {})
            for component, status in components.items():
                color = 'status-healthy' if status == 'healthy' else 'status-error'
                st.markdown(f"**{component.replace('_', ' ').title()}:** <span class='{color}'>{'✅' if status == 'healthy' else '❌'} {status.title()}</span>", unsafe_allow_html=True)
        else:
            st.markdown("**Status:** <span class='status-error'>❌ Disconnected</span>", unsafe_allow_html=True)
    
    # Main content
    if page == "Overview":
        show_overview()
    elif page == "Portfolio":
        show_portfolio()
    elif page == "Trades":
        show_trades()
    elif page == "Strategies":
        show_strategies()
    elif page == "Settings":
        show_settings()

def show_overview():
    """Show overview page"""
    st.header("📊 System Overview")
    
    # Metrics row
    col1, col2, col3, col4 = st.columns(4)
    
    portfolio_data = get_api_data("/api/v1/portfolio")
    
    if portfolio_data:
        portfolio = portfolio_data.get('portfolio', {})
        
        with col1:
            st.metric(
                "Total Portfolio Value",
                f"${portfolio.get('total_value', 0):,.2f}",
                delta=f"{portfolio.get('performance', {}).get('daily_pnl', 0):+.2f}"
            )
        
        with col2:
            st.metric(
                "Available Balance",
                f"${portfolio.get('available_balance', 0):,.2f}"
            )
        
        with col3:
            st.metric(
                "Total P&L",
                f"${portfolio.get('performance', {}).get('total_pnl', 0):+,.2f}"
            )
        
        with col4:
            st.metric(
                "Win Rate",
                f"{portfolio.get('performance', {}).get('win_rate', 0):.1%}"
            )
    else:
        st.error("Unable to fetch portfolio data")
    
    st.markdown("---")
    
    # Charts row
    col1, col2 = st.columns(2)
    
    with col1:
        st.subheader("Portfolio Performance")
        # Sample data for demo
        dates = pd.date_range(start=datetime.now() - timedelta(days=30), end=datetime.now(), freq='D')
        values = [10000 + i * 50 + (i % 7) * 100 for i in range(len(dates))]
        
        fig = go.Figure()
        fig.add_trace(go.Scatter(x=dates, y=values, mode='lines', name='Portfolio Value'))
        fig.update_layout(title="30-Day Portfolio Performance", xaxis_title="Date", yaxis_title="Value ($)")
        st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        st.subheader("Asset Allocation")
        # Sample data for demo
        assets = ['BTC', 'ETH', 'USDT', 'BNB']
        values = [40, 30, 20, 10]
        
        fig = px.pie(values=values, names=assets, title="Current Asset Allocation")
        st.plotly_chart(fig, use_container_width=True)

def show_portfolio():
    """Show portfolio page"""
    st.header("💼 Portfolio Management")
    
    portfolio_data = get_api_data("/api/v1/portfolio")
    
    if portfolio_data:
        portfolio = portfolio_data.get('portfolio', {})
        
        # Portfolio summary
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Total Value", f"${portfolio.get('total_value', 0):,.2f}")
        with col2:
            st.metric("Available Balance", f"${portfolio.get('available_balance', 0):,.2f}")
        with col3:
            st.metric("Total P&L", f"${portfolio.get('performance', {}).get('total_pnl', 0):+,.2f}")
        
        st.markdown("---")
        
        # Positions table
        st.subheader("Current Positions")
        positions = portfolio.get('positions', [])
        
        if positions:
            df = pd.DataFrame(positions)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No open positions")
    else:
        st.error("Unable to fetch portfolio data")

def show_trades():
    """Show trades page"""
    st.header("📈 Trading History")
    
    trades_data = get_api_data("/api/v1/trades")
    
    if trades_data:
        trades = trades_data.get('trades', [])
        
        if trades:
            df = pd.DataFrame(trades)
            st.dataframe(df, use_container_width=True)
        else:
            st.info("No trades found")
    else:
        st.error("Unable to fetch trades data")

def show_strategies():
    """Show strategies page"""
    st.header("🎯 Trading Strategies")
    
    strategies_data = get_api_data("/api/v1/strategies")
    
    if strategies_data:
        strategies = strategies_data.get('strategies', [])
        
        for strategy in strategies:
            with st.expander(f"{strategy.get('name', 'Unknown Strategy')} - {strategy.get('status', 'Unknown').title()}"):
                col1, col2, col3 = st.columns(3)
                
                performance = strategy.get('performance', {})
                
                with col1:
                    st.metric("Daily Return", f"{performance.get('daily_return', 0):+.2%}")
                with col2:
                    st.metric("Total Return", f"{performance.get('total_return', 0):+.2%}")
                with col3:
                    st.metric("Sharpe Ratio", f"{performance.get('sharpe_ratio', 0):.2f}")
    else:
        st.error("Unable to fetch strategies data")

def show_settings():
    """Show settings page"""
    st.header("⚙️ System Settings")
    
    # Trading settings
    st.subheader("Trading Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.number_input("Max Position Size (%)", min_value=1, max_value=100, value=10)
        st.number_input("Stop Loss (%)", min_value=0.1, max_value=50.0, value=2.0, step=0.1)
    
    with col2:
        st.number_input("Take Profit (%)", min_value=0.1, max_value=100.0, value=5.0, step=0.1)
        st.selectbox("Trading Mode", ["Paper Trading", "Live Trading"])
    
    st.markdown("---")
    
    # API settings
    st.subheader("API Configuration")
    
    st.text_input("Exchange API Key", type="password")
    st.text_input("Exchange Secret Key", type="password")
    
    if st.button("Save Settings"):
        st.success("Settings saved successfully!")

if __name__ == "__main__":
    main()