#!/usr/bin/env python3
"""
Decision Manager - Central decision making engine for AI trading agent

Author: inkbytefo
Description: Combines AI analysis, risk management, and portfolio optimization to make trading decisions
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
from dataclasses import dataclass
from enum import Enum

from .risk_manager import RiskManager
from ..config.settings import TradingSettings, RiskSettings
from ..utils.common import get_logger, PerformanceTimer, safe_get, calculate_duration
from ..utils.base_classes import BaseComponent, ComponentStatus


class DecisionType(Enum):
    """Types of trading decisions."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_POSITION = "close_position"
    ADJUST_POSITION = "adjust_position"
    EMERGENCY_EXIT = "emergency_exit"


class ConfidenceLevel(Enum):
    """Confidence levels for decisions."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9


@dataclass
class TradingDecision:
    """Trading decision data structure."""
    symbol: str
    decision_type: DecisionType
    confidence: float
    quantity: float
    price_target: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    reasoning: List[str]
    risk_score: float
    expected_return: float
    time_horizon: str  # 'short', 'medium', 'long'
    timestamp: datetime
    priority: int  # 1-10, 10 being highest priority
    metadata: Dict[str, Any]


class DecisionManager(BaseComponent):
    """Central decision making engine for AI trading agent."""

    def __init__(self, settings: TradingSettings, risk_settings: RiskSettings):
        super().__init__()
        self.settings = settings
        self.logger = get_logger(__name__)

        # Initialize RiskManager with centralized risk settings
        self.risk_manager = RiskManager(risk_settings)

        # Decision making configuration
        self.min_confidence_threshold = safe_get(settings, 'min_confidence_threshold', 0.6)
        self.max_position_size = safe_get(settings, 'max_position_size', 0.1)
        self.max_daily_trades = safe_get(settings, 'max_daily_trades', 10)
        self.risk_tolerance = safe_get(settings, 'risk_tolerance', 0.05)

        # Decision weights for different factors
        self.decision_weights = {
            'technical_analysis': safe_get(settings, 'technical_weight', 0.3),
            'sentiment_analysis': safe_get(settings, 'sentiment_weight', 0.2),
            'pattern_recognition': safe_get(settings, 'pattern_weight', 0.2),
            'price_prediction': safe_get(settings, 'prediction_weight', 0.2),
            'risk_assessment': safe_get(settings, 'risk_weight', 0.1)
        }
        
        # Market condition adjustments
        self.market_condition_multipliers = {
            'bull_market': 1.2,
            'bear_market': 0.8,
            'sideways': 1.0,
            'high_volatility': 0.7,
            'low_volatility': 1.1
        }
        
        # Decision history with size limit
        self.decision_history = []
        self.max_history_size = 1000
        
        # Performance tracking
        self.performance_metrics = {
            'total_decisions': 0,
            'successful_decisions': 0,
            'failed_decisions': 0,
            'average_return': 0.0,
            'win_rate': 0.0,
            'last_update': None,
            'avg_decision_time': 0.0
        }
        
        # Current market state
        self.market_state = {
            'condition': 'neutral',
            'volatility': 'medium',
            'trend': 'sideways',
            'sentiment': 'neutral',
            'last_update': None
        }
        
        # Active decisions tracking
        self.active_decisions = {}
        self.daily_trade_count = 0
        self.last_trade_date = None
        
        # Cache for performance optimization
        self.decision_cache = {}
        self.cache_ttl = timedelta(minutes=5)
    
    def _clear_expired_cache(self) -> None:
        """Clear expired cache entries."""
        current_time = datetime.now()
        expired_keys = [
            key for key, (_, timestamp) in self.decision_cache.items()
            if current_time - timestamp > self.cache_ttl
        ]
        for key in expired_keys:
            del self.decision_cache[key]
        
        if expired_keys:
            self.logger.debug(f"Cleared {len(expired_keys)} expired cache entries")
    
    async def start(self) -> None:
        """Start the decision manager with comprehensive initialization."""
        if self.status == ComponentStatus.RUNNING:
            self.logger.warning("Decision Manager is already running")
            return
        
        self.logger.info("Starting Decision Manager...")
        self.status = ComponentStatus.STARTING
        
        try:
            with PerformanceTimer() as timer:
                # Start risk manager
                await self.risk_manager.start()
                
                # Reset daily counters if new day
                await self._reset_daily_counters()
                
                # Initialize market state
                self.market_state['last_update'] = datetime.now()
                
                # Clear old cache entries
                self._clear_expired_cache()
                
                self.status = ComponentStatus.RUNNING
                self.logger.info(f"Decision Manager started successfully in {timer.elapsed:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Failed to start Decision Manager: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the decision manager with proper cleanup."""
        if self.status == ComponentStatus.STOPPED:
            self.logger.warning("Decision Manager is already stopped")
            return
        
        self.logger.info("Stopping Decision Manager...")
        self.status = ComponentStatus.STOPPING
        
        try:
            with PerformanceTimer() as timer:
                # Save decision data
                await self._save_decision_data()
                
                # Stop risk manager
                await self.risk_manager.stop()
                
                # Clear caches
                self.decision_cache.clear()
                
                self.status = ComponentStatus.STOPPED
                self.logger.info(f"Decision Manager stopped successfully in {timer.elapsed:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Error stopping Decision Manager: {e}")
            raise
        
        # Save decision history and performance metrics
        await self._save_decision_data()
        
        self.logger.info("Decision Manager stopped")
    
    async def make_decision(self, analysis_data: Dict[str, Any], 
                          portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Make trading decision with integrated risk assessment and caching."""
        # Check cache first
        cache_key = self._generate_cache_key(analysis_data, portfolio_data)
        if cache_key in self.decision_cache:
            cached_result, timestamp = self.decision_cache[cache_key]
            if datetime.now() - timestamp < self.cache_ttl:
                self.logger.debug(f"Returning cached decision for key: {cache_key[:20]}...")
                return cached_result
        
        try:
            with PerformanceTimer() as timer:
                # Check if we're in test mode (sandbox) - use simple ping-pong strategy
                if self._is_test_mode():
                    result = await self._make_test_decision(analysis_data, portfolio_data)
                    self.decision_cache[cache_key] = (result, datetime.now())
                    return result
                
                # Validate inputs
                analysis_data = safe_get(analysis_data, {}, dict)
                portfolio_data = safe_get(portfolio_data, {}, dict)
                
                # Perform risk assessment
                portfolio_risk = await self.risk_manager.assess_portfolio_risk(
                    portfolio_data, analysis_data.get('market_data', {})
                )
                
                # Create risk data for decision making
                risk_data = {
                    'portfolio_risk': portfolio_risk,
                    'risk_manager': self.risk_manager
                }
                
                # Make decisions using existing logic
                decisions = await self.make_decisions(analysis_data, portfolio_data, risk_data)
                
                # Convert decisions to actions format expected by TradingAgent
                actions = []
                for decision in decisions:
                    # Validate decision with risk manager
                    trade_approved = await self.risk_manager.assess_trade_risk(
                        decision.symbol, decision.quantity, 
                        decision.decision_type.value, portfolio_data, 
                        analysis_data.get('market_data', {})
                    )
                    
                    if trade_approved.get('approved', False):
                        actions.append({
                            'type': 'trade',
                            'symbol': decision.symbol,
                            'side': 'buy' if decision.decision_type.value in ['buy'] else 'sell',
                            'quantity': decision.quantity,
                            'price_limit': decision.price_target,
                            'urgency': 'medium',
                            'max_slippage': 0.01,
                            'time_horizon_minutes': 30,
                            'strategy': 'smart',
                            'split_orders': True,
                            'metadata': {
                                'confidence': decision.confidence,
                            'risk_score': decision.risk_score,
                            'reasoning': decision.reasoning,
                            'risk_assessment': trade_approved
                        }
                    })
                    else:
                        self.logger.warning(f"Trade rejected by risk manager: {trade_approved.get('reason', 'Unknown')}")
                
                # Update performance metrics
                self.performance_metrics['total_decisions'] += len(decisions)
                self.performance_metrics['avg_decision_time'] = (
                    (self.performance_metrics['avg_decision_time'] * 
                     (self.performance_metrics['total_decisions'] - len(decisions)) + timer.elapsed) /
                    self.performance_metrics['total_decisions']
                )
                
                result = {
                    'actions': actions,
                    'portfolio_risk': portfolio_risk,
                    'decisions_made': len(decisions),
                    'decisions_approved': len(actions),
                    'timestamp': datetime.now(),
                    'processing_time': timer.elapsed
                }
                
                # Cache the result
                self.decision_cache[cache_key] = (result, datetime.now())
                
                self.logger.info(f"Decision made in {timer.elapsed:.3f}s: {len(actions)} actions from {len(decisions)} decisions")
                return result
                
        except Exception as e:
            self.logger.error(f"Error making decision: {e}")
            return {'actions': [], 'error': str(e), 'timestamp': datetime.now()}
    
    def _generate_cache_key(self, analysis_data: Dict[str, Any], portfolio_data: Dict[str, Any]) -> str:
        """Generate a cache key for decision caching."""
        import hashlib
        import json
        
        # Extract key components for caching
        cache_data = {
            'market_data_hash': str(hash(str(analysis_data.get('market_data', {})))),
            'portfolio_hash': str(hash(str(portfolio_data.get('positions', {})))),
            'signals_hash': str(hash(str(analysis_data.get('signals', {})))),
            'timestamp_minute': datetime.now().strftime('%Y%m%d%H%M')  # Cache per minute
        }
        
        cache_string = json.dumps(cache_data, sort_keys=True)
        return hashlib.md5(cache_string.encode()).hexdigest()
    
    async def make_decisions(self, analysis_data: Dict[str, Any], 
                           portfolio_data: Dict[str, Any],
                           risk_data: Dict[str, Any]) -> List[TradingDecision]:
        """Make trading decisions based on comprehensive analysis with parallel processing."""
        try:
            with PerformanceTimer() as timer:
                decisions = []
                
                # Validate inputs
                analysis_data = safe_get(analysis_data, {}, dict)
                portfolio_data = safe_get(portfolio_data, {}, dict)
                risk_data = safe_get(risk_data, {}, dict)
                
                # Update market state
                await self._update_market_state(analysis_data)
                
                # Check if we can make more trades today
                if not await self._can_make_trades():
                    self.logger.info("Daily trade limit reached")
                    return decisions
                
                # Analyze each potential trading opportunity
                opportunities = await self._identify_opportunities(analysis_data, portfolio_data)
                
                if not opportunities:
                    self.logger.debug("No trading opportunities identified")
                    return decisions
                
                # Process opportunities in parallel for better performance
                decision_tasks = []
                for opportunity in opportunities:
                    task = self._generate_decision(
                        opportunity, analysis_data, portfolio_data, risk_data
                    )
                    decision_tasks.append(task)
                
                # Wait for all decisions to be generated
                decision_results = await asyncio.gather(*decision_tasks, return_exceptions=True)
                
                # Process results and filter valid decisions
                for i, result in enumerate(decision_results):
                    if isinstance(result, Exception):
                        self.logger.warning(f"Error generating decision for {opportunities[i].get('symbol', 'unknown')}: {result}")
                        continue
                    
                    if result and self._validate_decision(result, portfolio_data, risk_data):
                        decisions.append(result)
                        
                        # Update daily trade count
                        if result.decision_type in [DecisionType.BUY, DecisionType.SELL]:
                            self.daily_trade_count += 1
                
                # Sort decisions by priority and confidence
                decisions.sort(key=lambda x: (x.priority, x.confidence), reverse=True)
                
                # Limit number of decisions
                max_decisions = min(5, self.max_daily_trades - self.daily_trade_count)
                decisions = decisions[:max_decisions]
            
            # Update decision history
            self._update_decision_history(decisions)
            
            # Update performance metrics
            await self._update_performance_metrics()
            
            self.logger.info(f"Generated {len(decisions)} trading decisions in {(datetime.now() - decision_start).total_seconds():.2f}s")
            return decisions
            
        except Exception as e:
            self.logger.error(f"Error in decision making: {e}")
            return []
    
    async def _identify_opportunities(self, analysis_data: Dict[str, Any], 
                                    portfolio_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Identify potential trading opportunities with optimized processing."""
        opportunities = []
        
        try:
            with PerformanceTimer() as timer:
                # Validate inputs
                if not analysis_data or not portfolio_data:
                    self.logger.warning("Missing analysis or portfolio data for opportunity identification")
                    return opportunities
                
                # Get current portfolio positions
                current_positions = safe_get(portfolio_data, 'positions', {})
                
                # Get configurable thresholds
                signal_threshold = safe_get(self.settings, 'signal_threshold', 0.6)
                pattern_threshold = safe_get(self.settings, 'pattern_threshold', 0.7)
                prediction_threshold = safe_get(self.settings, 'prediction_threshold', 0.75)
                sentiment_threshold = safe_get(self.settings, 'sentiment_threshold', 0.8)
                
                # Analyze technical signals
                tech_opportunities = await self._analyze_technical_signals(
                    analysis_data, current_positions, signal_threshold
                )
                opportunities.extend(tech_opportunities)
                
                # Analyze pattern signals
                pattern_opportunities = await self._analyze_pattern_signals(
                    analysis_data, current_positions, pattern_threshold
                )
                opportunities.extend(pattern_opportunities)
                
                # Analyze prediction signals
                prediction_opportunities = await self._analyze_prediction_signals(
                    analysis_data, current_positions, prediction_threshold
                )
                opportunities.extend(prediction_opportunities)
                
                # Analyze sentiment extremes
                sentiment_opportunities = await self._analyze_sentiment_signals(
                    analysis_data, current_positions, sentiment_threshold
                )
                opportunities.extend(sentiment_opportunities)
                
                # Remove duplicates and merge similar opportunities
                opportunities = self._merge_opportunities(opportunities)
                
                # Filter by market conditions
                opportunities = self._filter_by_market_conditions(opportunities, analysis_data)
                
                self.logger.debug(f"Identified {len(opportunities)} opportunities in {timer.elapsed:.3f}s")
                
        except Exception as e:
            self.logger.error(f"Error identifying opportunities: {e}")
        
        return opportunities
    
    async def _analyze_technical_signals(self, analysis_data: Dict[str, Any], 
                                       current_positions: Dict[str, float],
                                       threshold: float) -> List[Dict[str, Any]]:
        """Analyze technical signals for opportunities."""
        opportunities = []
        
        tech_data = safe_get(analysis_data, 'technical_analysis', {})
        pairs = safe_get(tech_data, 'pairs', {})
        
        for symbol, pair_data in pairs.items():
            signals = safe_get(pair_data, 'signals', {})
            
            if signals:
                signal_strength = self._calculate_signal_strength(signals)
                
                if signal_strength > threshold:
                    opportunity = {
                        'symbol': symbol,
                        'type': 'technical_signal',
                        'strength': signal_strength,
                        'signals': signals,
                        'current_position': current_positions.get(symbol, 0),
                        'data': pair_data,
                        'timestamp': datetime.now()
                    }
                    opportunities.append(opportunity)
        
        return opportunities
    
    async def _analyze_pattern_signals(self, analysis_data: Dict[str, Any],
                                     current_positions: Dict[str, float],
                                     threshold: float) -> List[Dict[str, Any]]:
        """Analyze pattern signals for opportunities."""
        opportunities = []
        
        pattern_data = safe_get(analysis_data, 'pattern_analysis', {})
        patterns = safe_get(pattern_data, 'patterns', [])
        
        for pattern in patterns:
            confidence = getattr(pattern, 'confidence', 0)
            
            if confidence > threshold:
                symbol = self._extract_symbol_from_pattern(pattern)
                
                if symbol:
                    opportunity = {
                        'symbol': symbol,
                        'type': 'pattern_signal',
                        'strength': confidence,
                        'pattern': pattern,
                        'current_position': current_positions.get(symbol, 0),
                        'timestamp': datetime.now()
                    }
                    opportunities.append(opportunity)
        
        return opportunities
    
    async def _analyze_prediction_signals(self, analysis_data: Dict[str, Any],
                                        current_positions: Dict[str, float],
                                        threshold: float) -> List[Dict[str, Any]]:
        """Analyze prediction signals for opportunities."""
        opportunities = []
        
        pred_data = safe_get(analysis_data, 'predictions', {})
        predictions = safe_get(pred_data, 'predictions', [])
        
        for prediction in predictions:
            confidence = getattr(prediction, 'confidence', 0)
            symbol = getattr(prediction, 'symbol', None)
            
            if confidence > threshold and symbol:
                opportunity = {
                    'symbol': symbol,
                    'type': 'prediction_signal',
                    'strength': confidence,
                    'prediction': prediction,
                    'current_position': current_positions.get(symbol, 0),
                    'timestamp': datetime.now()
                }
                opportunities.append(opportunity)
        
        return opportunities
    
    async def _analyze_sentiment_signals(self, analysis_data: Dict[str, Any],
                                       current_positions: Dict[str, float],
                                       threshold: float) -> List[Dict[str, Any]]:
        """Analyze sentiment signals for opportunities."""
        opportunities = []
        
        sentiment_data = safe_get(analysis_data, 'sentiment_analysis', {})
        keyword_sentiment = safe_get(sentiment_data, 'keyword_sentiment', {})
        
        for symbol, sentiment in keyword_sentiment.items():
            score = abs(safe_get(sentiment, 'score', 0))
            
            if score > threshold:
                opportunity = {
                    'symbol': symbol,
                    'type': 'sentiment_signal',
                    'strength': score,
                    'sentiment': sentiment,
                    'current_position': current_positions.get(symbol, 0),
                    'timestamp': datetime.now()
                }
                opportunities.append(opportunity)
        
        return opportunities
    
    def _filter_by_market_conditions(self, opportunities: List[Dict[str, Any]],
                                   analysis_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Filter opportunities based on current market conditions."""
        if not opportunities:
            return opportunities
        
        try:
            # Get market volatility
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            # Adjust thresholds based on volatility
            if market_volatility > 0.8:  # High volatility
                # Require higher confidence in volatile markets
                min_strength = 0.8
            elif market_volatility < 0.3:  # Low volatility
                # Allow lower confidence in stable markets
                min_strength = 0.5
            else:
                min_strength = 0.6
            
            # Filter opportunities
            filtered = []
            for opp in opportunities:
                if opp.get('strength', 0) >= min_strength:
                    # Add market condition metadata
                    opp['market_volatility'] = market_volatility
                    opp['min_strength_required'] = min_strength
                    filtered.append(opp)
            
            return filtered
            
        except Exception as e:
            self.logger.error(f"Error filtering opportunities by market conditions: {e}")
            return opportunities
    
    async def _generate_decision(self, opportunity: Dict[str, Any], 
                               analysis_data: Dict[str, Any],
                               portfolio_data: Dict[str, Any],
                               risk_data: Dict[str, Any]) -> Optional[TradingDecision]:
        """Generate a trading decision for an opportunity with optimized processing."""
        try:
            with PerformanceTimer() as timer:
                # Validate inputs
                symbol = safe_get(opportunity, 'symbol')
                if not symbol:
                    self.logger.warning("Missing symbol in opportunity")
                    return None
                
                current_position = safe_get(opportunity, 'current_position', 0)
                
                # Calculate decision scores with error handling
                scores = await self._calculate_decision_scores(opportunity, analysis_data)
                if not scores or all(score == 0 for score in scores.values()):
                    self.logger.debug(f"No valid scores for {symbol}")
                    return None
                
                # Determine decision type
                decision_type = self._determine_decision_type(scores, current_position)
                
                if decision_type == DecisionType.HOLD:
                    self.logger.debug(f"Hold decision for {symbol}, no action needed")
                    return None
                
                # Calculate confidence with validation
                confidence = self._calculate_decision_confidence(scores, opportunity)
                min_threshold = safe_get(self.settings, 'min_confidence_threshold', self.min_confidence_threshold)
                
                if confidence < min_threshold:
                    self.logger.debug(f"Confidence {confidence:.3f} below threshold {min_threshold} for {symbol}")
                    return None
                
                # Calculate position size with risk validation
                quantity = await self._calculate_position_size(
                    symbol, decision_type, confidence, portfolio_data, risk_data
                )
                
                if quantity <= 0:
                    self.logger.debug(f"Invalid position size {quantity} for {symbol}")
                    return None
                
                # Calculate price targets with error handling
                try:
                    price_target, stop_loss, take_profit = await self._calculate_price_targets(
                        symbol, decision_type, analysis_data
                    )
                except Exception as e:
                    self.logger.warning(f"Error calculating price targets for {symbol}: {e}")
                    price_target = stop_loss = take_profit = None
                
                # Calculate risk and expected return
                risk_score = self._calculate_risk_score(symbol, decision_type, quantity, analysis_data)
                expected_return = self._calculate_expected_return(symbol, decision_type, analysis_data)
                
                # Validate risk limits
                max_risk = safe_get(self.settings, 'max_risk_per_trade', 0.02)
                if risk_score > max_risk:
                    self.logger.warning(f"Risk score {risk_score:.3f} exceeds limit {max_risk} for {symbol}")
                    return None
                
                # Generate reasoning and metadata
                reasoning = self._generate_reasoning(opportunity, scores, analysis_data)
                priority = self._calculate_priority(confidence, risk_score, expected_return)
                time_horizon = self._determine_time_horizon(opportunity, analysis_data)
                
                # Create decision with comprehensive metadata
                decision = TradingDecision(
                    symbol=symbol,
                    decision_type=decision_type,
                    confidence=confidence,
                    quantity=quantity,
                    price_target=price_target,
                    stop_loss=stop_loss,
                    take_profit=take_profit,
                    reasoning=reasoning,
                    risk_score=risk_score,
                    expected_return=expected_return,
                    time_horizon=time_horizon,
                    timestamp=datetime.now(),
                    priority=priority,
                    metadata={
                        'opportunity_type': safe_get(opportunity, 'type', 'unknown'),
                        'opportunity_strength': safe_get(opportunity, 'strength', 0),
                        'market_condition': safe_get(self.market_state, 'condition', 'unknown'),
                        'market_volatility': safe_get(self.market_state, 'volatility', 0.5),
                        'scores': scores,
                        'generation_time': timer.elapsed,
                        'min_confidence_used': min_threshold,
                        'max_risk_limit': max_risk
                    }
                )
                
                self.logger.debug(f"Generated {decision_type.value} decision for {symbol} "
                                f"(confidence: {confidence:.3f}, risk: {risk_score:.3f}) "
                                f"in {timer.elapsed:.3f}s")
                
                return decision
                
        except Exception as e:
            self.logger.error(f"Error generating decision for {opportunity.get('symbol', 'unknown')}: {e}")
            return None
    
    async def _calculate_decision_scores(self, opportunity: Dict[str, Any], 
                                       analysis_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate decision scores for different factors with optimized processing."""
        scores = {
            'technical': 0.0,
            'sentiment': 0.0,
            'pattern': 0.0,
            'prediction': 0.0,
            'risk': 0.0
        }
        
        try:
            symbol = safe_get(opportunity, 'symbol')
            if not symbol:
                self.logger.warning("Missing symbol in opportunity for score calculation")
                return scores
            
            # Calculate scores in parallel for better performance
            score_tasks = [
                self._calculate_technical_score(symbol, analysis_data),
                self._calculate_sentiment_score(symbol, analysis_data),
                self._calculate_pattern_score(symbol, analysis_data),
                self._calculate_prediction_score(symbol, analysis_data),
                self._calculate_risk_score_factor(opportunity)
            ]
            
            # Execute all score calculations concurrently
            results = await asyncio.gather(*score_tasks, return_exceptions=True)
            
            # Process results with error handling
            score_keys = ['technical', 'sentiment', 'pattern', 'prediction', 'risk']
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.warning(f"Error calculating {score_keys[i]} score for {symbol}: {result}")
                    scores[score_keys[i]] = 0.0
                else:
                    scores[score_keys[i]] = max(0.0, min(1.0, result))  # Clamp to [0, 1]
            
            # Apply score weights if configured
            weights = safe_get(self.settings, 'score_weights', {})
            if weights:
                for key in scores:
                    weight = safe_get(weights, key, 1.0)
                    scores[key] *= weight
            
            # Log score details for debugging
            self.logger.debug(f"Decision scores for {symbol}: {scores}")
            
        except Exception as e:
            self.logger.error(f"Error calculating decision scores for {symbol}: {e}")
        
        return scores
    
    async def _calculate_technical_score(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate technical analysis score for a symbol."""
        try:
            tech_data = safe_get(analysis_data, 'technical_analysis', {})
            pairs_data = safe_get(tech_data, 'pairs', {})
            
            if symbol not in pairs_data:
                return 0.0
            
            pair_data = pairs_data[symbol]
            signals = safe_get(pair_data, 'signals', {})
            
            return self._calculate_signal_strength(signals)
            
        except Exception as e:
            self.logger.debug(f"Error calculating technical score for {symbol}: {e}")
            return 0.0
    
    async def _calculate_sentiment_score(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate sentiment analysis score for a symbol."""
        try:
            sentiment_data = safe_get(analysis_data, 'sentiment_analysis', {})
            keyword_sentiment = safe_get(sentiment_data, 'keyword_sentiment', {})
            
            if symbol not in keyword_sentiment:
                return 0.0
            
            sentiment = keyword_sentiment[symbol]
            score = safe_get(sentiment, 'score', 0.0)
            
            # Return absolute value normalized to [0, 1]
            return min(1.0, abs(score))
            
        except Exception as e:
            self.logger.debug(f"Error calculating sentiment score for {symbol}: {e}")
            return 0.0
    
    async def _calculate_pattern_score(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate pattern analysis score for a symbol."""
        try:
            pattern_data = safe_get(analysis_data, 'pattern_analysis', {})
            patterns = safe_get(pattern_data, 'patterns', [])
            
            if not patterns:
                return 0.0
            
            # Find patterns for this symbol
            symbol_patterns = []
            for pattern in patterns:
                try:
                    pattern_symbol = self._extract_symbol_from_pattern(pattern)
                    if pattern_symbol == symbol:
                        confidence = getattr(pattern, 'confidence', 0.0)
                        symbol_patterns.append(confidence)
                except Exception:
                    continue
            
            return max(symbol_patterns) if symbol_patterns else 0.0
            
        except Exception as e:
            self.logger.debug(f"Error calculating pattern score for {symbol}: {e}")
            return 0.0
    
    async def _calculate_prediction_score(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate prediction score for a symbol."""
        try:
            pred_data = safe_get(analysis_data, 'predictions', {})
            predictions = safe_get(pred_data, 'predictions', [])
            
            if not predictions:
                return 0.0
            
            # Find predictions for this symbol
            symbol_predictions = []
            for prediction in predictions:
                try:
                    if getattr(prediction, 'symbol', None) == symbol:
                        confidence = getattr(prediction, 'confidence', 0.0)
                        symbol_predictions.append(confidence)
                except Exception:
                    continue
            
            return max(symbol_predictions) if symbol_predictions else 0.0
            
        except Exception as e:
            self.logger.debug(f"Error calculating prediction score for {symbol}: {e}")
            return 0.0
    
    async def _calculate_risk_score_factor(self, opportunity: Dict[str, Any]) -> float:
        """Calculate risk score factor for an opportunity."""
        try:
            risk_level = safe_get(opportunity, 'risk_level', 0.5)
            # Inverse risk - lower risk = higher score
            return 1.0 - min(max(risk_level, 0.0), 1.0)
            
        except Exception as e:
            self.logger.debug(f"Error calculating risk score factor: {e}")
            return 0.5
    
    def _determine_decision_type(self, scores: Dict[str, float], current_position: float) -> DecisionType:
        """Determine the type of decision to make with optimized logic."""
        try:
            # Calculate weighted score with error handling
            weighted_score = 0.0
            total_weight = 0.0
            
            for factor, score in scores.items():
                weight = safe_get(self.decision_weights, f"{factor}_analysis", 0.2)
                if weight > 0:  # Only consider positive weights
                    weighted_score += score * weight
                    total_weight += weight
            
            # Normalize by total weight to avoid bias
            if total_weight > 0:
                weighted_score /= total_weight
            
            # Adjust for market conditions
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            market_multiplier = safe_get(self.market_condition_multipliers, market_condition, 1.0)
            weighted_score *= market_multiplier
            
            # Get configurable thresholds
            strong_threshold = safe_get(self.settings, 'strong_signal_threshold', 0.7)
            weak_threshold = safe_get(self.settings, 'weak_signal_threshold', 0.3)
            
            # Determine decision based on score and current position
            if weighted_score > strong_threshold:  # Strong positive signal
                if current_position <= 0:
                    return DecisionType.BUY
                else:
                    # Check if we should increase position
                    max_position = safe_get(self.settings, 'max_position_size', 1.0)
                    if current_position < max_position:
                        return DecisionType.ADJUST_POSITION
                    else:
                        return DecisionType.HOLD
            
            elif weighted_score < -strong_threshold:  # Strong negative signal
                if current_position > 0:
                    return DecisionType.SELL
                elif current_position < 0:
                    return DecisionType.CLOSE_POSITION  # Close short position
                else:
                    # Only allow short selling if enabled
                    allow_short = safe_get(self.settings, 'allow_short_selling', False)
                    return DecisionType.SELL if allow_short else DecisionType.HOLD
            
            elif abs(weighted_score) < weak_threshold:  # Weak signal
                return DecisionType.HOLD
            
            else:
                # Medium strength signal - be conservative
                return DecisionType.HOLD
                
        except Exception as e:
            self.logger.error(f"Error determining decision type: {e}")
            return DecisionType.HOLD
    
    def _calculate_decision_confidence(self, scores: Dict[str, float], 
                                     opportunity: Dict[str, Any]) -> float:
        """Calculate confidence level for a decision with enhanced logic."""
        try:
            # Validate inputs
            if not scores or all(score == 0 for score in scores.values()):
                return 0.0
            
            # Base confidence on score consistency and strength
            score_values = [score for score in scores.values() if score > 0]
            if not score_values:
                return 0.0
            
            avg_score = np.mean(score_values)
            score_std = np.std(score_values) if len(score_values) > 1 else 0.0
            
            # Higher consistency = higher confidence
            consistency_factor = 1.0 - min(score_std / max(avg_score, 0.1), 1.0)
            
            # Opportunity strength factor with validation
            strength_factor = safe_get(opportunity, 'strength', 0.5)
            strength_factor = max(0.0, min(1.0, strength_factor))  # Clamp to [0, 1]
            
            # Market condition factor with more granular adjustments
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            market_factor = 1.0
            if market_condition == 'high_volatility' or market_volatility > 0.7:
                market_factor = 0.7  # Lower confidence in high volatility
            elif market_condition == 'low_volatility' or market_volatility < 0.3:
                market_factor = 1.2  # Higher confidence in low volatility
            elif market_condition == 'trending':
                market_factor = 1.1  # Slightly higher confidence in trending markets
            elif market_condition == 'sideways':
                market_factor = 0.9  # Slightly lower confidence in sideways markets
        
        # Combine factors
        confidence = (avg_score * 0.4 + consistency_factor * 0.3 + 
                     strength_factor * 0.3) * market_factor
        
        return max(0.1, min(0.95, confidence))
    
    async def _calculate_position_size(self, symbol: str, decision_type: DecisionType,
                                     confidence: float, portfolio_data: Dict[str, Any],
                                     risk_data: Dict[str, Any]) -> float:
        """Calculate appropriate position size with enhanced risk management."""
        try:
            # Input validation
            if not symbol or confidence <= 0:
                return 0.0
            
            # Get portfolio value with validation
            portfolio_value = safe_get(portfolio_data, 'total_value', 100000)
            if portfolio_value <= 0:
                self.logger.warning(f"Invalid portfolio value: {portfolio_value}")
                return 0.0
            
            # Get current price from market data
            current_price = self._get_current_price(symbol, portfolio_data)
            if current_price <= 0:
                self.logger.warning(f"Invalid current price for {symbol}: {current_price}")
                return 0.0
            
            # Base position size calculation with confidence scaling
            confidence_factor = min(confidence, 0.95)  # Cap confidence impact
            base_size = self.max_position_size * confidence_factor
            
            # Risk adjustment factors
            risk_adjustment = self._calculate_risk_adjustment(risk_data, decision_type)
            
            # Market condition adjustment
            market_adjustment = self._calculate_market_adjustment()
            
            # Volatility adjustment
            volatility_adjustment = self._calculate_volatility_adjustment(symbol, portfolio_data)
            
            # Calculate position size in dollars
            position_size_usd = (
                portfolio_value * base_size * 
                risk_adjustment * market_adjustment * volatility_adjustment
            )
            
            # Convert to quantity
            quantity = position_size_usd / current_price
            
            # Apply limits and constraints
            quantity = self._apply_position_limits(quantity, portfolio_value, current_price, symbol)
            
            # Final validation
            if quantity < 0:
                quantity = 0.0
            
            self.logger.debug(
                f"Position size calculated for {symbol}: {quantity:.6f} "
                f"(${position_size_usd:.2f} at ${current_price:.2f})"
            )
            
            return quantity
            
        except Exception as e:
            self.logger.error(f"Error calculating position size for {symbol}: {e}")
            return 0.0
    
    def _get_current_price(self, symbol: str, portfolio_data: Dict[str, Any]) -> float:
        """Get current price for symbol from market data."""
        try:
            # Try to get from portfolio data first
            if 'current_prices' in portfolio_data:
                price = safe_get(portfolio_data['current_prices'], symbol, 0)
                if price > 0:
                    return price
            
            # Try to get from market state
            if hasattr(self, 'market_state') and 'prices' in self.market_state:
                price = safe_get(self.market_state['prices'], symbol, 0)
                if price > 0:
                    return price
            
            # Default fallback price (should be replaced with real market data)
            return 50.0
            
        except Exception as e:
            self.logger.warning(f"Error getting current price for {symbol}: {e}")
            return 50.0
    
    def _calculate_risk_adjustment(self, risk_data: Dict[str, Any], decision_type: DecisionType) -> float:
        """Calculate risk-based position size adjustment."""
        try:
            risk_adjustment = 1.0
            
            # Portfolio risk adjustment
            if 'portfolio_risk' in risk_data:
                current_risk = safe_get(risk_data['portfolio_risk'], 'current_risk', 0.5)
                if current_risk > 0.8:  # High portfolio risk
                    risk_adjustment *= 0.4
                elif current_risk > 0.6:  # Medium-high risk
                    risk_adjustment *= 0.7
                elif current_risk < 0.3:  # Low portfolio risk
                    risk_adjustment *= 1.3
                elif current_risk < 0.5:  # Medium-low risk
                    risk_adjustment *= 1.1
            
            # Decision type adjustment
            if decision_type in [DecisionType.EMERGENCY_EXIT, DecisionType.CLOSE_POSITION]:
                risk_adjustment *= 1.5  # Allow larger positions for exits
            elif decision_type == DecisionType.ADJUST_POSITION:
                risk_adjustment *= 0.8  # More conservative for adjustments
            
            return max(0.1, min(2.0, risk_adjustment))
            
        except Exception as e:
            self.logger.warning(f"Error calculating risk adjustment: {e}")
            return 1.0
    
    def _calculate_market_adjustment(self) -> float:
        """Calculate market condition-based adjustment."""
        try:
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            if market_condition == 'high_volatility' or volatility > 0.7:
                return 0.6  # Reduce position size in high volatility
            elif market_condition == 'crisis':
                return 0.3  # Significantly reduce in crisis
            elif market_condition == 'trending':
                return 1.2  # Slightly increase in trending markets
            elif market_condition == 'low_volatility' or volatility < 0.3:
                return 1.1  # Slightly increase in stable markets
            
            return 1.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating market adjustment: {e}")
            return 1.0
    
    def _calculate_volatility_adjustment(self, symbol: str, portfolio_data: Dict[str, Any]) -> float:
        """Calculate symbol-specific volatility adjustment."""
        try:
            # Try to get symbol-specific volatility
            if 'volatility' in portfolio_data:
                symbol_volatility = safe_get(portfolio_data['volatility'], symbol, 0.5)
                if symbol_volatility > 0.8:
                    return 0.5  # High volatility stock
                elif symbol_volatility > 0.6:
                    return 0.7  # Medium-high volatility
                elif symbol_volatility < 0.2:
                    return 1.2  # Low volatility stock
                elif symbol_volatility < 0.4:
                    return 1.1  # Medium-low volatility
            
            return 1.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating volatility adjustment for {symbol}: {e}")
            return 1.0
    
    def _apply_position_limits(self, quantity: float, portfolio_value: float, 
                             current_price: float, symbol: str) -> float:
        """Apply position size limits and constraints."""
        try:
            # Minimum quantity (e.g., 0.001 shares or $1 worth)
            min_quantity = max(0.001, 1.0 / current_price)
            
            # Maximum quantity based on portfolio size
            max_quantity_by_portfolio = (portfolio_value * self.max_position_size) / current_price
            
            # Maximum quantity based on daily volume (if available)
            max_quantity_by_volume = float('inf')  # Default to no limit
            
            # Apply all limits
            quantity = max(min_quantity, quantity)
            quantity = min(quantity, max_quantity_by_portfolio)
            quantity = min(quantity, max_quantity_by_volume)
            
            return quantity
            
        except Exception as e:
            self.logger.warning(f"Error applying position limits for {symbol}: {e}")
            return max(0.001, quantity)
    
    async def _calculate_price_targets(self, symbol: str, decision_type: DecisionType,
                                     analysis_data: Dict[str, Any]) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Calculate sophisticated price targets with multiple data sources."""
        try:
            # Input validation
            if not symbol or not decision_type:
                return None, None, None
            
            # Get current price from multiple sources
            current_price = self._get_current_price_for_targets(symbol, analysis_data)
            if current_price <= 0:
                self.logger.warning(f"Invalid current price for {symbol}: {current_price}")
                return None, None, None
            
            # Initialize targets
            price_target = None
            stop_loss = None
            take_profit = None
            
            # Get volatility for dynamic target calculation
            volatility = self._get_symbol_volatility(symbol, analysis_data)
            
            # Calculate prediction-based price target
            price_target = await self._calculate_prediction_target(symbol, analysis_data)
            
            # Calculate technical analysis-based targets
            technical_targets = self._calculate_technical_targets(symbol, current_price, analysis_data)
            
            # Combine targets if both available
            if price_target and technical_targets.get('target'):
                # Weight prediction vs technical (60% prediction, 40% technical)
                price_target = (price_target * 0.6) + (technical_targets['target'] * 0.4)
            elif technical_targets.get('target') and not price_target:
                price_target = technical_targets['target']
            
            # Calculate stop loss and take profit based on decision type and volatility
            if decision_type == DecisionType.BUY:
                stop_loss, take_profit = self._calculate_buy_targets(
                    current_price, price_target, volatility, technical_targets
                )
            elif decision_type == DecisionType.SELL:
                stop_loss, take_profit = self._calculate_sell_targets(
                    current_price, price_target, volatility, technical_targets
                )
            elif decision_type == DecisionType.CLOSE_POSITION:
                # For closing positions, use tighter targets
                stop_loss, take_profit = self._calculate_close_targets(
                    current_price, volatility
                )
            elif decision_type == DecisionType.ADJUST_POSITION:
                # For position adjustments, use moderate targets
                stop_loss, take_profit = self._calculate_adjustment_targets(
                    current_price, volatility
                )
            
            # Validate and adjust targets
            price_target, stop_loss, take_profit = self._validate_targets(
                current_price, price_target, stop_loss, take_profit, decision_type
            )
            
            self.logger.debug(
                f"Price targets for {symbol} ({decision_type.value}): "
                f"Target=${price_target:.2f if price_target else 'None'}, "
                f"Stop=${stop_loss:.2f if stop_loss else 'None'}, "
                f"Take=${take_profit:.2f if take_profit else 'None'}"
            )
            
            return price_target, stop_loss, take_profit
            
        except Exception as e:
            self.logger.error(f"Error calculating price targets for {symbol}: {e}")
            return None, None, None
    
    def _get_current_price_for_targets(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Get current price for target calculations."""
        try:
            # Try to get from analysis data first
            if 'market_data' in analysis_data:
                market_data = analysis_data['market_data']
                if symbol in market_data:
                    price = safe_get(market_data[symbol], 'current_price', 0)
                    if price > 0:
                        return price
            
            # Try technical analysis data
            if 'technical_analysis' in analysis_data:
                tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
                price = safe_get(tech_data, 'current_price', 0)
                if price > 0:
                    return price
            
            # Fallback to market state
            if hasattr(self, 'market_state') and 'prices' in self.market_state:
                price = safe_get(self.market_state['prices'], symbol, 0)
                if price > 0:
                    return price
            
            # Default fallback
            return 50.0
            
        except Exception as e:
            self.logger.warning(f"Error getting current price for {symbol}: {e}")
            return 50.0
    
    def _get_symbol_volatility(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Get symbol volatility for target calculations."""
        try:
            # Try to get from technical analysis
            if 'technical_analysis' in analysis_data:
                tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
                volatility = safe_get(tech_data, 'volatility', 0)
                if volatility > 0:
                    return volatility
            
            # Try market data
            if 'market_data' in analysis_data:
                market_data = safe_get(analysis_data['market_data'], symbol, {})
                volatility = safe_get(market_data, 'volatility', 0)
                if volatility > 0:
                    return volatility
            
            # Default moderate volatility
            return 0.5
            
        except Exception as e:
            self.logger.warning(f"Error getting volatility for {symbol}: {e}")
            return 0.5
    
    async def _calculate_prediction_target(self, symbol: str, analysis_data: Dict[str, Any]) -> Optional[float]:
        """Calculate price target from predictions."""
        try:
            if 'predictions' not in analysis_data:
                return None
            
            pred_data = analysis_data['predictions']
            predictions = safe_get(pred_data, 'predictions', [])
            
            # Filter predictions for this symbol and price type
            symbol_predictions = [
                p for p in predictions 
                if (hasattr(p, 'symbol') and p.symbol == symbol and 
                    hasattr(p, 'prediction_type') and p.prediction_type == 'price')
            ]
            
            if not symbol_predictions:
                return None
            
            # Use highest confidence prediction
            best_prediction = max(symbol_predictions, key=lambda x: getattr(x, 'confidence', 0))
            return getattr(best_prediction, 'predicted_value', None)
            
        except Exception as e:
            self.logger.warning(f"Error calculating prediction target for {symbol}: {e}")
            return None
    
    def _calculate_technical_targets(self, symbol: str, current_price: float, 
                                   analysis_data: Dict[str, Any]) -> Dict[str, Optional[float]]:
        """Calculate targets from technical analysis."""
        try:
            targets = {'target': None, 'support': None, 'resistance': None}
            
            if 'technical_analysis' not in analysis_data:
                return targets
            
            tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
            
            # Get support and resistance levels
            targets['support'] = safe_get(tech_data, 'support_level', None)
            targets['resistance'] = safe_get(tech_data, 'resistance_level', None)
            
            # Calculate target based on trend direction
            trend = safe_get(tech_data, 'trend', 'neutral')
            if trend == 'bullish' and targets['resistance']:
                targets['target'] = targets['resistance']
            elif trend == 'bearish' and targets['support']:
                targets['target'] = targets['support']
            else:
                # Use moving average as target
                ma_20 = safe_get(tech_data, 'ma_20', None)
                if ma_20:
                    targets['target'] = ma_20
            
            return targets
            
        except Exception as e:
            self.logger.warning(f"Error calculating technical targets for {symbol}: {e}")
            return {'target': None, 'support': None, 'resistance': None}
    
    def _calculate_buy_targets(self, current_price: float, price_target: Optional[float], 
                             volatility: float, technical_targets: Dict[str, Optional[float]]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate stop loss and take profit for buy decisions."""
        try:
            # Dynamic stop loss based on volatility (1.5% to 4%)
            stop_loss_pct = 0.015 + (volatility * 0.025)  # 1.5% + volatility adjustment
            stop_loss = current_price * (1 - stop_loss_pct)
            
            # Use support level if available and closer
            if technical_targets.get('support'):
                support_stop = technical_targets['support'] * 0.98  # 2% below support
                if support_stop > stop_loss and support_stop < current_price:
                    stop_loss = support_stop
            
            # Dynamic take profit (2x to 4x risk)
            risk_amount = current_price - stop_loss
            risk_reward_ratio = 2.5 + (volatility * 1.5)  # 2.5x to 4x
            take_profit = current_price + (risk_amount * risk_reward_ratio)
            
            # Use price target if available and reasonable
            if price_target and price_target > current_price:
                # Use the more conservative of price target or calculated take profit
                if price_target < take_profit:
                    take_profit = price_target
            
            return stop_loss, take_profit
            
        except Exception as e:
            self.logger.warning(f"Error calculating buy targets: {e}")
            return None, None
    
    def _calculate_sell_targets(self, current_price: float, price_target: Optional[float], 
                              volatility: float, technical_targets: Dict[str, Optional[float]]) -> Tuple[Optional[float], Optional[float]]:
        """Calculate stop loss and take profit for sell decisions."""
        try:
            # Dynamic stop loss based on volatility (1.5% to 4% above current)
            stop_loss_pct = 0.015 + (volatility * 0.025)
            stop_loss = current_price * (1 + stop_loss_pct)
            
            # Use resistance level if available and closer
            if technical_targets.get('resistance'):
                resistance_stop = technical_targets['resistance'] * 1.02  # 2% above resistance
                if resistance_stop < stop_loss and resistance_stop > current_price:
                    stop_loss = resistance_stop
            
            # Dynamic take profit
            risk_amount = stop_loss - current_price
            risk_reward_ratio = 2.5 + (volatility * 1.5)
            take_profit = current_price - (risk_amount * risk_reward_ratio)
            
            # Use price target if available and reasonable
            if price_target and price_target < current_price:
                if price_target > take_profit:
                    take_profit = price_target
            
            return stop_loss, take_profit
            
        except Exception as e:
            self.logger.warning(f"Error calculating sell targets: {e}")
            return None, None
    
    def _calculate_close_targets(self, current_price: float, volatility: float) -> Tuple[Optional[float], Optional[float]]:
        """Calculate targets for closing positions."""
        try:
            # Tighter stops for closing positions
            stop_loss_pct = 0.01 + (volatility * 0.015)  # 1% to 2.5%
            stop_loss = current_price * (1 + stop_loss_pct)  # Above current for short close
            
            # Quick take profit
            take_profit_pct = 0.02 + (volatility * 0.02)  # 2% to 4%
            take_profit = current_price * (1 - take_profit_pct)  # Below current for short close
            
            return stop_loss, take_profit
            
        except Exception as e:
            self.logger.warning(f"Error calculating close targets: {e}")
            return None, None
    
    def _calculate_adjustment_targets(self, current_price: float, volatility: float) -> Tuple[Optional[float], Optional[float]]:
        """Calculate targets for position adjustments."""
        try:
            # Moderate stops for adjustments
            stop_loss_pct = 0.02 + (volatility * 0.02)  # 2% to 4%
            stop_loss = current_price * (1 - stop_loss_pct)
            
            # Moderate take profit
            take_profit_pct = 0.03 + (volatility * 0.025)  # 3% to 5.5%
            take_profit = current_price * (1 + take_profit_pct)
            
            return stop_loss, take_profit
            
        except Exception as e:
            self.logger.warning(f"Error calculating adjustment targets: {e}")
            return None, None
    
    def _validate_targets(self, current_price: float, price_target: Optional[float], 
                        stop_loss: Optional[float], take_profit: Optional[float], 
                        decision_type: DecisionType) -> Tuple[Optional[float], Optional[float], Optional[float]]:
        """Validate and adjust price targets."""
        try:
            # Ensure stop loss is on correct side
            if stop_loss:
                if decision_type == DecisionType.BUY and stop_loss >= current_price:
                    stop_loss = current_price * 0.98  # 2% below
                elif decision_type == DecisionType.SELL and stop_loss <= current_price:
                    stop_loss = current_price * 1.02  # 2% above
            
            # Ensure take profit is on correct side
            if take_profit:
                if decision_type == DecisionType.BUY and take_profit <= current_price:
                    take_profit = current_price * 1.05  # 5% above
                elif decision_type == DecisionType.SELL and take_profit >= current_price:
                    take_profit = current_price * 0.95  # 5% below
            
            # Ensure reasonable risk/reward ratio
            if stop_loss and take_profit:
                if decision_type == DecisionType.BUY:
                    risk = current_price - stop_loss
                    reward = take_profit - current_price
                    if risk > 0 and reward / risk < 1.5:  # Minimum 1.5:1 ratio
                        take_profit = current_price + (risk * 1.5)
                elif decision_type == DecisionType.SELL:
                    risk = stop_loss - current_price
                    reward = current_price - take_profit
                    if risk > 0 and reward / risk < 1.5:
                        take_profit = current_price - (risk * 1.5)
            
            return price_target, stop_loss, take_profit
            
        except Exception as e:
            self.logger.warning(f"Error validating targets: {e}")
            return price_target, stop_loss, take_profit
    
    def _calculate_signal_strength(self, signals: Dict[str, Any]) -> float:
        """Calculate overall signal strength from technical indicators."""
        if not signals:
            return 0
        
        try:
            # Weight different signal types
            signal_weights = {
                'trend': 0.3,
                'momentum': 0.25,
                'volume': 0.2,
                'volatility': 0.15,
                'support_resistance': 0.1
            }
            
            weighted_score = 0
            total_weight = 0
            
            for signal_type, weight in signal_weights.items():
                if signal_type in signals:
                    signal_value = signals[signal_type]
                    
                    # Normalize signal value to -1 to 1 range
                    if isinstance(signal_value, (int, float)):
                        normalized_value = max(-1, min(1, signal_value))
                        weighted_score += normalized_value * weight
                        total_weight += weight
            
            if total_weight > 0:
                return abs(weighted_score / total_weight)
            
        except Exception as e:
            self.logger.warning(f"Error calculating signal strength: {e}")
        
        return 0
    
    def _extract_symbol_from_pattern(self, pattern) -> Optional[str]:
        """Extract symbol from pattern description."""
        try:
            # This would parse the pattern description to extract symbol
            # For now, return a placeholder
            description = getattr(pattern, 'description', '')
            
            # Simple extraction logic (would be more sophisticated in practice)
            if 'BTC' in description:
                return 'BTC/USDT'
            elif 'ETH' in description:
                return 'ETH/USDT'
            
            return None
            
        except Exception:
            return None
    
    def _merge_opportunities(self, opportunities: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Merge similar opportunities for the same symbol."""
        merged = {}
        
        for opp in opportunities:
            symbol = opp['symbol']
            
            if symbol not in merged:
                merged[symbol] = opp
            else:
                # Merge with existing opportunity
                existing = merged[symbol]
                
                # Take the stronger signal
                if opp['strength'] > existing['strength']:
                    merged[symbol] = opp
                else:
                    # Combine strengths
                    existing['strength'] = (existing['strength'] + opp['strength']) / 2
        
        return list(merged.values())
    
    def _calculate_risk_score(self, symbol: str, decision_type: DecisionType, 
                            quantity: float, analysis_data: Dict[str, Any]) -> float:
        """Calculate comprehensive risk score for a decision."""
        try:
            # Input validation
            if not symbol or not decision_type:
                return 0.5
            
            # Initialize risk components
            base_risk = 0.3  # Conservative base risk
            volatility_risk = 0.0
            market_risk = 0.0
            position_risk = 0.0
            technical_risk = 0.0
            
            # 1. Volatility Risk Assessment
            volatility_risk = self._calculate_volatility_risk(symbol, analysis_data)
            
            # 2. Market Condition Risk
            market_risk = self._calculate_market_risk()
            
            # 3. Position Size Risk
            position_risk = self._calculate_position_risk(quantity, symbol, analysis_data)
            
            # 4. Technical Risk (trend reversal, support/resistance breaks)
            technical_risk = self._calculate_technical_risk(symbol, analysis_data)
            
            # 5. Liquidity Risk
            liquidity_risk = self._calculate_liquidity_risk(symbol, analysis_data)
            
            # 6. Correlation Risk (if multiple positions)
            correlation_risk = self._calculate_correlation_risk(symbol)
            
            # Combine risk factors with weights
            risk_weights = {
                'base': 0.15,
                'volatility': 0.25,
                'market': 0.20,
                'position': 0.15,
                'technical': 0.15,
                'liquidity': 0.05,
                'correlation': 0.05
            }
            
            total_risk = (
                base_risk * risk_weights['base'] +
                volatility_risk * risk_weights['volatility'] +
                market_risk * risk_weights['market'] +
                position_risk * risk_weights['position'] +
                technical_risk * risk_weights['technical'] +
                liquidity_risk * risk_weights['liquidity'] +
                correlation_risk * risk_weights['correlation']
            )
            
            # Apply decision type adjustments
            if decision_type == DecisionType.SELL:
                total_risk *= 1.1  # Short positions generally riskier
            elif decision_type == DecisionType.CLOSE_POSITION:
                total_risk *= 0.8  # Closing positions reduces risk
            elif decision_type == DecisionType.EMERGENCY_EXIT:
                total_risk *= 0.5  # Emergency exits are risk reduction
            
            # Ensure risk is within bounds
            return max(0.05, min(0.95, total_risk))
            
        except Exception as e:
            self.logger.warning(f"Error calculating risk score for {symbol}: {e}")
            return 0.5
    
    def _calculate_volatility_risk(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate risk from volatility."""
        try:
            volatility = 0.5  # Default moderate volatility
            
            # Get volatility from predictions
            if 'predictions' in analysis_data:
                pred_data = analysis_data['predictions']
                predictions = safe_get(pred_data, 'predictions', [])
                
                volatility_preds = [
                    p for p in predictions 
                    if (hasattr(p, 'symbol') and p.symbol == symbol and 
                        hasattr(p, 'prediction_type') and p.prediction_type == 'volatility')
                ]
                
                if volatility_preds:
                    # Use confidence-weighted average
                    weighted_vol = sum(getattr(p, 'predicted_value', 0.5) * getattr(p, 'confidence', 0.5) 
                                     for p in volatility_preds)
                    total_confidence = sum(getattr(p, 'confidence', 0.5) for p in volatility_preds)
                    
                    if total_confidence > 0:
                        volatility = weighted_vol / total_confidence
            
            # Get volatility from technical analysis
            if 'technical_analysis' in analysis_data:
                tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
                tech_volatility = safe_get(tech_data, 'volatility', 0)
                if tech_volatility > 0:
                    volatility = (volatility + tech_volatility) / 2  # Average with prediction
            
            # Convert volatility to risk score (higher volatility = higher risk)
            return min(0.9, volatility * 1.5)  # Scale and cap
            
        except Exception as e:
            self.logger.warning(f"Error calculating volatility risk for {symbol}: {e}")
            return 0.5
    
    def _calculate_market_risk(self) -> float:
        """Calculate risk from market conditions."""
        try:
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            # Base market risk
            if market_condition == 'high_volatility':
                return 0.8
            elif market_condition == 'crisis':
                return 0.9
            elif market_condition == 'low_volatility':
                return 0.2
            elif market_condition == 'trending':
                return 0.3
            elif market_condition == 'sideways':
                return 0.4
            else:
                # Use volatility as proxy
                return min(0.8, market_volatility * 1.2)
                
        except Exception as e:
            self.logger.warning(f"Error calculating market risk: {e}")
            return 0.5
    
    def _calculate_position_risk(self, quantity: float, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate risk from position size."""
        try:
            if quantity <= 0:
                return 0.0
            
            # Get current price for position value calculation
            current_price = self._get_current_price_for_targets(symbol, analysis_data)
            position_value = quantity * current_price
            
            # Get portfolio value
            portfolio_value = safe_get(self.market_state, 'portfolio_value', 10000)
            
            # Calculate position as percentage of portfolio
            position_pct = position_value / portfolio_value if portfolio_value > 0 else 0
            
            # Risk increases exponentially with position size
            if position_pct > 0.5:  # More than 50% of portfolio
                return 0.9
            elif position_pct > 0.3:  # More than 30%
                return 0.7
            elif position_pct > 0.2:  # More than 20%
                return 0.5
            elif position_pct > 0.1:  # More than 10%
                return 0.3
            else:
                return position_pct * 2  # Linear scaling for small positions
                
        except Exception as e:
            self.logger.warning(f"Error calculating position risk for {symbol}: {e}")
            return 0.3
    
    def _calculate_technical_risk(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate risk from technical indicators."""
        try:
            if 'technical_analysis' not in analysis_data:
                return 0.4
            
            tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
            risk_score = 0.4  # Base technical risk
            
            # Check trend strength
            trend = safe_get(tech_data, 'trend', 'neutral')
            trend_strength = safe_get(tech_data, 'trend_strength', 0.5)
            
            if trend == 'neutral' or trend_strength < 0.3:
                risk_score += 0.2  # Higher risk in weak/no trend
            
            # Check support/resistance proximity
            current_price = safe_get(tech_data, 'current_price', 0)
            support = safe_get(tech_data, 'support_level', 0)
            resistance = safe_get(tech_data, 'resistance_level', 0)
            
            if current_price > 0:
                if support > 0:
                    support_distance = (current_price - support) / current_price
                    if support_distance < 0.02:  # Within 2% of support
                        risk_score += 0.15
                
                if resistance > 0:
                    resistance_distance = (resistance - current_price) / current_price
                    if resistance_distance < 0.02:  # Within 2% of resistance
                        risk_score += 0.15
            
            # Check RSI for overbought/oversold conditions
            rsi = safe_get(tech_data, 'rsi', 50)
            if rsi > 80 or rsi < 20:
                risk_score += 0.1  # Higher risk in extreme RSI conditions
            
            return min(0.9, risk_score)
            
        except Exception as e:
            self.logger.warning(f"Error calculating technical risk for {symbol}: {e}")
            return 0.4
    
    def _calculate_liquidity_risk(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate risk from liquidity constraints."""
        try:
            # Get volume data
            if 'market_data' in analysis_data:
                market_data = safe_get(analysis_data['market_data'], symbol, {})
                volume = safe_get(market_data, 'volume', 0)
                avg_volume = safe_get(market_data, 'avg_volume', 0)
                
                if avg_volume > 0:
                    volume_ratio = volume / avg_volume
                    if volume_ratio < 0.5:  # Low volume
                        return 0.6
                    elif volume_ratio < 0.8:
                        return 0.4
                    else:
                        return 0.2
            
            # Default moderate liquidity risk
            return 0.3
            
        except Exception as e:
            self.logger.warning(f"Error calculating liquidity risk for {symbol}: {e}")
            return 0.3
    
    def _calculate_correlation_risk(self, symbol: str) -> float:
        """Calculate risk from portfolio correlation."""
        try:
            # This would analyze correlation with existing positions
            # For now, return low correlation risk
            return 0.2
            
        except Exception as e:
            self.logger.warning(f"Error calculating correlation risk for {symbol}: {e}")
            return 0.2
    
    def _calculate_expected_return(self, symbol: str, decision_type: DecisionType,
                                 analysis_data: Dict[str, Any]) -> float:
        """Calculate sophisticated expected return for a decision."""
        try:
            # Input validation
            if not symbol or not decision_type:
                return 0.0
            
            # Initialize return components
            prediction_return = 0.0
            technical_return = 0.0
            sentiment_return = 0.0
            
            # 1. Get prediction-based expected return
            prediction_return = self._calculate_prediction_return(symbol, analysis_data)
            
            # 2. Get technical analysis-based return
            technical_return = self._calculate_technical_return(symbol, analysis_data)
            
            # 3. Get sentiment-based return
            sentiment_return = self._calculate_sentiment_return(symbol, analysis_data)
            
            # Combine returns with weights
            return_weights = {
                'prediction': 0.5,
                'technical': 0.3,
                'sentiment': 0.2
            }
            
            combined_return = (
                prediction_return * return_weights['prediction'] +
                technical_return * return_weights['technical'] +
                sentiment_return * return_weights['sentiment']
            )
            
            # Apply decision type adjustments
            if decision_type == DecisionType.SELL:
                combined_return = -combined_return  # Inverse for short positions
            elif decision_type == DecisionType.CLOSE_POSITION:
                # For closing, use smaller expected return (quick exit)
                combined_return *= 0.5
            elif decision_type == DecisionType.ADJUST_POSITION:
                # For adjustments, moderate return expectation
                combined_return *= 0.7
            
            # Apply market condition adjustments
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            if market_condition == 'high_volatility':
                combined_return *= 0.8  # Lower expectations in volatile markets
            elif market_condition == 'trending':
                combined_return *= 1.2  # Higher expectations in trending markets
            elif market_condition == 'crisis':
                combined_return *= 0.5  # Much lower expectations in crisis
            
            # Ensure reasonable bounds
            return max(-0.5, min(0.5, combined_return))  # Cap at ±50%
            
        except Exception as e:
            self.logger.warning(f"Error calculating expected return for {symbol}: {e}")
            return 0.0
    
    def _calculate_prediction_return(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate expected return from price predictions."""
        try:
            if 'predictions' not in analysis_data:
                return 0.0
            
            pred_data = analysis_data['predictions']
            predictions = safe_get(pred_data, 'predictions', [])
            
            # Filter price predictions for this symbol
            price_preds = [
                p for p in predictions 
                if (hasattr(p, 'symbol') and p.symbol == symbol and 
                    hasattr(p, 'prediction_type') and p.prediction_type == 'price')
            ]
            
            if not price_preds:
                return 0.0
            
            # Get current price
            current_price = self._get_current_price_for_targets(symbol, analysis_data)
            if current_price <= 0:
                return 0.0
            
            # Calculate confidence-weighted expected return
            weighted_return = 0.0
            total_confidence = 0.0
            
            for pred in price_preds:
                predicted_price = getattr(pred, 'predicted_value', current_price)
                confidence = getattr(pred, 'confidence', 0.5)
                
                # Calculate return
                expected_return = (predicted_price - current_price) / current_price
                
                weighted_return += expected_return * confidence
                total_confidence += confidence
            
            if total_confidence > 0:
                return weighted_return / total_confidence
            
            return 0.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating prediction return for {symbol}: {e}")
            return 0.0
    
    def _calculate_technical_return(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate expected return from technical analysis."""
        try:
            if 'technical_analysis' not in analysis_data:
                return 0.0
            
            tech_data = safe_get(analysis_data['technical_analysis'], symbol, {})
            current_price = safe_get(tech_data, 'current_price', 0)
            
            if current_price <= 0:
                return 0.0
            
            # Get trend direction and strength
            trend = safe_get(tech_data, 'trend', 'neutral')
            trend_strength = safe_get(tech_data, 'trend_strength', 0.5)
            
            # Calculate target based on support/resistance
            target_price = current_price
            
            if trend == 'bullish':
                resistance = safe_get(tech_data, 'resistance_level', 0)
                if resistance > current_price:
                    target_price = resistance
                else:
                    target_price = current_price * (1 + trend_strength * 0.1)  # Up to 10% based on strength
            elif trend == 'bearish':
                support = safe_get(tech_data, 'support_level', 0)
                if support > 0 and support < current_price:
                    target_price = support
                else:
                    target_price = current_price * (1 - trend_strength * 0.1)  # Down to 10% based on strength
            
            # Calculate expected return
            expected_return = (target_price - current_price) / current_price
            
            # Adjust for trend strength
            return expected_return * trend_strength
            
        except Exception as e:
            self.logger.warning(f"Error calculating technical return for {symbol}: {e}")
            return 0.0
    
    def _calculate_sentiment_return(self, symbol: str, analysis_data: Dict[str, Any]) -> float:
        """Calculate expected return from sentiment analysis."""
        try:
            if 'sentiment_analysis' not in analysis_data:
                return 0.0
            
            sentiment_data = safe_get(analysis_data['sentiment_analysis'], symbol, {})
            
            # Get overall sentiment score
            sentiment_score = safe_get(sentiment_data, 'overall_sentiment', 0.5)
            sentiment_strength = safe_get(sentiment_data, 'sentiment_strength', 0.5)
            
            # Convert sentiment to expected return
            # Sentiment of 0.5 = neutral (0% return)
            # Sentiment of 1.0 = very positive (up to 5% return)
            # Sentiment of 0.0 = very negative (down to -5% return)
            
            sentiment_return = (sentiment_score - 0.5) * 0.1  # ±5% max
            
            # Adjust for sentiment strength
            return sentiment_return * sentiment_strength
            
        except Exception as e:
            self.logger.warning(f"Error calculating sentiment return for {symbol}: {e}")
            return 0.0
    
    def _generate_reasoning(self, opportunity: Dict[str, Any], 
                          scores: Dict[str, float],
                          analysis_data: Dict[str, Any]) -> List[str]:
        """Generate comprehensive reasoning for a trading decision."""
        reasoning = []
        
        try:
            # Input validation
            if not opportunity or not scores:
                return ["Decision based on comprehensive market analysis"]
            
            symbol = opportunity.get('symbol', 'Unknown')
            opp_type = opportunity.get('type', 'unknown')
            strength = opportunity.get('strength', 0)
            
            # Primary opportunity reasoning
            if strength > 0.8:
                reasoning.append(f"Very strong {opp_type} signal for {symbol} ({strength:.1%} confidence)")
            elif strength > 0.6:
                reasoning.append(f"Strong {opp_type} opportunity identified for {symbol} ({strength:.1%} confidence)")
            elif strength > 0.4:
                reasoning.append(f"Moderate {opp_type} signal detected for {symbol} ({strength:.1%} confidence)")
            else:
                reasoning.append(f"Weak {opp_type} signal for {symbol} ({strength:.1%} confidence)")
            
            # Score-based detailed reasoning
            high_scores = [(factor, score) for factor, score in scores.items() if score > 0.7]
            moderate_scores = [(factor, score) for factor, score in scores.items() if 0.5 < score <= 0.7]
            
            if high_scores:
                high_factors = ", ".join([f"{factor} ({score:.1%})" for factor, score in high_scores])
                reasoning.append(f"Strong signals from: {high_factors}")
            
            if moderate_scores:
                moderate_factors = ", ".join([f"{factor} ({score:.1%})" for factor, score in moderate_scores])
                reasoning.append(f"Supporting signals from: {moderate_factors}")
            
            # Market condition context
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            if market_condition == 'high_volatility':
                reasoning.append(f"High volatility market ({market_volatility:.1%}) - increased caution applied")
            elif market_condition == 'trending':
                reasoning.append(f"Trending market conditions favor momentum strategies")
            elif market_condition == 'sideways':
                reasoning.append(f"Sideways market - range-bound trading approach")
            else:
                reasoning.append(f"Normal market conditions ({market_condition})")
            
            # Technical analysis insights
            self._add_technical_reasoning(reasoning, symbol, analysis_data)
            
            # Pattern analysis insights
            self._add_pattern_reasoning(reasoning, analysis_data)
            
            # Prediction insights
            self._add_prediction_reasoning(reasoning, symbol, analysis_data)
            
            # Sentiment insights
            self._add_sentiment_reasoning(reasoning, symbol, analysis_data)
            
            # Risk considerations
            self._add_risk_reasoning(reasoning, opportunity, scores)
            
            # Fallback reasoning
            if len(reasoning) < 2:
                reasoning.append("Decision based on comprehensive multi-factor analysis")
                reasoning.append("AI-driven trading algorithm recommendation")
            
        except Exception as e:
            self.logger.warning(f"Error generating reasoning: {e}")
            reasoning = ["Automated trading decision based on AI analysis", 
                        "Multiple market factors considered"]
        
        return reasoning[:8]  # Limit to 8 reasons for readability
    
    def _add_technical_reasoning(self, reasoning: List[str], symbol: str, analysis_data: Dict[str, Any]) -> None:
        """Add technical analysis insights to reasoning."""
        try:
            tech_data = analysis_data.get('technical_analysis', {})
            if not tech_data:
                return
            
            # RSI insights
            rsi = safe_get(tech_data, f'{symbol}.rsi', None)
            if rsi is not None:
                if rsi > 70:
                    reasoning.append(f"RSI indicates overbought conditions ({rsi:.1f})")
                elif rsi < 30:
                    reasoning.append(f"RSI indicates oversold conditions ({rsi:.1f})")
                elif 45 <= rsi <= 55:
                    reasoning.append(f"RSI shows neutral momentum ({rsi:.1f})")
            
            # MACD insights
            macd_data = safe_get(tech_data, f'{symbol}.macd', {})
            if macd_data:
                macd_line = macd_data.get('macd', 0)
                signal_line = macd_data.get('signal', 0)
                if macd_line > signal_line:
                    reasoning.append("MACD shows bullish momentum")
                elif macd_line < signal_line:
                    reasoning.append("MACD shows bearish momentum")
            
            # Moving averages
            ma_data = safe_get(tech_data, f'{symbol}.moving_averages', {})
            if ma_data:
                sma_20 = ma_data.get('sma_20')
                sma_50 = ma_data.get('sma_50')
                if sma_20 and sma_50:
                    if sma_20 > sma_50:
                        reasoning.append("Short-term MA above long-term MA (bullish)")
                    else:
                        reasoning.append("Short-term MA below long-term MA (bearish)")
                        
        except Exception as e:
            self.logger.debug(f"Error adding technical reasoning: {e}")
    
    def _add_pattern_reasoning(self, reasoning: List[str], analysis_data: Dict[str, Any]) -> None:
        """Add pattern analysis insights to reasoning."""
        try:
            pattern_data = analysis_data.get('pattern_analysis', {})
            patterns = pattern_data.get('patterns', [])
            
            if patterns:
                # Get top 2 patterns
                top_patterns = sorted(patterns, key=lambda x: x.confidence, reverse=True)[:2]
                
                for pattern in top_patterns:
                    if pattern.confidence > 0.6:
                        reasoning.append(f"Strong {pattern.name} pattern detected ({pattern.confidence:.1%})")
                    elif pattern.confidence > 0.4:
                        reasoning.append(f"Moderate {pattern.name} pattern identified ({pattern.confidence:.1%})")
                        
        except Exception as e:
            self.logger.debug(f"Error adding pattern reasoning: {e}")
    
    def _add_prediction_reasoning(self, reasoning: List[str], symbol: str, analysis_data: Dict[str, Any]) -> None:
        """Add prediction insights to reasoning."""
        try:
            pred_data = analysis_data.get('predictions', {})
            predictions = pred_data.get('predictions', [])
            
            symbol_predictions = [p for p in predictions if p.symbol == symbol]
            if symbol_predictions:
                # Get most confident prediction
                best_pred = max(symbol_predictions, key=lambda x: x.confidence)
                
                direction = "upward" if best_pred.predicted_price_change > 0 else "downward"
                change_pct = abs(best_pred.predicted_price_change) * 100
                
                reasoning.append(f"AI predicts {direction} movement of {change_pct:.1f}% ({best_pred.confidence:.1%} confidence)")
                
        except Exception as e:
            self.logger.debug(f"Error adding prediction reasoning: {e}")
    
    def _add_sentiment_reasoning(self, reasoning: List[str], symbol: str, analysis_data: Dict[str, Any]) -> None:
        """Add sentiment analysis insights to reasoning."""
        try:
            sentiment_data = analysis_data.get('sentiment_analysis', {})
            symbol_sentiment = sentiment_data.get(symbol, {})
            
            if symbol_sentiment:
                overall_sentiment = symbol_sentiment.get('overall_sentiment', 0)
                confidence = symbol_sentiment.get('confidence', 0)
                
                if confidence > 0.6:
                    if overall_sentiment > 0.6:
                        reasoning.append(f"Strong positive market sentiment ({overall_sentiment:.1%})")
                    elif overall_sentiment < -0.6:
                        reasoning.append(f"Strong negative market sentiment ({abs(overall_sentiment):.1%})")
                    elif abs(overall_sentiment) < 0.2:
                        reasoning.append("Neutral market sentiment")
                        
        except Exception as e:
            self.logger.debug(f"Error adding sentiment reasoning: {e}")
    
    def _add_risk_reasoning(self, reasoning: List[str], opportunity: Dict[str, Any], scores: Dict[str, float]) -> None:
        """Add risk-related insights to reasoning."""
        try:
            # Risk level assessment
            avg_score = np.mean(list(scores.values())) if scores else 0
            strength = opportunity.get('strength', 0)
            
            if avg_score > 0.8 and strength > 0.8:
                reasoning.append("High confidence, low risk opportunity")
            elif avg_score > 0.6 and strength > 0.6:
                reasoning.append("Moderate confidence, acceptable risk level")
            elif avg_score < 0.4 or strength < 0.4:
                reasoning.append("Lower confidence signals - proceed with caution")
            
            # Market volatility consideration
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            if market_volatility > 0.7:
                reasoning.append("High market volatility increases position risk")
            elif market_volatility < 0.3:
                reasoning.append("Low volatility environment supports stable positions")
                
        except Exception as e:
            self.logger.debug(f"Error adding risk reasoning: {e}")
    
    def _calculate_priority(self, confidence: float, risk_score: float, expected_return: float) -> int:
        """Calculate sophisticated priority score for the decision (1-10)."""
        try:
            # Input validation and normalization
            confidence = max(0.0, min(1.0, confidence))
            risk_score = max(0.0, min(1.0, risk_score))
            expected_return = max(-1.0, min(2.0, expected_return))  # Allow negative returns
            
            # Base priority calculation with enhanced weighting
            base_score = (
                confidence * 0.35 +                    # Confidence weight
                (1 - risk_score) * 0.25 +             # Risk adjustment (inverted)
                max(0, expected_return) * 0.25 +      # Positive return weight
                self._calculate_urgency_factor() * 0.15  # Market urgency factor
            )
            
            # Apply market condition modifiers
            market_modifier = self._calculate_market_priority_modifier()
            base_score *= market_modifier
            
            # Apply time-sensitive adjustments
            time_modifier = self._calculate_time_priority_modifier()
            base_score *= time_modifier
            
            # Risk-return ratio bonus
            if risk_score > 0:
                risk_return_ratio = expected_return / risk_score
                if risk_return_ratio > 2.0:  # High return vs risk
                    base_score *= 1.1
                elif risk_return_ratio < 0.5:  # Poor return vs risk
                    base_score *= 0.9
            
            # Confidence threshold adjustments
            if confidence > 0.8:
                base_score *= 1.05  # Boost high confidence decisions
            elif confidence < 0.4:
                base_score *= 0.85  # Reduce low confidence decisions
            
            # Convert to 1-10 scale with better distribution
            if base_score >= 0.9:
                priority = 10
            elif base_score >= 0.8:
                priority = 9
            elif base_score >= 0.7:
                priority = 8
            elif base_score >= 0.6:
                priority = 7
            elif base_score >= 0.5:
                priority = 6
            elif base_score >= 0.4:
                priority = 5
            elif base_score >= 0.3:
                priority = 4
            elif base_score >= 0.2:
                priority = 3
            elif base_score >= 0.1:
                priority = 2
            else:
                priority = 1
            
            return priority
            
        except Exception as e:
            self.logger.debug(f"Error calculating priority: {e}")
            return 5
    
    def _calculate_urgency_factor(self) -> float:
        """Calculate urgency factor based on market conditions and timing."""
        try:
            urgency = 0.5  # Base urgency
            
            # Market volatility urgency
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            if market_volatility > 0.8:
                urgency += 0.3  # High volatility = high urgency
            elif market_volatility > 0.6:
                urgency += 0.1
            elif market_volatility < 0.2:
                urgency -= 0.1  # Low volatility = lower urgency
            
            # Market condition urgency
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            if market_condition == 'trending':
                urgency += 0.2  # Trending markets need quick action
            elif market_condition == 'high_volatility':
                urgency += 0.3  # High volatility needs immediate attention
            elif market_condition == 'sideways':
                urgency -= 0.1  # Sideways markets are less urgent
            
            # Time-based urgency (market close, etc.)
            current_hour = datetime.now().hour
            if 9 <= current_hour <= 10 or 15 <= current_hour <= 16:  # Market open/close
                urgency += 0.1
            
            return max(0.0, min(1.0, urgency))
            
        except Exception:
            return 0.5
    
    def _calculate_market_priority_modifier(self) -> float:
        """Calculate market-based priority modifier."""
        try:
            modifier = 1.0
            
            market_condition = safe_get(self.market_state, 'condition', 'normal')
            market_volatility = safe_get(self.market_state, 'volatility', 0.5)
            
            # Market condition modifiers
            if market_condition == 'trending':
                modifier *= 1.1  # Trending markets favor momentum
            elif market_condition == 'high_volatility':
                modifier *= 0.9  # High volatility reduces priority slightly
            elif market_condition == 'sideways':
                modifier *= 0.95  # Sideways markets slightly reduce priority
            
            # Volatility modifiers
            if market_volatility > 0.8:
                modifier *= 0.85  # Very high volatility reduces priority
            elif market_volatility < 0.2:
                modifier *= 1.05  # Low volatility increases priority
            
            return max(0.5, min(1.5, modifier))
            
        except Exception:
            return 1.0
    
    def _calculate_time_priority_modifier(self) -> float:
        """Calculate time-based priority modifier."""
        try:
            modifier = 1.0
            current_time = datetime.now()
            
            # Market hours consideration
            market_hour = current_time.hour
            
            # Higher priority during active trading hours
            if 9 <= market_hour <= 16:  # Market hours
                if 9 <= market_hour <= 10:  # Market open
                    modifier *= 1.15
                elif 15 <= market_hour <= 16:  # Market close
                    modifier *= 1.1
                else:  # Regular trading hours
                    modifier *= 1.05
            else:  # After hours
                modifier *= 0.8
            
            # Day of week consideration
            weekday = current_time.weekday()
            if weekday == 0:  # Monday
                modifier *= 1.05  # Week start
            elif weekday == 4:  # Friday
                modifier *= 1.02  # Week end
            elif weekday >= 5:  # Weekend
                modifier *= 0.7  # Weekend trading
            
            return max(0.5, min(1.3, modifier))
            
        except Exception:
            return 1.0
    
    def _determine_time_horizon(self, opportunity: Dict[str, Any], 
                              analysis_data: Dict[str, Any]) -> str:
        """Determine time horizon for the decision."""
        try:
            # Check prediction horizons
            if 'predictions' in analysis_data:
                pred_data = analysis_data['predictions']
                predictions = pred_data.get('predictions', [])
                
                symbol = opportunity['symbol']
                symbol_predictions = [p for p in predictions if p.symbol == symbol]
                
                if symbol_predictions:
                    avg_horizon = np.mean([p.prediction_horizon for p in symbol_predictions])
                    
                    if avg_horizon <= 15:
                        return 'short'
                    elif avg_horizon <= 60:
                        return 'medium'
                    else:
                        return 'long'
            
            # Default based on opportunity type
            opp_type = opportunity.get('type', '')
            
            if 'technical' in opp_type:
                return 'short'
            elif 'pattern' in opp_type:
                return 'medium'
            elif 'sentiment' in opp_type:
                return 'short'
            
            return 'medium'
            
        except Exception:
            return 'medium'
    
    def _validate_decision(self, decision: TradingDecision, 
                         portfolio_data: Dict[str, Any],
                         risk_data: Dict[str, Any]) -> bool:
        """Validate a trading decision before execution."""
        try:
            # Check confidence threshold
            if decision.confidence < self.min_confidence_threshold:
                return False
            
            # Check risk limits
            if decision.risk_score > self.risk_tolerance * 2:  # 2x risk tolerance
                return False
            
            # Check position size limits
            portfolio_value = portfolio_data.get('total_value', 100000)
            position_value = decision.quantity * (decision.price_target or 50)
            
            if position_value > portfolio_value * self.max_position_size:
                return False
            
            # Check daily trade limits
            if self.daily_trade_count >= self.max_daily_trades:
                return False
            
            # Check portfolio risk
            if 'portfolio_risk' in risk_data:
                current_risk = risk_data['portfolio_risk'].get('current_risk', 0)
                if current_risk > 0.9:  # Very high portfolio risk
                    return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating decision: {e}")
            return False
    
    async def _update_market_state(self, analysis_data: Dict[str, Any]):
        """Update current market state based on analysis."""
        try:
            # Update market condition
            if 'market_analysis' in analysis_data:
                market_data = analysis_data['market_analysis']
                self.market_state['condition'] = market_data.get('condition', 'neutral')
                self.market_state['volatility'] = market_data.get('volatility', 'medium')
                self.market_state['trend'] = market_data.get('trend', 'sideways')
            
            # Update sentiment
            if 'sentiment_analysis' in analysis_data:
                sentiment_data = analysis_data['sentiment_analysis']
                overall_sentiment = sentiment_data.get('overall_sentiment', {})
                
                sentiment_score = overall_sentiment.get('score', 0)
                
                if sentiment_score > 0.3:
                    self.market_state['sentiment'] = 'positive'
                elif sentiment_score < -0.3:
                    self.market_state['sentiment'] = 'negative'
                else:
                    self.market_state['sentiment'] = 'neutral'
            
        except Exception as e:
            self.logger.warning(f"Error updating market state: {e}")
    
    async def _can_make_trades(self) -> bool:
        """Check if we can make more trades today."""
        await self._reset_daily_counters()
        return self.daily_trade_count < self.max_daily_trades
    
    async def _reset_daily_counters(self):
        """Reset daily counters if it's a new day."""
        today = datetime.now().date()
        
        if self.last_trade_date != today:
            self.daily_trade_count = 0
            self.last_trade_date = today
    
    def _update_decision_history(self, decisions: List[TradingDecision]):
        """Update decision history."""
        for decision in decisions:
            self.decision_history.append({
                'timestamp': decision.timestamp,
                'symbol': decision.symbol,
                'decision_type': decision.decision_type.value,
                'confidence': decision.confidence,
                'quantity': decision.quantity,
                'risk_score': decision.risk_score,
                'expected_return': decision.expected_return
            })
        
        # Keep only recent history
        if len(self.decision_history) > self.max_history_size:
            self.decision_history = self.decision_history[-self.max_history_size:]
    
    async def _update_performance_metrics(self):
        """Update performance metrics."""
        try:
            self.performance_metrics['total_decisions'] = len(self.decision_history)
            
            if self.decision_history:
                # Calculate average return (would need actual outcomes)
                avg_expected_return = np.mean([d['expected_return'] for d in self.decision_history])
                self.performance_metrics['average_return'] = avg_expected_return
            
            self.performance_metrics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.warning(f"Error updating performance metrics: {e}")
    
    async def _save_decision_data(self):
        """Save decision history and performance data."""
        try:
            # In a real implementation, this would save to database
            self.logger.debug("Decision data saving skipped - not implemented")
            
        except Exception as e:
            self.logger.warning(f"Error saving decision data: {e}")
    
    def get_decision_history(self) -> List[Dict[str, Any]]:
        """Get decision history."""
        return self.decision_history.copy()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return self.performance_metrics.copy()
    
    def get_market_state(self) -> Dict[str, Any]:
        """Get current market state."""
        return self.market_state.copy()
    
    def update_decision_weights(self, new_weights: Dict[str, float]):
        """Update decision weights for different factors."""
        for factor, weight in new_weights.items():
            if factor in self.decision_weights:
                self.decision_weights[factor] = weight
        self.logger.info(f"Updated decision weights: {self.decision_weights}")
    
    def _is_test_mode(self) -> bool:
        """Check if we're in test/sandbox mode."""
        # Check if any exchange is in sandbox mode
        # This would typically be passed from the main agent or config
        return getattr(self, '_test_mode', False)
    
    def set_test_mode(self, test_mode: bool):
        """Set test mode for ping-pong strategy."""
        self._test_mode = test_mode
        if test_mode:
            self.logger.info("Test mode enabled - using simple ping-pong strategy")
            # Initialize test state
            self._test_state = {
                'last_action': None,
                'last_action_time': None,
                'trade_count': 0,
                'target_symbol': 'BTCUSDT'  # Default test symbol
            }
    
    async def _make_test_decision(self, analysis_data: Dict[str, Any], 
                                portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Simple ping-pong strategy for testing."""
        try:
            current_time = datetime.now()
            actions = []
            
            # Initialize test state if not exists
            if not hasattr(self, '_test_state'):
                self._test_state = {
                    'last_action': None,
                    'last_action_time': None,
                    'trade_count': 0,
                    'target_symbol': 'BTCUSDT'
                }
            
            # Get current minute
            current_minute = current_time.minute
            
            # Check if enough time has passed since last action (minimum 5 minutes)
            if (self._test_state['last_action_time'] is None or 
                (current_time - self._test_state['last_action_time']).total_seconds() >= 300):
                
                # Ping-pong logic: Buy every 10 minutes (0, 10, 20, etc.), Sell every 5 minutes offset (5, 15, 25, etc.)
                if current_minute % 10 == 0 and self._test_state['last_action'] != 'buy':
                    # Buy signal
                    action = self._create_test_buy_action(analysis_data, portfolio_data)
                    if action:
                        actions.append(action)
                        self._test_state['last_action'] = 'buy'
                        self._test_state['last_action_time'] = current_time
                        self._test_state['trade_count'] += 1
                        self.logger.info(f"Test strategy: BUY signal generated (trade #{self._test_state['trade_count']})")
                
                elif current_minute % 10 == 5 and self._test_state['last_action'] != 'sell':
                    # Sell signal
                    action = self._create_test_sell_action(analysis_data, portfolio_data)
                    if action:
                        actions.append(action)
                        self._test_state['last_action'] = 'sell'
                        self._test_state['last_action_time'] = current_time
                        self._test_state['trade_count'] += 1
                        self.logger.info(f"Test strategy: SELL signal generated (trade #{self._test_state['trade_count']})")
            
            return {
                'actions': actions,
                'test_mode': True,
                'trade_count': self._test_state['trade_count'],
                'last_action': self._test_state['last_action'],
                'timestamp': current_time
            }
            
        except Exception as e:
            self.logger.error(f"Error in test decision making: {e}")
            return {'actions': [], 'error': str(e), 'test_mode': True}
    
    def _create_test_buy_action(self, analysis_data: Dict[str, Any], 
                              portfolio_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a test buy action."""
        try:
            symbol = self._test_state['target_symbol']
            
            # Get current price from analysis data
            market_data = analysis_data.get('market_data', {})
            current_price = market_data.get(symbol, {}).get('price', 50000)  # Default fallback
            
            # Calculate small test quantity (minimum order size + small buffer)
            test_quantity = 0.001  # Very small BTC amount for testing
            
            return {
                'type': 'trade',
                'symbol': symbol,
                'side': 'buy',
                'quantity': test_quantity,
                'price_limit': current_price * 1.001,  # Slightly above market for quick fill
                'urgency': 'medium',
                'max_slippage': 0.01,
                'time_horizon_minutes': 30,
                'strategy': 'smart',
                'split_orders': False,
                'metadata': {
                    'test_mode': True,
                    'strategy': 'ping_pong',
                    'confidence': 0.8,
                    'reasoning': ['Test buy signal - ping-pong strategy'],
                    'trade_number': self._test_state['trade_count'] + 1
                }
            }
        except Exception as e:
            self.logger.error(f"Error creating test buy action: {e}")
            return None
    
    def _create_test_sell_action(self, analysis_data: Dict[str, Any], 
                               portfolio_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Create a test sell action."""
        try:
            symbol = self._test_state['target_symbol']
            
            # Check if we have any position to sell
            positions = portfolio_data.get('positions', {})
            base_asset = symbol.replace('USDT', '').replace('USD', '')
            current_position = positions.get(base_asset, 0)
            
            if current_position <= 0.0001:  # No significant position to sell
                self.logger.info("No position to sell in test mode")
                return None
            
            # Get current price from analysis data
            market_data = analysis_data.get('market_data', {})
            current_price = market_data.get(symbol, {}).get('price', 50000)  # Default fallback
            
            # Sell a small portion or minimum amount
            test_quantity = min(0.001, current_position * 0.5)  # Sell half or 0.001 BTC, whichever is smaller
            
            return {
                'type': 'trade',
                'symbol': symbol,
                'side': 'sell',
                'quantity': test_quantity,
                'price_limit': current_price * 0.999,  # Slightly below market for quick fill
                'urgency': 'medium',
                'max_slippage': 0.01,
                'time_horizon_minutes': 30,
                'strategy': 'smart',
                'split_orders': False,
                'metadata': {
                    'test_mode': True,
                    'strategy': 'ping_pong',
                    'confidence': 0.8,
                    'reasoning': ['Test sell signal - ping-pong strategy'],
                    'trade_number': self._test_state['trade_count'] + 1
                }
            }
        except Exception as e:
            self.logger.error(f"Error creating test sell action: {e}")
            return None
    
    def _calculate_risk_and_return(self, symbol: str, decision_type: DecisionType,
                                 analysis_data: Dict[str, Any], 
                                 position_size: float) -> Tuple[float, float]:
        """Calculate risk score and expected return for the decision.
        
        Args:
            symbol: Trading symbol
            decision_type: Type of trading decision
            analysis_data: Analysis data containing predictions, technical analysis, etc.
            position_size: Calculated position size
            
        Returns:
            Tuple of (risk_score, expected_return)
        """
        try:
            # Input validation
            if not symbol or not analysis_data or position_size <= 0:
                return 0.5, 0.0
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(symbol, decision_type, analysis_data, position_size)
            
            # Calculate expected return
            expected_return = self._calculate_expected_return(symbol, decision_type, analysis_data)
            
            # Validate results
            risk_score = max(0.0, min(1.0, risk_score))
            expected_return = max(-1.0, min(5.0, expected_return))  # Cap at 500% return
            
            return risk_score, expected_return
            
        except Exception as e:
            self.logger.error(f"Error calculating risk and return for {symbol}: {e}")
            return 0.5, 0.0