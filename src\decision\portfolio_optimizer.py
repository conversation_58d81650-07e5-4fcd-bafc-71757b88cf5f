#!/usr/bin/env python3
"""
Portfolio Optimizer - Advanced portfolio optimization for AI trading agent

Author: inkbytefo
Description: Optimizes portfolio allocation using modern portfolio theory and AI-driven insights
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum
from scipy.optimize import minimize
from sklearn.covariance import LedoitWolf


class OptimizationObjective(Enum):
    """Portfolio optimization objectives."""
    MAX_SHARPE = "max_sharpe"
    MIN_VARIANCE = "min_variance"
    MAX_RETURN = "max_return"
    RISK_PARITY = "risk_parity"
    BLACK_LITTERMAN = "black_litterman"
    MEAN_REVERSION = "mean_reversion"
    MOMENTUM = "momentum"
    BALANCED = "balanced"


class RebalanceFrequency(Enum):
    """Portfolio rebalancing frequencies."""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    QUARTERLY = "quarterly"
    DYNAMIC = "dynamic"


@dataclass
class OptimizationConstraints:
    """Portfolio optimization constraints."""
    min_weight: float = 0.0
    max_weight: float = 1.0
    max_positions: int = 20
    min_positions: int = 3
    max_turnover: float = 0.5
    target_volatility: Optional[float] = None
    target_return: Optional[float] = None
    sector_limits: Dict[str, float] = None
    liquidity_threshold: float = 0.7
    correlation_limit: float = 0.8


@dataclass
class AssetAllocation:
    """Asset allocation recommendation."""
    symbol: str
    current_weight: float
    target_weight: float
    weight_change: float
    current_value: float
    target_value: float
    trade_amount: float
    trade_direction: str  # 'buy', 'sell', 'hold'
    confidence: float
    reasoning: List[str]
    risk_contribution: float
    expected_return: float
    timestamp: datetime


@dataclass
class PortfolioOptimization:
    """Portfolio optimization result."""
    objective: OptimizationObjective
    allocations: List[AssetAllocation]
    expected_return: float
    expected_volatility: float
    sharpe_ratio: float
    total_turnover: float
    optimization_score: float
    rebalance_needed: bool
    constraints_satisfied: bool
    optimization_time: float
    recommendations: List[str]
    risk_metrics: Dict[str, float]
    performance_attribution: Dict[str, float]
    timestamp: datetime


class PortfolioOptimizer:
    """Advanced portfolio optimization system for AI trading agent."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Optimization parameters
        self.optimization_params = {
            'lookback_period': config.get('lookback_period', 252),  # 1 year
            'rebalance_threshold': config.get('rebalance_threshold', 0.05),  # 5%
            'transaction_cost': config.get('transaction_cost', 0.001),  # 0.1%
            'risk_free_rate': config.get('risk_free_rate', 0.02),  # 2%
            'confidence_level': config.get('confidence_level', 0.95),  # 95%
            'max_iterations': config.get('max_iterations', 1000),
            'convergence_tolerance': config.get('convergence_tolerance', 1e-6)
        }
        
        # Default constraints
        self.default_constraints = OptimizationConstraints(
            min_weight=config.get('min_weight', 0.01),  # 1% minimum
            max_weight=config.get('max_weight', 0.25),  # 25% maximum
            max_positions=config.get('max_positions', 15),
            min_positions=config.get('min_positions', 5),
            max_turnover=config.get('max_turnover', 0.3),  # 30% max turnover
            liquidity_threshold=config.get('liquidity_threshold', 0.7),
            correlation_limit=config.get('correlation_limit', 0.8)
        )
        
        # Market data for optimization
        self.price_history = {}
        self.return_history = {}
        self.covariance_matrix = None
        self.expected_returns = {}
        
        # Optimization history
        self.optimization_history = []
        self.performance_tracking = []
        self.max_history_size = 500
        
        # Current state
        self.current_optimization = None
        self.last_rebalance = None
        self.rebalance_frequency = RebalanceFrequency.WEEKLY
        
        # Performance metrics
        self.performance_metrics = {
            'total_optimizations': 0,
            'successful_optimizations': 0,
            'average_optimization_time': 0,
            'average_sharpe_improvement': 0,
            'total_rebalances': 0,
            'last_update': None
        }
        
        # Asset universe
        self.asset_universe = config.get('asset_universe', [
            'BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT',
            'SOL/USDT', 'AVAX/USDT', 'MATIC/USDT', 'ATOM/USDT', 'NEAR/USDT'
        ])
        
        # Sector classifications
        self.sector_mapping = {
            'BTC/USDT': 'Store of Value',
            'ETH/USDT': 'Smart Contracts',
            'ADA/USDT': 'Smart Contracts',
            'DOT/USDT': 'Interoperability',
            'LINK/USDT': 'Oracle',
            'SOL/USDT': 'Smart Contracts',
            'AVAX/USDT': 'Smart Contracts',
            'MATIC/USDT': 'Scaling',
            'ATOM/USDT': 'Interoperability',
            'NEAR/USDT': 'Smart Contracts'
        }
    
    async def start(self):
        """Start the portfolio optimizer."""
        self.logger.info("Starting Portfolio Optimizer...")
        
        # Initialize optimization engine
        await self._initialize_optimization_engine()
        
        self.logger.info("Portfolio Optimizer started")
    
    async def stop(self):
        """Stop the portfolio optimizer."""
        self.logger.info("Stopping Portfolio Optimizer...")
        
        # Save optimization data
        await self._save_optimization_data()
        
        self.logger.info("Portfolio Optimizer stopped")
    
    async def optimize_portfolio(self, portfolio_data: Dict[str, Any],
                               market_data: Dict[str, Any],
                               ai_insights: Dict[str, Any],
                               objective: OptimizationObjective = OptimizationObjective.BALANCED,
                               constraints: Optional[OptimizationConstraints] = None) -> PortfolioOptimization:
        """Optimize portfolio allocation based on multiple factors."""
        optimization_start = datetime.now()
        
        try:
            # Use default constraints if none provided
            if constraints is None:
                constraints = self.default_constraints
            
            # Update market data
            await self._update_market_data(market_data)
            
            # Get current portfolio state
            current_positions = portfolio_data.get('positions', {})
            total_value = portfolio_data.get('total_value', 0)
            
            if total_value <= 0:
                return self._create_empty_optimization(objective)
            
            # Calculate current weights
            current_weights = await self._calculate_current_weights(current_positions, total_value, market_data)
            
            # Get expected returns from AI insights
            expected_returns = await self._get_expected_returns(ai_insights, market_data)
            
            # Calculate covariance matrix
            covariance_matrix = await self._calculate_covariance_matrix(list(current_weights.keys()))
            
            # Filter asset universe based on constraints
            eligible_assets = await self._filter_eligible_assets(constraints, market_data)
            
            # Perform optimization based on objective
            optimal_weights = await self._perform_optimization(
                objective, expected_returns, covariance_matrix, constraints, eligible_assets
            )
            
            # Calculate allocation changes
            allocations = await self._calculate_allocations(
                current_weights, optimal_weights, total_value, market_data, ai_insights
            )
            
            # Calculate portfolio metrics
            portfolio_metrics = await self._calculate_portfolio_metrics(
                optimal_weights, expected_returns, covariance_matrix
            )
            
            # Check if rebalancing is needed
            rebalance_needed = await self._check_rebalance_needed(current_weights, optimal_weights, constraints)
            
            # Validate constraints
            constraints_satisfied = await self._validate_constraints(optimal_weights, constraints)
            
            # Calculate optimization score
            optimization_score = await self._calculate_optimization_score(
                portfolio_metrics, constraints_satisfied, objective
            )
            
            # Generate recommendations
            recommendations = await self._generate_optimization_recommendations(
                allocations, portfolio_metrics, rebalance_needed, constraints_satisfied
            )
            
            # Calculate performance attribution
            performance_attribution = await self._calculate_performance_attribution(
                allocations, ai_insights
            )
            
            # Create optimization result
            optimization_time = (datetime.now() - optimization_start).total_seconds()
            
            optimization_result = PortfolioOptimization(
                objective=objective,
                allocations=allocations,
                expected_return=portfolio_metrics['expected_return'],
                expected_volatility=portfolio_metrics['volatility'],
                sharpe_ratio=portfolio_metrics['sharpe_ratio'],
                total_turnover=portfolio_metrics['turnover'],
                optimization_score=optimization_score,
                rebalance_needed=rebalance_needed,
                constraints_satisfied=constraints_satisfied,
                optimization_time=optimization_time,
                recommendations=recommendations,
                risk_metrics=portfolio_metrics['risk_metrics'],
                performance_attribution=performance_attribution,
                timestamp=datetime.now()
            )
            
            # Update current state
            self.current_optimization = optimization_result
            
            # Update history
            self._update_optimization_history(optimization_result)
            
            # Update performance metrics
            await self._update_performance_metrics(optimization_result)
            
            self.logger.info(f"Portfolio optimization completed in {optimization_time:.2f}s")
            self.logger.info(f"Expected return: {portfolio_metrics['expected_return']:.2%}, Volatility: {portfolio_metrics['volatility']:.2%}")
            self.logger.info(f"Sharpe ratio: {portfolio_metrics['sharpe_ratio']:.3f}, Rebalance needed: {rebalance_needed}")
            
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"Error in portfolio optimization: {e}")
            return self._create_empty_optimization(objective)
    
    async def suggest_rebalancing(self, portfolio_data: Dict[str, Any],
                                market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Suggest portfolio rebalancing actions."""
        try:
            if not self.current_optimization:
                return {'rebalance_needed': False, 'reason': 'No optimization available'}
            
            # Check time since last rebalance
            time_threshold = await self._get_rebalance_time_threshold()
            
            if (self.last_rebalance and 
                datetime.now() - self.last_rebalance < time_threshold):
                return {'rebalance_needed': False, 'reason': 'Too soon since last rebalance'}
            
            # Calculate current drift from target
            current_weights = await self._calculate_current_weights(
                portfolio_data.get('positions', {}),
                portfolio_data.get('total_value', 0),
                market_data
            )
            
            target_weights = {alloc.symbol: alloc.target_weight 
                            for alloc in self.current_optimization.allocations}
            
            # Calculate drift
            max_drift = 0
            drift_details = {}
            
            for symbol in set(list(current_weights.keys()) + list(target_weights.keys())):
                current_weight = current_weights.get(symbol, 0)
                target_weight = target_weights.get(symbol, 0)
                drift = abs(current_weight - target_weight)
                
                drift_details[symbol] = {
                    'current_weight': current_weight,
                    'target_weight': target_weight,
                    'drift': drift
                }
                
                max_drift = max(max_drift, drift)
            
            # Check if rebalancing is needed
            rebalance_threshold = self.optimization_params['rebalance_threshold']
            rebalance_needed = max_drift > rebalance_threshold
            
            # Generate rebalancing actions
            rebalancing_actions = []
            if rebalance_needed:
                for alloc in self.current_optimization.allocations:
                    if abs(alloc.weight_change) > 0.01:  # 1% threshold
                        rebalancing_actions.append({
                            'symbol': alloc.symbol,
                            'action': alloc.trade_direction,
                            'amount': alloc.trade_amount,
                            'current_weight': alloc.current_weight,
                            'target_weight': alloc.target_weight,
                            'priority': 'high' if abs(alloc.weight_change) > 0.05 else 'medium'
                        })
            
            return {
                'rebalance_needed': rebalance_needed,
                'max_drift': max_drift,
                'drift_threshold': rebalance_threshold,
                'drift_details': drift_details,
                'rebalancing_actions': rebalancing_actions,
                'estimated_cost': await self._estimate_rebalancing_cost(rebalancing_actions),
                'expected_benefit': await self._estimate_rebalancing_benefit(drift_details),
                'recommendation': 'Rebalance recommended' if rebalance_needed else 'No rebalancing needed'
            }
            
        except Exception as e:
            self.logger.error(f"Error in rebalancing suggestion: {e}")
            return {'rebalance_needed': False, 'reason': f'Error: {str(e)}'}
    
    async def _calculate_current_weights(self, positions: Dict[str, float],
                                       total_value: float,
                                       market_data: Dict[str, Any]) -> Dict[str, float]:
        """Calculate current portfolio weights."""
        weights = {}
        
        try:
            if total_value <= 0:
                return weights
            
            for symbol, position_size in positions.items():
                if position_size != 0:
                    # Get current price
                    price = await self._get_current_price(symbol, market_data)
                    if price:
                        market_value = abs(position_size * price)
                        weight = market_value / total_value
                        weights[symbol] = weight
            
        except Exception as e:
            self.logger.warning(f"Error calculating current weights: {e}")
        
        return weights
    
    async def _get_expected_returns(self, ai_insights: Dict[str, Any],
                                  market_data: Dict[str, Any]) -> Dict[str, float]:
        """Get expected returns from AI insights and market data."""
        expected_returns = {}
        
        try:
            # Get AI predictions
            predictions = ai_insights.get('predictions', {})
            
            # Get sentiment scores
            sentiment_scores = ai_insights.get('sentiment', {})
            
            # Get technical signals
            technical_signals = ai_insights.get('technical_signals', {})
            
            for symbol in self.asset_universe:
                # Base expected return from historical data
                historical_return = await self._get_historical_return(symbol, 252)  # 1 year
                
                # AI prediction adjustment
                prediction_adjustment = 0
                if symbol in predictions:
                    prediction = predictions[symbol]
                    if isinstance(prediction, dict):
                        prediction_return = prediction.get('expected_return', 0)
                        confidence = prediction.get('confidence', 0.5)
                        prediction_adjustment = prediction_return * confidence
                
                # Sentiment adjustment
                sentiment_adjustment = 0
                if symbol in sentiment_scores:
                    sentiment = sentiment_scores[symbol]
                    if isinstance(sentiment, dict):
                        sentiment_score = sentiment.get('score', 0)
                        sentiment_adjustment = sentiment_score * 0.02  # 2% max adjustment
                
                # Technical signal adjustment
                technical_adjustment = 0
                if symbol in technical_signals:
                    signals = technical_signals[symbol]
                    if isinstance(signals, dict):
                        signal_strength = signals.get('strength', 0)
                        signal_direction = signals.get('direction', 0)
                        technical_adjustment = signal_strength * signal_direction * 0.01
                
                # Combine adjustments
                total_expected_return = (historical_return + 
                                       prediction_adjustment + 
                                       sentiment_adjustment + 
                                       technical_adjustment)
                
                expected_returns[symbol] = total_expected_return
            
        except Exception as e:
            self.logger.warning(f"Error calculating expected returns: {e}")
            # Fallback to default returns
            for symbol in self.asset_universe:
                expected_returns[symbol] = 0.1  # 10% default expected return
        
        return expected_returns
    
    async def _calculate_covariance_matrix(self, symbols: List[str]) -> np.ndarray:
        """Calculate covariance matrix for given symbols."""
        try:
            # Get return data for all symbols
            return_data = []
            
            for symbol in symbols:
                returns = await self._get_return_series(symbol, self.optimization_params['lookback_period'])
                if returns:
                    return_data.append(returns)
                else:
                    # Generate synthetic returns if no data available
                    synthetic_returns = np.random.normal(0.001, 0.02, self.optimization_params['lookback_period'])
                    return_data.append(synthetic_returns)
            
            if not return_data:
                # Return identity matrix if no data
                return np.eye(len(symbols)) * 0.01
            
            # Convert to numpy array
            returns_matrix = np.array(return_data).T
            
            # Use Ledoit-Wolf shrinkage estimator for better covariance estimation
            lw = LedoitWolf()
            covariance_matrix = lw.fit(returns_matrix).covariance_
            
            # Annualize the covariance matrix
            covariance_matrix *= 252
            
            return covariance_matrix
            
        except Exception as e:
            self.logger.warning(f"Error calculating covariance matrix: {e}")
            # Return default covariance matrix
            n = len(symbols)
            default_variance = 0.04  # 20% annual volatility
            default_correlation = 0.3
            
            cov_matrix = np.full((n, n), default_variance * default_correlation)
            np.fill_diagonal(cov_matrix, default_variance)
            
            return cov_matrix
    
    async def _filter_eligible_assets(self, constraints: OptimizationConstraints,
                                    market_data: Dict[str, Any]) -> List[str]:
        """Filter assets based on constraints and market conditions."""
        eligible_assets = []
        
        try:
            for symbol in self.asset_universe:
                # Check liquidity
                liquidity_score = await self._get_liquidity_score(symbol, market_data)
                if liquidity_score < constraints.liquidity_threshold:
                    continue
                
                # Check if we have sufficient data
                if not await self._has_sufficient_data(symbol):
                    continue
                
                # Check market cap or volume requirements
                volume = market_data.get('volumes', {}).get(symbol, 0)
                if volume < 100000:  # Minimum $100k daily volume
                    continue
                
                eligible_assets.append(symbol)
            
            # Ensure we have minimum number of assets
            if len(eligible_assets) < constraints.min_positions:
                # Add top assets by volume if needed
                volume_sorted = sorted(self.asset_universe, 
                                     key=lambda x: market_data.get('volumes', {}).get(x, 0), 
                                     reverse=True)
                
                for symbol in volume_sorted:
                    if symbol not in eligible_assets:
                        eligible_assets.append(symbol)
                        if len(eligible_assets) >= constraints.min_positions:
                            break
            
            # Limit to maximum positions
            if len(eligible_assets) > constraints.max_positions:
                # Keep top assets by some criteria (volume, market cap, etc.)
                eligible_assets = eligible_assets[:constraints.max_positions]
            
        except Exception as e:
            self.logger.warning(f"Error filtering eligible assets: {e}")
            # Fallback to top assets
            eligible_assets = self.asset_universe[:constraints.max_positions]
        
        return eligible_assets
    
    async def _perform_optimization(self, objective: OptimizationObjective,
                                  expected_returns: Dict[str, float],
                                  covariance_matrix: np.ndarray,
                                  constraints: OptimizationConstraints,
                                  eligible_assets: List[str]) -> Dict[str, float]:
        """Perform portfolio optimization based on objective."""
        try:
            n_assets = len(eligible_assets)
            
            if n_assets == 0:
                return {}
            
            # Convert expected returns to array
            returns_array = np.array([expected_returns.get(symbol, 0.1) for symbol in eligible_assets])
            
            # Initial guess (equal weights)
            x0 = np.ones(n_assets) / n_assets
            
            # Bounds for weights
            bounds = [(constraints.min_weight, constraints.max_weight) for _ in range(n_assets)]
            
            # Constraints
            constraint_list = []
            
            # Weights sum to 1
            constraint_list.append({
                'type': 'eq',
                'fun': lambda x: np.sum(x) - 1.0
            })
            
            # Sector constraints if specified
            if constraints.sector_limits:
                for sector, limit in constraints.sector_limits.items():
                    sector_indices = [i for i, symbol in enumerate(eligible_assets) 
                                    if self.sector_mapping.get(symbol) == sector]
                    if sector_indices:
                        constraint_list.append({
                            'type': 'ineq',
                            'fun': lambda x, indices=sector_indices: limit - np.sum(x[indices])
                        })
            
            # Target volatility constraint
            if constraints.target_volatility:
                constraint_list.append({
                    'type': 'eq',
                    'fun': lambda x: np.sqrt(np.dot(x, np.dot(covariance_matrix, x))) - constraints.target_volatility
                })
            
            # Target return constraint
            if constraints.target_return:
                constraint_list.append({
                    'type': 'eq',
                    'fun': lambda x: np.dot(x, returns_array) - constraints.target_return
                })
            
            # Define objective function based on optimization objective
            if objective == OptimizationObjective.MAX_SHARPE:
                def objective_func(x):
                    portfolio_return = np.dot(x, returns_array)
                    portfolio_variance = np.dot(x, np.dot(covariance_matrix, x))
                    portfolio_volatility = np.sqrt(portfolio_variance)
                    
                    if portfolio_volatility == 0:
                        return -np.inf
                    
                    sharpe_ratio = (portfolio_return - self.optimization_params['risk_free_rate']) / portfolio_volatility
                    return -sharpe_ratio  # Minimize negative Sharpe ratio
            
            elif objective == OptimizationObjective.MIN_VARIANCE:
                def objective_func(x):
                    return np.dot(x, np.dot(covariance_matrix, x))
            
            elif objective == OptimizationObjective.MAX_RETURN:
                def objective_func(x):
                    return -np.dot(x, returns_array)  # Minimize negative return
            
            elif objective == OptimizationObjective.RISK_PARITY:
                def objective_func(x):
                    portfolio_variance = np.dot(x, np.dot(covariance_matrix, x))
                    marginal_contrib = np.dot(covariance_matrix, x)
                    contrib = x * marginal_contrib / portfolio_variance
                    
                    # Minimize sum of squared deviations from equal risk contribution
                    target_contrib = 1.0 / n_assets
                    return np.sum((contrib - target_contrib) ** 2)
            
            else:  # BALANCED or other objectives
                def objective_func(x):
                    portfolio_return = np.dot(x, returns_array)
                    portfolio_variance = np.dot(x, np.dot(covariance_matrix, x))
                    
                    # Balanced objective: maximize return-to-risk ratio with penalty for concentration
                    concentration_penalty = np.sum(x ** 2)  # Herfindahl index
                    
                    return -(portfolio_return / np.sqrt(portfolio_variance)) + 0.1 * concentration_penalty
            
            # Perform optimization
            result = minimize(
                objective_func,
                x0,
                method='SLSQP',
                bounds=bounds,
                constraints=constraint_list,
                options={
                    'maxiter': self.optimization_params['max_iterations'],
                    'ftol': self.optimization_params['convergence_tolerance']
                }
            )
            
            if result.success:
                optimal_weights = result.x
                
                # Normalize weights to ensure they sum to 1
                optimal_weights = optimal_weights / np.sum(optimal_weights)
                
                # Convert to dictionary
                weight_dict = {}
                for i, symbol in enumerate(eligible_assets):
                    if optimal_weights[i] > constraints.min_weight:
                        weight_dict[symbol] = optimal_weights[i]
                
                return weight_dict
            
            else:
                self.logger.warning(f"Optimization failed: {result.message}")
                # Fallback to equal weights
                equal_weight = 1.0 / n_assets
                return {symbol: equal_weight for symbol in eligible_assets}
            
        except Exception as e:
            self.logger.error(f"Error in portfolio optimization: {e}")
            # Fallback to equal weights
            n_assets = len(eligible_assets)
            if n_assets > 0:
                equal_weight = 1.0 / n_assets
                return {symbol: equal_weight for symbol in eligible_assets}
            return {}
    
    async def _calculate_allocations(self, current_weights: Dict[str, float],
                                   optimal_weights: Dict[str, float],
                                   total_value: float,
                                   market_data: Dict[str, Any],
                                   ai_insights: Dict[str, Any]) -> List[AssetAllocation]:
        """Calculate allocation changes needed."""
        allocations = []
        
        try:
            # Get all symbols
            all_symbols = set(list(current_weights.keys()) + list(optimal_weights.keys()))
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = optimal_weights.get(symbol, 0)
                weight_change = target_weight - current_weight
                
                # Calculate values
                current_value = current_weight * total_value
                target_value = target_weight * total_value
                trade_amount = target_value - current_value
                
                # Determine trade direction
                if abs(trade_amount) < total_value * 0.01:  # Less than 1% of portfolio
                    trade_direction = 'hold'
                elif trade_amount > 0:
                    trade_direction = 'buy'
                else:
                    trade_direction = 'sell'
                
                # Calculate confidence based on AI insights
                confidence = await self._calculate_allocation_confidence(symbol, ai_insights)
                
                # Generate reasoning
                reasoning = await self._generate_allocation_reasoning(
                    symbol, weight_change, ai_insights, market_data
                )
                
                # Calculate risk contribution
                risk_contribution = await self._calculate_risk_contribution(symbol, target_weight)
                
                # Get expected return
                expected_return = ai_insights.get('predictions', {}).get(symbol, {}).get('expected_return', 0.1)
                
                allocation = AssetAllocation(
                    symbol=symbol,
                    current_weight=current_weight,
                    target_weight=target_weight,
                    weight_change=weight_change,
                    current_value=current_value,
                    target_value=target_value,
                    trade_amount=trade_amount,
                    trade_direction=trade_direction,
                    confidence=confidence,
                    reasoning=reasoning,
                    risk_contribution=risk_contribution,
                    expected_return=expected_return,
                    timestamp=datetime.now()
                )
                
                allocations.append(allocation)
            
            # Sort by absolute weight change (largest changes first)
            allocations.sort(key=lambda x: abs(x.weight_change), reverse=True)
            
        except Exception as e:
            self.logger.warning(f"Error calculating allocations: {e}")
        
        return allocations
    
    async def _calculate_portfolio_metrics(self, optimal_weights: Dict[str, float],
                                         expected_returns: Dict[str, float],
                                         covariance_matrix: np.ndarray) -> Dict[str, Any]:
        """Calculate portfolio performance metrics."""
        metrics = {}
        
        try:
            if not optimal_weights:
                return {
                    'expected_return': 0,
                    'volatility': 0,
                    'sharpe_ratio': 0,
                    'turnover': 0,
                    'risk_metrics': {}
                }
            
            symbols = list(optimal_weights.keys())
            weights = np.array([optimal_weights[symbol] for symbol in symbols])
            returns = np.array([expected_returns.get(symbol, 0.1) for symbol in symbols])
            
            # Portfolio expected return
            portfolio_return = np.dot(weights, returns)
            
            # Portfolio volatility
            portfolio_variance = np.dot(weights, np.dot(covariance_matrix, weights))
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Sharpe ratio
            risk_free_rate = self.optimization_params['risk_free_rate']
            sharpe_ratio = (portfolio_return - risk_free_rate) / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Calculate turnover (if we have previous weights)
            turnover = 0
            if self.current_optimization:
                prev_weights = {alloc.symbol: alloc.target_weight 
                              for alloc in self.current_optimization.allocations}
                
                for symbol in set(list(optimal_weights.keys()) + list(prev_weights.keys())):
                    current_weight = optimal_weights.get(symbol, 0)
                    previous_weight = prev_weights.get(symbol, 0)
                    turnover += abs(current_weight - previous_weight)
                
                turnover /= 2  # Divide by 2 to get one-way turnover
            
            # Risk metrics
            risk_metrics = {
                'var_95': portfolio_volatility * 1.96 * np.sqrt(1/252),  # Daily 95% VaR
                'var_99': portfolio_volatility * 2.33 * np.sqrt(1/252),  # Daily 99% VaR
                'expected_shortfall': portfolio_volatility * 2.33 * np.sqrt(1/252) * 1.3,
                'max_drawdown_estimate': portfolio_volatility * 2.0,  # Rough estimate
                'concentration_ratio': np.sum(weights ** 2),  # Herfindahl index
                'effective_positions': 1 / np.sum(weights ** 2)  # Effective number of positions
            }
            
            metrics = {
                'expected_return': portfolio_return,
                'volatility': portfolio_volatility,
                'sharpe_ratio': sharpe_ratio,
                'turnover': turnover,
                'risk_metrics': risk_metrics
            }
            
        except Exception as e:
            self.logger.warning(f"Error calculating portfolio metrics: {e}")
            metrics = {
                'expected_return': 0,
                'volatility': 0,
                'sharpe_ratio': 0,
                'turnover': 0,
                'risk_metrics': {}
            }
        
        return metrics
    
    async def _check_rebalance_needed(self, current_weights: Dict[str, float],
                                    optimal_weights: Dict[str, float],
                                    constraints: OptimizationConstraints) -> bool:
        """Check if portfolio rebalancing is needed."""
        try:
            max_drift = 0
            
            all_symbols = set(list(current_weights.keys()) + list(optimal_weights.keys()))
            
            for symbol in all_symbols:
                current_weight = current_weights.get(symbol, 0)
                target_weight = optimal_weights.get(symbol, 0)
                drift = abs(current_weight - target_weight)
                max_drift = max(max_drift, drift)
            
            return max_drift > self.optimization_params['rebalance_threshold']
            
        except Exception as e:
            self.logger.warning(f"Error checking rebalance need: {e}")
            return False
    
    async def _validate_constraints(self, optimal_weights: Dict[str, float],
                                  constraints: OptimizationConstraints) -> bool:
        """Validate that optimal weights satisfy constraints."""
        try:
            # Check weight bounds
            for weight in optimal_weights.values():
                if weight < constraints.min_weight or weight > constraints.max_weight:
                    return False
            
            # Check number of positions
            num_positions = len([w for w in optimal_weights.values() if w > constraints.min_weight])
            if num_positions < constraints.min_positions or num_positions > constraints.max_positions:
                return False
            
            # Check sector limits
            if constraints.sector_limits:
                sector_weights = {}
                for symbol, weight in optimal_weights.items():
                    sector = self.sector_mapping.get(symbol, 'Other')
                    sector_weights[sector] = sector_weights.get(sector, 0) + weight
                
                for sector, limit in constraints.sector_limits.items():
                    if sector_weights.get(sector, 0) > limit:
                        return False
            
            # Check weights sum to approximately 1
            total_weight = sum(optimal_weights.values())
            if abs(total_weight - 1.0) > 0.01:  # 1% tolerance
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating constraints: {e}")
            return False
    
    async def _calculate_optimization_score(self, portfolio_metrics: Dict[str, Any],
                                          constraints_satisfied: bool,
                                          objective: OptimizationObjective) -> float:
        """Calculate optimization quality score."""
        try:
            score = 0
            
            # Base score from Sharpe ratio
            sharpe_ratio = portfolio_metrics.get('sharpe_ratio', 0)
            score += min(sharpe_ratio * 20, 50)  # Max 50 points from Sharpe
            
            # Penalty for high turnover
            turnover = portfolio_metrics.get('turnover', 0)
            turnover_penalty = min(turnover * 50, 20)  # Max 20 point penalty
            score -= turnover_penalty
            
            # Bonus for diversification
            risk_metrics = portfolio_metrics.get('risk_metrics', {})
            effective_positions = risk_metrics.get('effective_positions', 1)
            diversification_bonus = min(effective_positions * 2, 20)  # Max 20 points
            score += diversification_bonus
            
            # Penalty for constraint violations
            if not constraints_satisfied:
                score -= 30
            
            # Objective-specific adjustments
            if objective == OptimizationObjective.MIN_VARIANCE:
                volatility = portfolio_metrics.get('volatility', 0)
                score += max(0, 20 - volatility * 100)  # Bonus for low volatility
            
            elif objective == OptimizationObjective.MAX_RETURN:
                expected_return = portfolio_metrics.get('expected_return', 0)
                score += expected_return * 100  # Bonus for high return
            
            # Normalize score to 0-100 range
            score = max(0, min(100, score))
            
            return score
            
        except Exception as e:
            self.logger.warning(f"Error calculating optimization score: {e}")
            return 50  # Default medium score
    
    async def _generate_optimization_recommendations(self, allocations: List[AssetAllocation],
                                                   portfolio_metrics: Dict[str, Any],
                                                   rebalance_needed: bool,
                                                   constraints_satisfied: bool) -> List[str]:
        """Generate optimization recommendations."""
        recommendations = []
        
        try:
            # Rebalancing recommendation
            if rebalance_needed:
                recommendations.append("Portfolio rebalancing is recommended")
                
                # Identify major changes
                major_changes = [alloc for alloc in allocations if abs(alloc.weight_change) > 0.05]
                if major_changes:
                    symbols = [alloc.symbol for alloc in major_changes]
                    recommendations.append(f"Major allocation changes needed for: {', '.join(symbols)}")
            else:
                recommendations.append("Portfolio is well-balanced, no immediate rebalancing needed")
            
            # Risk recommendations
            risk_metrics = portfolio_metrics.get('risk_metrics', {})
            concentration_ratio = risk_metrics.get('concentration_ratio', 0)
            
            if concentration_ratio > 0.5:  # High concentration
                recommendations.append("Consider diversifying to reduce concentration risk")
            
            effective_positions = risk_metrics.get('effective_positions', 0)
            if effective_positions < 5:
                recommendations.append("Consider adding more positions for better diversification")
            
            # Performance recommendations
            sharpe_ratio = portfolio_metrics.get('sharpe_ratio', 0)
            if sharpe_ratio < 0.5:
                recommendations.append("Portfolio risk-adjusted returns could be improved")
            
            turnover = portfolio_metrics.get('turnover', 0)
            if turnover > 0.5:
                recommendations.append("High turnover detected - consider reducing trading frequency")
            
            # Constraint recommendations
            if not constraints_satisfied:
                recommendations.append("Some portfolio constraints are not satisfied - review limits")
            
            # Asset-specific recommendations
            high_confidence_buys = [alloc for alloc in allocations 
                                  if alloc.trade_direction == 'buy' and alloc.confidence > 0.7]
            
            if high_confidence_buys:
                symbols = [alloc.symbol for alloc in high_confidence_buys]
                recommendations.append(f"High confidence buy opportunities: {', '.join(symbols)}")
            
            high_confidence_sells = [alloc for alloc in allocations 
                                   if alloc.trade_direction == 'sell' and alloc.confidence > 0.7]
            
            if high_confidence_sells:
                symbols = [alloc.symbol for alloc in high_confidence_sells]
                recommendations.append(f"High confidence sell recommendations: {', '.join(symbols)}")
            
            if not recommendations:
                recommendations.append("Portfolio optimization completed successfully")
            
        except Exception as e:
            self.logger.warning(f"Error generating recommendations: {e}")
            recommendations = ["Regular portfolio monitoring recommended"]
        
        return recommendations
    
    async def _calculate_performance_attribution(self, allocations: List[AssetAllocation],
                                               ai_insights: Dict[str, Any]) -> Dict[str, float]:
        """Calculate performance attribution by factor."""
        attribution = {
            'ai_predictions': 0,
            'sentiment_analysis': 0,
            'technical_signals': 0,
            'risk_management': 0,
            'diversification': 0
        }
        
        try:
            total_weight = sum(alloc.target_weight for alloc in allocations)
            
            if total_weight == 0:
                return attribution
            
            for alloc in allocations:
                weight = alloc.target_weight / total_weight
                expected_return = alloc.expected_return
                
                # AI prediction contribution
                predictions = ai_insights.get('predictions', {})
                if alloc.symbol in predictions:
                    prediction_confidence = predictions[alloc.symbol].get('confidence', 0.5)
                    attribution['ai_predictions'] += weight * expected_return * prediction_confidence
                
                # Sentiment contribution
                sentiment_scores = ai_insights.get('sentiment', {})
                if alloc.symbol in sentiment_scores:
                    sentiment_impact = sentiment_scores[alloc.symbol].get('score', 0)
                    attribution['sentiment_analysis'] += weight * expected_return * abs(sentiment_impact) * 0.5
                
                # Technical signal contribution
                technical_signals = ai_insights.get('technical_signals', {})
                if alloc.symbol in technical_signals:
                    signal_strength = technical_signals[alloc.symbol].get('strength', 0)
                    attribution['technical_signals'] += weight * expected_return * signal_strength * 0.3
                
                # Risk management contribution (inverse of risk)
                risk_contribution = alloc.risk_contribution
                attribution['risk_management'] += weight * expected_return * (1 - risk_contribution) * 0.2
                
                # Diversification benefit
                attribution['diversification'] += weight * expected_return * 0.1
            
        except Exception as e:
            self.logger.warning(f"Error calculating performance attribution: {e}")
        
        return attribution
    
    # Helper methods
    async def _get_current_price(self, symbol: str, market_data: Dict[str, Any]) -> Optional[float]:
        """Get current price for a symbol."""
        try:
            if 'prices' in market_data and symbol in market_data['prices']:
                return market_data['prices'][symbol]
            return 50.0  # Default price
        except Exception:
            return None
    
    async def _get_historical_return(self, symbol: str, days: int) -> float:
        """Get historical return for a symbol."""
        try:
            if symbol in self.return_history and len(self.return_history[symbol]) >= days:
                returns = self.return_history[symbol][-days:]
                return np.mean(returns) * 252  # Annualized
            
            # Default returns based on asset type
            if 'BTC' in symbol:
                return 0.15  # 15% for Bitcoin
            elif 'ETH' in symbol:
                return 0.20  # 20% for Ethereum
            else:
                return 0.25  # 25% for altcoins
        except Exception:
            return 0.15  # Default 15% return
    
    async def _get_return_series(self, symbol: str, days: int) -> Optional[np.ndarray]:
        """Get return series for a symbol."""
        try:
            if symbol in self.return_history and len(self.return_history[symbol]) >= days:
                return np.array(self.return_history[symbol][-days:])
            
            # Generate synthetic returns if no data
            if 'BTC' in symbol:
                return np.random.normal(0.0006, 0.04, days)  # Bitcoin-like returns
            elif 'ETH' in symbol:
                return np.random.normal(0.0008, 0.05, days)  # Ethereum-like returns
            else:
                return np.random.normal(0.001, 0.06, days)   # Altcoin-like returns
        except Exception:
            return None
    
    async def _get_liquidity_score(self, symbol: str, market_data: Dict[str, Any]) -> float:
        """Get liquidity score for a symbol."""
        try:
            volume = market_data.get('volumes', {}).get(symbol, 0)
            spread = market_data.get('spreads', {}).get(symbol, 0.01)
            
            # Simple liquidity score based on volume and spread
            volume_score = min(volume / 1000000, 1.0)  # Normalize to $1M
            spread_score = max(0, 1 - spread * 100)    # Lower spread = higher score
            
            return (volume_score * 0.7 + spread_score * 0.3)
        except Exception:
            return 0.7  # Default medium liquidity
    
    async def _has_sufficient_data(self, symbol: str) -> bool:
        """Check if we have sufficient data for a symbol."""
        try:
            min_data_points = 30  # Minimum 30 data points
            
            if symbol in self.price_history:
                return len(self.price_history[symbol]) >= min_data_points
            
            return True  # Assume we have data for now
        except Exception:
            return True
    
    async def _calculate_allocation_confidence(self, symbol: str, ai_insights: Dict[str, Any]) -> float:
        """Calculate confidence for an allocation."""
        try:
            confidence = 0.5  # Base confidence
            
            # AI prediction confidence
            predictions = ai_insights.get('predictions', {})
            if symbol in predictions:
                pred_confidence = predictions[symbol].get('confidence', 0.5)
                confidence = (confidence + pred_confidence) / 2
            
            # Sentiment confidence
            sentiment_scores = ai_insights.get('sentiment', {})
            if symbol in sentiment_scores:
                sentiment_confidence = sentiment_scores[symbol].get('confidence', 0.5)
                confidence = (confidence + sentiment_confidence) / 2
            
            # Technical signal confidence
            technical_signals = ai_insights.get('technical_signals', {})
            if symbol in technical_signals:
                signal_confidence = technical_signals[symbol].get('confidence', 0.5)
                confidence = (confidence + signal_confidence) / 2
            
            return max(0.1, min(0.9, confidence))
        except Exception:
            return 0.5
    
    async def _generate_allocation_reasoning(self, symbol: str, weight_change: float,
                                           ai_insights: Dict[str, Any],
                                           market_data: Dict[str, Any]) -> List[str]:
        """Generate reasoning for allocation changes."""
        reasoning = []
        
        try:
            if abs(weight_change) < 0.01:
                reasoning.append(f"{symbol}: Maintaining current allocation")
                return reasoning
            
            if weight_change > 0:
                reasoning.append(f"{symbol}: Increasing allocation by {weight_change:.1%}")
                
                # Add specific reasons for increase
                predictions = ai_insights.get('predictions', {})
                if symbol in predictions:
                    pred = predictions[symbol]
                    if pred.get('expected_return', 0) > 0.1:
                        reasoning.append("- AI model predicts strong returns")
                
                sentiment = ai_insights.get('sentiment', {})
                if symbol in sentiment:
                    sent_score = sentiment[symbol].get('score', 0)
                    if sent_score > 0.2:
                        reasoning.append("- Positive market sentiment")
                
                technical = ai_insights.get('technical_signals', {})
                if symbol in technical:
                    tech_signal = technical[symbol].get('direction', 0)
                    if tech_signal > 0.3:
                        reasoning.append("- Strong technical buy signals")
            
            else:
                reasoning.append(f"{symbol}: Decreasing allocation by {abs(weight_change):.1%}")
                
                # Add specific reasons for decrease
                predictions = ai_insights.get('predictions', {})
                if symbol in predictions:
                    pred = predictions[symbol]
                    if pred.get('expected_return', 0) < 0.05:
                        reasoning.append("- AI model predicts weak returns")
                
                sentiment = ai_insights.get('sentiment', {})
                if symbol in sentiment:
                    sent_score = sentiment[symbol].get('score', 0)
                    if sent_score < -0.2:
                        reasoning.append("- Negative market sentiment")
                
                technical = ai_insights.get('technical_signals', {})
                if symbol in technical:
                    tech_signal = technical[symbol].get('direction', 0)
                    if tech_signal < -0.3:
                        reasoning.append("- Technical sell signals detected")
            
            if not reasoning:
                reasoning.append(f"{symbol}: Allocation change for portfolio optimization")
            
        except Exception as e:
            self.logger.warning(f"Error generating allocation reasoning: {e}")
            reasoning = [f"{symbol}: Allocation adjusted for optimization"]
        
        return reasoning
    
    async def _calculate_risk_contribution(self, symbol: str, target_weight: float) -> float:
        """Calculate risk contribution of an asset."""
        try:
            # Simplified risk contribution calculation
            # In practice, this would use the full covariance matrix
            
            # Base risk from volatility
            volatility = await self._get_asset_volatility(symbol)
            
            # Risk contribution is weight * volatility * correlation with portfolio
            # For simplification, assume average correlation of 0.5
            risk_contribution = target_weight * volatility * 0.5
            
            return max(0, min(1, risk_contribution))
        except Exception:
            return target_weight  # Fallback to weight as risk contribution
    
    async def _get_asset_volatility(self, symbol: str) -> float:
        """Get asset volatility."""
        try:
            if symbol in self.volatility_history and self.volatility_history[symbol]:
                return self.volatility_history[symbol][-1]
            
            # Default volatilities
            if 'BTC' in symbol:
                return 0.04  # 4% daily volatility
            elif 'ETH' in symbol:
                return 0.05  # 5% daily volatility
            else:
                return 0.06  # 6% daily volatility for altcoins
        except Exception:
            return 0.05
    
    async def _get_rebalance_time_threshold(self) -> timedelta:
        """Get time threshold for rebalancing."""
        if self.rebalance_frequency == RebalanceFrequency.DAILY:
            return timedelta(days=1)
        elif self.rebalance_frequency == RebalanceFrequency.WEEKLY:
            return timedelta(weeks=1)
        elif self.rebalance_frequency == RebalanceFrequency.MONTHLY:
            return timedelta(days=30)
        elif self.rebalance_frequency == RebalanceFrequency.QUARTERLY:
            return timedelta(days=90)
        else:  # DYNAMIC
            return timedelta(hours=6)  # Check every 6 hours
    
    async def _estimate_rebalancing_cost(self, rebalancing_actions: List[Dict[str, Any]]) -> float:
        """Estimate cost of rebalancing."""
        try:
            total_cost = 0
            transaction_cost = self.optimization_params['transaction_cost']
            
            for action in rebalancing_actions:
                trade_amount = abs(action.get('amount', 0))
                total_cost += trade_amount * transaction_cost
            
            return total_cost
        except Exception:
            return 0
    
    async def _estimate_rebalancing_benefit(self, drift_details: Dict[str, Any]) -> float:
        """Estimate benefit of rebalancing."""
        try:
            # Simplified benefit calculation
            # In practice, this would consider expected returns and risk reduction
            
            total_drift = sum(details['drift'] for details in drift_details.values())
            
            # Assume benefit is proportional to drift reduction
            benefit = total_drift * 0.1  # 10% of drift as benefit
            
            return benefit
        except Exception:
            return 0
    
    def _update_optimization_history(self, optimization_result: PortfolioOptimization):
        """Update optimization history."""
        try:
            history_entry = {
                'timestamp': optimization_result.timestamp,
                'objective': optimization_result.objective.value,
                'expected_return': optimization_result.expected_return,
                'expected_volatility': optimization_result.expected_volatility,
                'sharpe_ratio': optimization_result.sharpe_ratio,
                'optimization_score': optimization_result.optimization_score,
                'rebalance_needed': optimization_result.rebalance_needed,
                'constraints_satisfied': optimization_result.constraints_satisfied,
                'optimization_time': optimization_result.optimization_time,
                'num_allocations': len(optimization_result.allocations)
            }
            
            self.optimization_history.append(history_entry)
            
            # Keep only recent history
            if len(self.optimization_history) > self.max_history_size:
                self.optimization_history = self.optimization_history[-self.max_history_size:]
            
        except Exception as e:
            self.logger.warning(f"Error updating optimization history: {e}")
    
    async def _update_performance_metrics(self, optimization_result: PortfolioOptimization):
        """Update performance metrics."""
        try:
            self.performance_metrics['total_optimizations'] += 1
            
            if optimization_result.constraints_satisfied:
                self.performance_metrics['successful_optimizations'] += 1
            
            # Update average optimization time
            current_avg = self.performance_metrics['average_optimization_time']
            total_opts = self.performance_metrics['total_optimizations']
            new_avg = ((current_avg * (total_opts - 1)) + optimization_result.optimization_time) / total_opts
            self.performance_metrics['average_optimization_time'] = new_avg
            
            # Update Sharpe improvement
            if self.optimization_history and len(self.optimization_history) > 1:
                prev_sharpe = self.optimization_history[-2]['sharpe_ratio']
                current_sharpe = optimization_result.sharpe_ratio
                sharpe_improvement = current_sharpe - prev_sharpe
                
                current_avg_improvement = self.performance_metrics['average_sharpe_improvement']
                new_avg_improvement = ((current_avg_improvement * (total_opts - 1)) + sharpe_improvement) / total_opts
                self.performance_metrics['average_sharpe_improvement'] = new_avg_improvement
            
            if optimization_result.rebalance_needed:
                self.performance_metrics['total_rebalances'] += 1
            
            self.performance_metrics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.warning(f"Error updating performance metrics: {e}")
    
    async def _update_market_data(self, market_data: Dict[str, Any]):
        """Update market data for optimization."""
        try:
            # Update price history
            if 'prices' in market_data:
                for symbol, price in market_data['prices'].items():
                    if symbol not in self.price_history:
                        self.price_history[symbol] = []
                    
                    self.price_history[symbol].append(price)
                    
                    # Calculate returns
                    if len(self.price_history[symbol]) > 1:
                        prev_price = self.price_history[symbol][-2]
                        return_val = (price - prev_price) / prev_price
                        
                        if symbol not in self.return_history:
                            self.return_history[symbol] = []
                        
                        self.return_history[symbol].append(return_val)
                        
                        # Keep only recent history
                        if len(self.return_history[symbol]) > 1000:
                            self.return_history[symbol] = self.return_history[symbol][-1000:]
                    
                    # Keep only recent price history
                    if len(self.price_history[symbol]) > 1000:
                        self.price_history[symbol] = self.price_history[symbol][-1000:]
            
        except Exception as e:
            self.logger.warning(f"Error updating market data: {e}")
    
    async def _initialize_optimization_engine(self):
        """Initialize optimization engine."""
        try:
            # Load historical data if available
            # In a real implementation, this would load from database
            
            # Initialize volatility history
            self.volatility_history = {}
            
            # Set up default optimization parameters
            self.logger.info("Portfolio optimizer engine initialized")
            
        except Exception as e:
            self.logger.error(f"Error initializing optimization engine: {e}")
    
    async def _save_optimization_data(self):
        """Save optimization data."""
        try:
            # In a real implementation, this would save to database
            self.logger.info("Optimization data saved")
        except Exception as e:
            self.logger.warning(f"Error saving optimization data: {e}")
    
    def _create_empty_optimization(self, objective: OptimizationObjective) -> PortfolioOptimization:
        """Create empty optimization result."""
        return PortfolioOptimization(
            objective=objective,
            allocations=[],
            expected_return=0,
            expected_volatility=0,
            sharpe_ratio=0,
            total_turnover=0,
            optimization_score=0,
            rebalance_needed=False,
            constraints_satisfied=True,
            optimization_time=0,
            recommendations=["No optimization performed"],
            risk_metrics={},
            performance_attribution={},
            timestamp=datetime.now()
        )
    
    def get_optimization_status(self) -> Dict[str, Any]:
        """Get current optimization status."""
        try:
            status = {
                'is_running': True,
                'last_optimization': self.current_optimization.timestamp if self.current_optimization else None,
                'last_rebalance': self.last_rebalance,
                'rebalance_frequency': self.rebalance_frequency.value,
                'performance_metrics': self.performance_metrics.copy(),
                'optimization_history_size': len(self.optimization_history),
                'asset_universe_size': len(self.asset_universe),
                'current_objective': self.current_optimization.objective.value if self.current_optimization else None,
                'current_score': self.current_optimization.optimization_score if self.current_optimization else 0
            }
            
            return status
            
        except Exception as e:
            self.logger.warning(f"Error getting optimization status: {e}")
            return {'is_running': False, 'error': str(e)}
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        try:
            if not self.optimization_history:
                return {'message': 'No optimization history available'}
            
            recent_history = self.optimization_history[-30:]  # Last 30 optimizations
            
            summary = {
                'total_optimizations': len(self.optimization_history),
                'recent_optimizations': len(recent_history),
                'average_sharpe_ratio': np.mean([h['sharpe_ratio'] for h in recent_history]),
                'average_expected_return': np.mean([h['expected_return'] for h in recent_history]),
                'average_volatility': np.mean([h['expected_volatility'] for h in recent_history]),
                'average_optimization_score': np.mean([h['optimization_score'] for h in recent_history]),
                'rebalance_frequency': sum(1 for h in recent_history if h['rebalance_needed']) / len(recent_history),
                'constraint_satisfaction_rate': sum(1 for h in recent_history if h['constraints_satisfied']) / len(recent_history),
                'average_optimization_time': np.mean([h['optimization_time'] for h in recent_history]),
                'performance_trend': self._calculate_performance_trend(recent_history)
            }
            
            return summary
            
        except Exception as e:
            self.logger.warning(f"Error getting performance summary: {e}")
            return {'error': str(e)}
    
    def _calculate_performance_trend(self, history: List[Dict[str, Any]]) -> str:
        """Calculate performance trend."""
        try:
            if len(history) < 5:
                return 'insufficient_data'
            
            # Calculate trend in Sharpe ratio
            sharpe_ratios = [h['sharpe_ratio'] for h in history]
            
            # Simple linear trend
            x = np.arange(len(sharpe_ratios))
            slope = np.polyfit(x, sharpe_ratios, 1)[0]
            
            if slope > 0.01:
                return 'improving'
            elif slope < -0.01:
                return 'declining'
            else:
                return 'stable'
                
        except Exception:
            return 'unknown'