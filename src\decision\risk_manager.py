#!/usr/bin/env python3
"""
Risk Manager - Comprehensive risk management system for AI trading agent

Author: inkbytefo
Description: Manages portfolio risk, position sizing, and risk assessment for trading decisions
"""

import asyncio
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from dataclasses import dataclass
from enum import Enum

from ..config.settings import RiskSettings
from ..utils.common import get_logger, PerformanceTimer, safe_get, calculate_duration
from ..utils.base_classes import BaseComponent, ComponentStatus


class RiskLevel(Enum):
    """Risk levels for different assessments."""
    VERY_LOW = "very_low"
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"
    CRITICAL = "critical"


class RiskType(Enum):
    """Types of risks to monitor."""
    MARKET_RISK = "market_risk"
    LIQUIDITY_RISK = "liquidity_risk"
    CONCENTRATION_RISK = "concentration_risk"
    VOLATILITY_RISK = "volatility_risk"
    CORRELATION_RISK = "correlation_risk"
    DRAWDOWN_RISK = "drawdown_risk"
    LEVERAGE_RISK = "leverage_risk"


@dataclass
class RiskMetric:
    """Risk metric data structure."""
    name: str
    value: float
    threshold: float
    level: RiskLevel
    description: str
    timestamp: datetime
    recommendations: List[str]


@dataclass
class PositionRisk:
    """Position-specific risk assessment."""
    symbol: str
    position_size: float
    market_value: float
    risk_score: float
    var_1d: float  # 1-day Value at Risk
    var_7d: float  # 7-day Value at Risk
    max_loss: float
    concentration_risk: float
    liquidity_risk: float
    volatility_risk: float
    correlation_risk: float
    recommendations: List[str]
    timestamp: datetime


@dataclass
class PortfolioRisk:
    """Portfolio-wide risk assessment."""
    total_value: float
    total_risk: float
    var_1d: float
    var_7d: float
    expected_shortfall: float
    max_drawdown: float
    sharpe_ratio: float
    sortino_ratio: float
    beta: float
    correlation_matrix: Dict[str, Dict[str, float]]
    risk_metrics: List[RiskMetric]
    position_risks: List[PositionRisk]
    overall_level: RiskLevel
    recommendations: List[str]
    timestamp: datetime


class RiskManager(BaseComponent):
    """Comprehensive risk management system for AI trading agent."""
    
    def __init__(self, risk_settings: RiskSettings):
        super().__init__()
        self.risk_settings = risk_settings
        self.logger = get_logger(__name__)
        
        # Risk thresholds from centralized configuration
        self.risk_thresholds = {
            'max_portfolio_risk': risk_settings.max_portfolio_risk,
            'max_position_risk': risk_settings.max_position_risk,
            'max_daily_loss': risk_settings.max_daily_loss,
            'max_drawdown': risk_settings.max_drawdown,
            'position_size_limit': risk_settings.position_size_limit,
            'max_open_orders': risk_settings.max_open_orders,
            'max_concentration': risk_settings.max_concentration,
            'max_correlation': risk_settings.max_correlation,
            'min_liquidity': risk_settings.min_liquidity,
            'max_leverage': risk_settings.max_leverage,
            'var_confidence': risk_settings.var_confidence,
            'correlation_threshold': risk_settings.correlation_threshold,
            'concentration_limit': risk_settings.concentration_limit,
            'leverage_limit': risk_settings.leverage_limit,
            'stop_loss_percentage': risk_settings.stop_loss_percentage,
            'take_profit_percentage': risk_settings.take_profit_percentage
        }
        
        # Risk calculation parameters
        self.lookback_periods = {
            'short': 30,   # 30 days
            'medium': 90,  # 90 days
            'long': 252    # 252 days (1 year)
        }
        
        # Market data for risk calculations
        self.price_history = {}
        self.volatility_history = {}
        self.correlation_matrix = {}
        
        # Risk monitoring
        self.risk_alerts = []
        self.risk_history = []
        self.max_history_size = 1000
        
        # Performance tracking
        self.performance_metrics = {
            'total_assessments': 0,
            'high_risk_alerts': 0,
            'risk_prevented_losses': 0,
            'average_portfolio_risk': 0,
            'last_update': None
        }
        
        # Current risk state
        self.current_portfolio_risk = None
        self.last_risk_assessment = None
    
    async def start(self) -> None:
        """Start the risk manager with comprehensive initialization."""
        if self.status == ComponentStatus.RUNNING:
            self.logger.warning("Risk Manager is already running")
            return
        
        self.logger.info("Starting Risk Manager...")
        self.status = ComponentStatus.STARTING
        
        try:
            with PerformanceTimer() as timer:
                # Initialize risk monitoring
                await self._initialize_risk_monitoring()
                
                # Validate risk thresholds
                self._validate_risk_thresholds()
                
                self.status = ComponentStatus.RUNNING
                self.logger.info(f"Risk Manager started successfully in {timer.elapsed:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Failed to start Risk Manager: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the risk manager with proper cleanup."""
        if self.status == ComponentStatus.STOPPED:
            self.logger.warning("Risk Manager is already stopped")
            return
        
        self.logger.info("Stopping Risk Manager...")
        self.status = ComponentStatus.STOPPING
        
        try:
            with PerformanceTimer() as timer:
                # Save current risk data
                await self._save_risk_data()
                
                # Clear caches
                self._clear_caches()
                
                self.status = ComponentStatus.STOPPED
                self.logger.info(f"Risk Manager stopped successfully in {timer.elapsed:.2f}s")
                
        except Exception as e:
            self.status = ComponentStatus.ERROR
            self.logger.error(f"Error stopping Risk Manager: {e}")
            raise
    
    async def assess_portfolio_risk(self, portfolio_data: Dict[str, Any], 
                                  market_data: Dict[str, Any]) -> PortfolioRisk:
        """Assess comprehensive portfolio risk with performance optimization."""
        with PerformanceTimer() as timer:
            try:
                # Input validation
                positions = safe_get(portfolio_data, 'positions', {})
                total_value = safe_get(portfolio_data, 'total_value', 0.0)
                
                if not positions or total_value <= 0:
                    self.logger.warning("Empty portfolio or zero value, returning empty risk assessment")
                    return self._create_empty_portfolio_risk()
                
                # Update market data efficiently
                await self._update_market_data(market_data)
                
                # Parallel calculation of position risks
                position_risk_tasks = [
                    self._calculate_position_risk(symbol, position_size, total_value, market_data)
                    for symbol, position_size in positions.items() if position_size != 0
                ]
                
                position_risks = [risk for risk in await asyncio.gather(*position_risk_tasks, return_exceptions=True) 
                                if isinstance(risk, PositionRisk)]
                
                # Parallel calculation of portfolio metrics
                portfolio_metrics_tasks = [
                    self._calculate_portfolio_var(positions, total_value, 1),
                    self._calculate_portfolio_var(positions, total_value, 7),
                    self._calculate_expected_shortfall(positions, total_value),
                    self._calculate_max_drawdown(portfolio_data),
                    self._calculate_sharpe_ratio(portfolio_data),
                    self._calculate_sortino_ratio(portfolio_data),
                    self._calculate_portfolio_beta(positions),
                    self._calculate_correlation_matrix(list(positions.keys()))
                ]
                
                (
                    portfolio_var_1d, portfolio_var_7d, expected_shortfall,
                    max_drawdown, sharpe_ratio, sortino_ratio, beta, correlation_matrix
                ) = await asyncio.gather(*portfolio_metrics_tasks, return_exceptions=True)
                
                # Handle any calculation errors
                portfolio_var_1d = portfolio_var_1d if isinstance(portfolio_var_1d, (int, float)) else 0.0
                portfolio_var_7d = portfolio_var_7d if isinstance(portfolio_var_7d, (int, float)) else 0.0
                expected_shortfall = expected_shortfall if isinstance(expected_shortfall, (int, float)) else 0.0
                max_drawdown = max_drawdown if isinstance(max_drawdown, (int, float)) else 0.0
                sharpe_ratio = sharpe_ratio if isinstance(sharpe_ratio, (int, float)) else 0.0
                sortino_ratio = sortino_ratio if isinstance(sortino_ratio, (int, float)) else 0.0
                beta = beta if isinstance(beta, (int, float)) else 1.0
                correlation_matrix = correlation_matrix if isinstance(correlation_matrix, dict) else {}
                
                # Calculate risk metrics
                risk_metrics = await self._calculate_risk_metrics(position_risks, total_value)
                
                # Determine overall risk level
                overall_risk = (
                    sum(metric.value for metric in risk_metrics) / len(risk_metrics) 
                    if risk_metrics else 0.0
                )
                overall_level = self._determine_risk_level(overall_risk)
                
                # Generate recommendations
                recommendations = await self._generate_portfolio_recommendations(
                    position_risks, risk_metrics, overall_level
                )
                
                # Create portfolio risk assessment
                portfolio_risk = PortfolioRisk(
                    total_value=total_value,
                    total_risk=overall_risk,
                    var_1d=portfolio_var_1d,
                    var_7d=portfolio_var_7d,
                    expected_shortfall=expected_shortfall,
                    max_drawdown=max_drawdown,
                    sharpe_ratio=sharpe_ratio,
                    sortino_ratio=sortino_ratio,
                    beta=beta,
                    correlation_matrix=correlation_matrix,
                    risk_metrics=risk_metrics,
                    position_risks=position_risks,
                    overall_level=overall_level,
                    recommendations=recommendations,
                    timestamp=datetime.now()
                )
                
                # Update current state
                self.current_portfolio_risk = portfolio_risk
                self.last_risk_assessment = datetime.now()
                
                # Async operations for alerts and history
                await asyncio.gather(
                    self._check_risk_alerts(portfolio_risk),
                    return_exceptions=True
                )
                
                self._update_risk_history(portfolio_risk)
                
                self.logger.debug(f"Portfolio risk assessment completed in {timer.elapsed:.3f}s")
            
            # Update performance metrics
            await self._update_performance_metrics()
            
            self.logger.info(f"Portfolio risk assessment completed in {(datetime.now() - assessment_start).total_seconds():.2f}s")
            self.logger.info(f"Overall risk level: {overall_level.value}, Total risk: {overall_risk:.3f}")
            
            return portfolio_risk
            
        except Exception as e:
            self.logger.error(f"Error in portfolio risk assessment: {e}")
            return self._create_empty_portfolio_risk()
    
    async def assess_trade_risk(self, symbol: str, trade_size: float, 
                              trade_type: str, portfolio_data: Dict[str, Any],
                              market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risk for a potential trade."""
        try:
            current_position = portfolio_data.get('positions', {}).get(symbol, 0)
            total_value = portfolio_data.get('total_value', 0)
            
            if total_value <= 0:
                return {'approved': False, 'reason': 'Invalid portfolio value'}
            
            # Calculate new position after trade
            new_position = current_position + (trade_size if trade_type == 'buy' else -trade_size)
            
            # Calculate position risk
            position_risk = await self._calculate_position_risk(
                symbol, new_position, total_value, market_data
            )
            
            if not position_risk:
                return {'approved': False, 'reason': 'Unable to calculate position risk'}
            
            # Check risk thresholds
            risk_checks = await self._perform_trade_risk_checks(
                symbol, new_position, position_risk, portfolio_data
            )
            
            # Calculate impact on portfolio risk
            portfolio_impact = await self._calculate_portfolio_impact(
                symbol, new_position, current_position, portfolio_data
            )
            
            # Make approval decision
            approved = all(check['passed'] for check in risk_checks.values())
            
            # Generate recommendations
            recommendations = []
            for check_name, check_result in risk_checks.items():
                if not check_result['passed']:
                    recommendations.append(f"Risk check failed: {check_name} - {check_result['reason']}")
            
            if not approved:
                recommendations.append("Consider reducing trade size or avoiding this trade")
            
            return {
                'approved': approved,
                'position_risk': position_risk,
                'risk_checks': risk_checks,
                'portfolio_impact': portfolio_impact,
                'recommendations': recommendations,
                'risk_score': position_risk.risk_score,
                'max_suggested_size': await self._calculate_max_safe_trade_size(
                    symbol, trade_type, portfolio_data, market_data
                )
            }
            
        except Exception as e:
            self.logger.error(f"Error in trade risk assessment: {e}")
            return {'approved': False, 'reason': f'Risk assessment error: {str(e)}'}
    
    async def _calculate_position_risk(self, symbol: str, position_size: float,
                                     total_value: float, market_data: Dict[str, Any]) -> Optional[PositionRisk]:
        """Calculate risk metrics for a specific position."""
        try:
            # Get current price
            current_price = await self._get_current_price(symbol, market_data)
            if not current_price:
                return None
            
            market_value = abs(position_size * current_price)
            
            # Calculate concentration risk
            concentration_risk = market_value / total_value if total_value > 0 else 0
            
            # Calculate volatility risk
            volatility = await self._get_volatility(symbol, 30)  # 30-day volatility
            volatility_risk = volatility * np.sqrt(252)  # Annualized
            
            # Calculate VaR
            var_1d = market_value * volatility * 2.33  # 99% confidence
            var_7d = market_value * volatility * np.sqrt(7) * 2.33
            
            # Calculate maximum potential loss
            max_loss = market_value * 0.5  # Assume max 50% loss
            
            # Calculate liquidity risk
            liquidity_risk = await self._calculate_liquidity_risk(symbol, market_data)
            
            # Calculate correlation risk
            correlation_risk = await self._calculate_correlation_risk(symbol)
            
            # Calculate overall risk score
            risk_score = self._calculate_position_risk_score(
                concentration_risk, volatility_risk, liquidity_risk, correlation_risk
            )
            
            # Generate recommendations
            recommendations = self._generate_position_recommendations(
                symbol, concentration_risk, volatility_risk, liquidity_risk, correlation_risk
            )
            
            return PositionRisk(
                symbol=symbol,
                position_size=position_size,
                market_value=market_value,
                risk_score=risk_score,
                var_1d=var_1d,
                var_7d=var_7d,
                max_loss=max_loss,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                volatility_risk=volatility_risk,
                correlation_risk=correlation_risk,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.warning(f"Error calculating position risk for {symbol}: {e}")
            return None
    
    async def _calculate_portfolio_var(self, positions: Dict[str, float], 
                                     total_value: float, days: int) -> float:
        """Calculate portfolio Value at Risk."""
        try:
            if not positions or total_value <= 0:
                return 0
            
            # Get position weights
            weights = {}
            for symbol, position_size in positions.items():
                price = await self._get_current_price(symbol, {})
                if price:
                    market_value = abs(position_size * price)
                    weights[symbol] = market_value / total_value
            
            if not weights:
                return 0
            
            # Calculate portfolio volatility
            portfolio_variance = 0
            
            for symbol1, weight1 in weights.items():
                vol1 = await self._get_volatility(symbol1, 30)
                
                for symbol2, weight2 in weights.items():
                    vol2 = await self._get_volatility(symbol2, 30)
                    
                    if symbol1 == symbol2:
                        correlation = 1.0
                    else:
                        correlation = await self._get_correlation(symbol1, symbol2)
                    
                    portfolio_variance += weight1 * weight2 * vol1 * vol2 * correlation
            
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Calculate VaR
            confidence_level = self.risk_thresholds['var_confidence']
            z_score = 2.33 if confidence_level == 0.99 else 1.96  # 99% or 95%
            
            var = total_value * portfolio_volatility * np.sqrt(days) * z_score
            
            return var
            
        except Exception as e:
            self.logger.warning(f"Error calculating portfolio VaR: {e}")
            return 0
    
    async def _calculate_expected_shortfall(self, positions: Dict[str, float], 
                                          total_value: float) -> float:
        """Calculate Expected Shortfall (Conditional VaR)."""
        try:
            # Simplified ES calculation
            var_1d = await self._calculate_portfolio_var(positions, total_value, 1)
            
            # ES is typically 1.3-1.5 times VaR for normal distributions
            expected_shortfall = var_1d * 1.4
            
            return expected_shortfall
            
        except Exception as e:
            self.logger.warning(f"Error calculating expected shortfall: {e}")
            return 0
    
    async def _calculate_max_drawdown(self, portfolio_data: Dict[str, Any]) -> float:
        """Calculate maximum drawdown from portfolio history."""
        try:
            # Get portfolio value history
            value_history = portfolio_data.get('value_history', [])
            
            if len(value_history) < 2:
                return 0
            
            # Calculate drawdowns
            values = [entry['value'] for entry in value_history]
            peak = values[0]
            max_drawdown = 0
            
            for value in values[1:]:
                if value > peak:
                    peak = value
                else:
                    drawdown = (peak - value) / peak
                    max_drawdown = max(max_drawdown, drawdown)
            
            return max_drawdown
            
        except Exception as e:
            self.logger.warning(f"Error calculating max drawdown: {e}")
            return 0
    
    async def _calculate_sharpe_ratio(self, portfolio_data: Dict[str, Any]) -> float:
        """Calculate Sharpe ratio."""
        try:
            # Get return history
            value_history = portfolio_data.get('value_history', [])
            
            if len(value_history) < 30:  # Need at least 30 data points
                return 0
            
            # Calculate daily returns
            values = [entry['value'] for entry in value_history[-252:]]  # Last year
            returns = [(values[i] - values[i-1]) / values[i-1] for i in range(1, len(values))]
            
            if not returns:
                return 0
            
            # Calculate metrics
            avg_return = np.mean(returns) * 252  # Annualized
            volatility = np.std(returns) * np.sqrt(252)  # Annualized
            risk_free_rate = 0.02  # Assume 2% risk-free rate
            
            if volatility == 0:
                return 0
            
            sharpe_ratio = (avg_return - risk_free_rate) / volatility
            
            return sharpe_ratio
            
        except Exception as e:
            self.logger.warning(f"Error calculating Sharpe ratio: {e}")
            return 0
    
    async def _calculate_sortino_ratio(self, portfolio_data: Dict[str, Any]) -> float:
        """Calculate Sortino ratio."""
        try:
            # Get return history
            value_history = portfolio_data.get('value_history', [])
            
            if len(value_history) < 30:
                return 0
            
            # Calculate daily returns
            values = [entry['value'] for entry in value_history[-252:]]
            returns = [(values[i] - values[i-1]) / values[i-1] for i in range(1, len(values))]
            
            if not returns:
                return 0
            
            # Calculate downside deviation
            negative_returns = [r for r in returns if r < 0]
            
            if not negative_returns:
                return float('inf')  # No downside risk
            
            downside_deviation = np.std(negative_returns) * np.sqrt(252)
            avg_return = np.mean(returns) * 252
            risk_free_rate = 0.02
            
            if downside_deviation == 0:
                return float('inf')
            
            sortino_ratio = (avg_return - risk_free_rate) / downside_deviation
            
            return sortino_ratio
            
        except Exception as e:
            self.logger.warning(f"Error calculating Sortino ratio: {e}")
            return 0
    
    async def _calculate_portfolio_beta(self, positions: Dict[str, float]) -> float:
        """Calculate portfolio beta relative to market."""
        try:
            if not positions:
                return 1.0
            
            # Simplified beta calculation
            # In practice, this would use actual market data and regression
            
            # Assume crypto market has higher beta
            crypto_symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
            
            total_crypto_weight = 0
            for symbol in positions.keys():
                for crypto in crypto_symbols:
                    if crypto in symbol:
                        total_crypto_weight += 0.2  # Assume each crypto position is 20% weight
                        break
            
            # Higher crypto exposure = higher beta
            beta = 1.0 + (total_crypto_weight * 0.5)  # Add 0.5 beta per crypto position
            
            return min(beta, 3.0)  # Cap at 3.0
            
        except Exception as e:
            self.logger.warning(f"Error calculating portfolio beta: {e}")
            return 1.0
    
    async def _calculate_correlation_matrix(self, symbols: List[str]) -> Dict[str, Dict[str, float]]:
        """Calculate correlation matrix for portfolio symbols."""
        try:
            correlation_matrix = {}
            
            for symbol1 in symbols:
                correlation_matrix[symbol1] = {}
                
                for symbol2 in symbols:
                    if symbol1 == symbol2:
                        correlation_matrix[symbol1][symbol2] = 1.0
                    else:
                        correlation = await self._get_correlation(symbol1, symbol2)
                        correlation_matrix[symbol1][symbol2] = correlation
            
            return correlation_matrix
            
        except Exception as e:
            self.logger.warning(f"Error calculating correlation matrix: {e}")
            return {}
    
    async def _calculate_risk_metrics(self, position_risks: List[PositionRisk], 
                                    total_value: float) -> List[RiskMetric]:
        """Calculate portfolio-wide risk metrics."""
        metrics = []
        
        try:
            if not position_risks:
                return metrics
            
            # Concentration risk metric
            max_concentration = max(pos.concentration_risk for pos in position_risks)
            concentration_metric = RiskMetric(
                name="Concentration Risk",
                value=max_concentration,
                threshold=self.risk_thresholds['max_concentration'],
                level=self._determine_risk_level(max_concentration / self.risk_thresholds['max_concentration']),
                description=f"Maximum single position concentration: {max_concentration:.1%}",
                timestamp=datetime.now(),
                recommendations=self._get_concentration_recommendations(max_concentration)
            )
            metrics.append(concentration_metric)
            
            # Volatility risk metric
            avg_volatility = np.mean([pos.volatility_risk for pos in position_risks])
            volatility_metric = RiskMetric(
                name="Volatility Risk",
                value=avg_volatility,
                threshold=0.3,  # 30% volatility threshold
                level=self._determine_risk_level(avg_volatility / 0.3),
                description=f"Average portfolio volatility: {avg_volatility:.1%}",
                timestamp=datetime.now(),
                recommendations=self._get_volatility_recommendations(avg_volatility)
            )
            metrics.append(volatility_metric)
            
            # Liquidity risk metric
            avg_liquidity = np.mean([1 - pos.liquidity_risk for pos in position_risks])
            liquidity_metric = RiskMetric(
                name="Liquidity Risk",
                value=1 - avg_liquidity,
                threshold=1 - self.risk_thresholds['min_liquidity'],
                level=self._determine_risk_level((1 - avg_liquidity) / (1 - self.risk_thresholds['min_liquidity'])),
                description=f"Average portfolio liquidity: {avg_liquidity:.1%}",
                timestamp=datetime.now(),
                recommendations=self._get_liquidity_recommendations(avg_liquidity)
            )
            metrics.append(liquidity_metric)
            
            # Overall risk metric
            overall_risk = np.mean([pos.risk_score for pos in position_risks])
            overall_metric = RiskMetric(
                name="Overall Risk",
                value=overall_risk,
                threshold=self.risk_thresholds['max_portfolio_risk'],
                level=self._determine_risk_level(overall_risk / self.risk_thresholds['max_portfolio_risk']),
                description=f"Overall portfolio risk score: {overall_risk:.3f}",
                timestamp=datetime.now(),
                recommendations=self._get_overall_recommendations(overall_risk)
            )
            metrics.append(overall_metric)
            
        except Exception as e:
            self.logger.warning(f"Error calculating risk metrics: {e}")
        
        return metrics
    
    def _determine_risk_level(self, risk_ratio: float) -> RiskLevel:
        """Determine risk level based on risk ratio."""
        if risk_ratio <= 0.3:
            return RiskLevel.VERY_LOW
        elif risk_ratio <= 0.5:
            return RiskLevel.LOW
        elif risk_ratio <= 0.7:
            return RiskLevel.MEDIUM
        elif risk_ratio <= 0.9:
            return RiskLevel.HIGH
        elif risk_ratio <= 1.2:
            return RiskLevel.VERY_HIGH
        else:
            return RiskLevel.CRITICAL
    
    async def _get_current_price(self, symbol: str, market_data: Dict[str, Any]) -> Optional[float]:
        """Get current price for a symbol."""
        try:
            # Try to get from market data first
            if 'prices' in market_data and symbol in market_data['prices']:
                return market_data['prices'][symbol]
            
            # Fallback to stored price history
            if symbol in self.price_history and self.price_history[symbol]:
                return self.price_history[symbol][-1]
            
            # Default price for testing
            return 50.0
            
        except Exception:
            return None
    
    async def _get_volatility(self, symbol: str, days: int) -> float:
        """Get volatility for a symbol."""
        try:
            # Try to get from stored volatility history
            if symbol in self.volatility_history and self.volatility_history[symbol]:
                return self.volatility_history[symbol][-1]
            
            # Calculate from price history if available
            if symbol in self.price_history and len(self.price_history[symbol]) > days:
                prices = self.price_history[symbol][-days:]
                returns = [(prices[i] - prices[i-1]) / prices[i-1] for i in range(1, len(prices))]
                return np.std(returns) if returns else 0.02
            
            # Default volatility based on asset type
            if 'BTC' in symbol or 'ETH' in symbol:
                return 0.04  # 4% daily volatility for major cryptos
            else:
                return 0.06  # 6% daily volatility for altcoins
            
        except Exception:
            return 0.05  # Default 5% volatility
    
    async def _get_correlation(self, symbol1: str, symbol2: str) -> float:
        """Get correlation between two symbols."""
        try:
            # Check stored correlation matrix
            if (symbol1 in self.correlation_matrix and 
                symbol2 in self.correlation_matrix[symbol1]):
                return self.correlation_matrix[symbol1][symbol2]
            
            # Default correlations based on asset types
            crypto_symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
            
            symbol1_is_crypto = any(crypto in symbol1 for crypto in crypto_symbols)
            symbol2_is_crypto = any(crypto in symbol2 for crypto in crypto_symbols)
            
            if symbol1_is_crypto and symbol2_is_crypto:
                if 'BTC' in symbol1 and 'ETH' in symbol2:
                    return 0.7  # High correlation between BTC and ETH
                elif 'BTC' in symbol1 or 'BTC' in symbol2:
                    return 0.6  # Medium-high correlation with BTC
                else:
                    return 0.5  # Medium correlation between altcoins
            
            return 0.3  # Low correlation for different asset types
            
        except Exception:
            return 0.5  # Default medium correlation
    
    async def _calculate_liquidity_risk(self, symbol: str, market_data: Dict[str, Any]) -> float:
        """Calculate liquidity risk for a symbol."""
        try:
            # Get volume data
            volume = 0
            if 'volumes' in market_data and symbol in market_data['volumes']:
                volume = market_data['volumes'][symbol]
            
            # Get spread data
            spread = 0.001  # Default 0.1% spread
            if 'spreads' in market_data and symbol in market_data['spreads']:
                spread = market_data['spreads'][symbol]
            
            # Calculate liquidity score
            # Higher volume = lower liquidity risk
            # Lower spread = lower liquidity risk
            
            volume_score = min(volume / 1000000, 1.0)  # Normalize to $1M volume
            spread_score = max(0, 1 - spread * 100)  # Normalize spread
            
            liquidity_score = (volume_score * 0.7 + spread_score * 0.3)
            liquidity_risk = 1 - liquidity_score
            
            return max(0, min(1, liquidity_risk))
            
        except Exception:
            return 0.3  # Default medium liquidity risk
    
    async def _calculate_correlation_risk(self, symbol: str) -> float:
        """Calculate correlation risk for a symbol."""
        try:
            # Get correlations with other portfolio positions
            # This would require current portfolio data
            
            # For now, return default based on asset type
            crypto_symbols = ['BTC', 'ETH', 'ADA', 'DOT', 'LINK']
            
            if any(crypto in symbol for crypto in crypto_symbols):
                return 0.6  # High correlation risk for crypto assets
            
            return 0.3  # Lower correlation risk for other assets
            
        except Exception:
            return 0.5  # Default medium correlation risk
    
    def _calculate_position_risk_score(self, concentration_risk: float, 
                                     volatility_risk: float, liquidity_risk: float,
                                     correlation_risk: float) -> float:
        """Calculate overall position risk score."""
        try:
            # Weight different risk factors
            weights = {
                'concentration': 0.3,
                'volatility': 0.3,
                'liquidity': 0.2,
                'correlation': 0.2
            }
            
            risk_score = (concentration_risk * weights['concentration'] +
                         volatility_risk * weights['volatility'] +
                         liquidity_risk * weights['liquidity'] +
                         correlation_risk * weights['correlation'])
            
            return max(0, min(1, risk_score))
            
        except Exception:
            return 0.5
    
    def _generate_position_recommendations(self, symbol: str, concentration_risk: float,
                                         volatility_risk: float, liquidity_risk: float,
                                         correlation_risk: float) -> List[str]:
        """Generate recommendations for position risk management."""
        recommendations = []
        
        try:
            if concentration_risk > self.risk_thresholds['max_concentration']:
                recommendations.append(f"Reduce {symbol} position size - concentration too high ({concentration_risk:.1%})")
            
            if volatility_risk > 0.4:  # 40% volatility threshold
                recommendations.append(f"Consider hedging {symbol} position due to high volatility ({volatility_risk:.1%})")
            
            if liquidity_risk > 0.5:  # 50% liquidity risk threshold
                recommendations.append(f"Monitor {symbol} liquidity closely - may be difficult to exit")
            
            if correlation_risk > 0.7:  # 70% correlation risk threshold
                recommendations.append(f"Diversify away from {symbol} - high correlation with other positions")
            
            if not recommendations:
                recommendations.append(f"{symbol} position within acceptable risk parameters")
            
        except Exception as e:
            self.logger.warning(f"Error generating position recommendations: {e}")
            recommendations = ["Monitor position risk regularly"]
        
        return recommendations
    
    def _get_concentration_recommendations(self, max_concentration: float) -> List[str]:
        """Get recommendations for concentration risk."""
        recommendations = []
        
        if max_concentration > self.risk_thresholds['max_concentration']:
            recommendations.append("Reduce largest position sizes")
            recommendations.append("Diversify across more assets")
            recommendations.append("Consider position size limits")
        else:
            recommendations.append("Concentration risk within acceptable limits")
        
        return recommendations
    
    def _get_volatility_recommendations(self, avg_volatility: float) -> List[str]:
        """Get recommendations for volatility risk."""
        recommendations = []
        
        if avg_volatility > 0.3:
            recommendations.append("Consider reducing position sizes in high volatility assets")
            recommendations.append("Implement stop-loss orders")
            recommendations.append("Consider hedging strategies")
        else:
            recommendations.append("Volatility risk manageable")
        
        return recommendations
    
    def _get_liquidity_recommendations(self, avg_liquidity: float) -> List[str]:
        """Get recommendations for liquidity risk."""
        recommendations = []
        
        if avg_liquidity < self.risk_thresholds['min_liquidity']:
            recommendations.append("Focus on more liquid assets")
            recommendations.append("Reduce positions in illiquid assets")
            recommendations.append("Monitor market depth before large trades")
        else:
            recommendations.append("Liquidity risk acceptable")
        
        return recommendations
    
    def _get_overall_recommendations(self, overall_risk: float) -> List[str]:
        """Get overall portfolio recommendations."""
        recommendations = []
        
        if overall_risk > self.risk_thresholds['max_portfolio_risk']:
            recommendations.append("Reduce overall portfolio risk")
            recommendations.append("Consider defensive positioning")
            recommendations.append("Review and adjust risk management strategy")
        else:
            recommendations.append("Overall portfolio risk within acceptable range")
        
        return recommendations
    
    async def _generate_portfolio_recommendations(self, position_risks: List[PositionRisk],
                                                risk_metrics: List[RiskMetric],
                                                overall_level: RiskLevel) -> List[str]:
        """Generate portfolio-wide recommendations."""
        recommendations = []
        
        try:
            # Check overall risk level
            if overall_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                recommendations.append("URGENT: Portfolio risk is elevated - consider reducing positions")
            
            # Check individual risk metrics
            for metric in risk_metrics:
                if metric.level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                    recommendations.extend(metric.recommendations)
            
            # Check position-specific risks
            high_risk_positions = [pos for pos in position_risks if pos.risk_score > 0.7]
            
            if high_risk_positions:
                symbols = [pos.symbol for pos in high_risk_positions]
                recommendations.append(f"High risk positions detected: {', '.join(symbols)}")
            
            # General recommendations
            if len(position_risks) < 3:
                recommendations.append("Consider diversifying across more assets")
            
            if not recommendations:
                recommendations.append("Portfolio risk management is on track")
            
        except Exception as e:
            self.logger.warning(f"Error generating portfolio recommendations: {e}")
            recommendations = ["Regular risk monitoring recommended"]
        
        return recommendations
    
    async def _perform_trade_risk_checks(self, symbol: str, new_position: float,
                                       position_risk: PositionRisk,
                                       portfolio_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        """Perform comprehensive risk checks for a trade."""
        checks = {}
        
        try:
            total_value = portfolio_data.get('total_value', 0)
            
            # Position size check
            checks['position_size'] = {
                'passed': position_risk.concentration_risk <= self.risk_thresholds['max_concentration'],
                'reason': f"Position concentration: {position_risk.concentration_risk:.1%} (max: {self.risk_thresholds['max_concentration']:.1%})"
            }
            
            # Risk score check
            checks['risk_score'] = {
                'passed': position_risk.risk_score <= self.risk_thresholds['max_position_risk'] * 2,
                'reason': f"Position risk score: {position_risk.risk_score:.3f} (max: {self.risk_thresholds['max_position_risk'] * 2:.3f})"
            }
            
            # Liquidity check
            checks['liquidity'] = {
                'passed': position_risk.liquidity_risk <= (1 - self.risk_thresholds['min_liquidity']),
                'reason': f"Liquidity risk: {position_risk.liquidity_risk:.1%} (max: {(1 - self.risk_thresholds['min_liquidity']):.1%})"
            }
            
            # Portfolio impact check
            current_portfolio_risk = 0.1  # Placeholder
            if self.current_portfolio_risk:
                current_portfolio_risk = self.current_portfolio_risk.total_risk
            
            checks['portfolio_impact'] = {
                'passed': current_portfolio_risk <= self.risk_thresholds['max_portfolio_risk'],
                'reason': f"Portfolio risk impact: {current_portfolio_risk:.3f} (max: {self.risk_thresholds['max_portfolio_risk']:.3f})"
            }
            
        except Exception as e:
            self.logger.warning(f"Error performing trade risk checks: {e}")
            checks['error'] = {
                'passed': False,
                'reason': f"Risk check error: {str(e)}"
            }
        
        return checks
    
    async def _calculate_portfolio_impact(self, symbol: str, new_position: float,
                                        current_position: float,
                                        portfolio_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate impact of trade on portfolio risk."""
        try:
            position_change = new_position - current_position
            total_value = portfolio_data.get('total_value', 0)
            
            # Calculate position value change
            current_price = await self._get_current_price(symbol, {})
            value_change = abs(position_change * current_price) if current_price else 0
            
            # Calculate concentration change
            concentration_change = value_change / total_value if total_value > 0 else 0
            
            # Calculate risk change (simplified)
            volatility = await self._get_volatility(symbol, 30)
            risk_change = concentration_change * volatility
            
            return {
                'position_change': position_change,
                'value_change': value_change,
                'concentration_change': concentration_change,
                'risk_change': risk_change,
                'new_concentration': abs(new_position * current_price) / total_value if current_price and total_value > 0 else 0
            }
            
        except Exception as e:
            self.logger.warning(f"Error calculating portfolio impact: {e}")
            return {}
    
    async def _calculate_max_safe_trade_size(self, symbol: str, trade_type: str,
                                           portfolio_data: Dict[str, Any],
                                           market_data: Dict[str, Any]) -> float:
        """Calculate maximum safe trade size."""
        try:
            total_value = portfolio_data.get('total_value', 0)
            current_position = portfolio_data.get('positions', {}).get(symbol, 0)
            
            if total_value <= 0:
                return 0
            
            # Get current price
            current_price = await self._get_current_price(symbol, market_data)
            if not current_price:
                return 0
            
            # Calculate max position value based on concentration limit
            max_position_value = total_value * self.risk_thresholds['max_concentration']
            current_position_value = abs(current_position * current_price)
            
            # Calculate remaining capacity
            remaining_capacity = max_position_value - current_position_value
            
            if remaining_capacity <= 0:
                return 0
            
            # Convert to quantity
            max_trade_size = remaining_capacity / current_price
            
            # Apply additional safety margin
            safety_margin = 0.8  # 80% of calculated max
            safe_trade_size = max_trade_size * safety_margin
            
            return max(0, safe_trade_size)
            
        except Exception as e:
            self.logger.warning(f"Error calculating max safe trade size: {e}")
            return 0
    
    async def _check_risk_alerts(self, portfolio_risk: PortfolioRisk):
        """Check for risk alerts and generate notifications."""
        try:
            alerts = []
            
            # Check overall risk level
            if portfolio_risk.overall_level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                alerts.append({
                    'type': 'portfolio_risk',
                    'level': portfolio_risk.overall_level.value,
                    'message': f"Portfolio risk level is {portfolio_risk.overall_level.value}",
                    'timestamp': datetime.now()
                })
            
            # Check individual risk metrics
            for metric in portfolio_risk.risk_metrics:
                if metric.level in [RiskLevel.HIGH, RiskLevel.VERY_HIGH, RiskLevel.CRITICAL]:
                    alerts.append({
                        'type': 'risk_metric',
                        'metric': metric.name,
                        'level': metric.level.value,
                        'value': metric.value,
                        'threshold': metric.threshold,
                        'message': f"{metric.name} is {metric.level.value}: {metric.description}",
                        'timestamp': datetime.now()
                    })
            
            # Check position risks
            for position_risk in portfolio_risk.position_risks:
                if position_risk.risk_score > 0.8:  # High risk threshold
                    alerts.append({
                        'type': 'position_risk',
                        'symbol': position_risk.symbol,
                        'risk_score': position_risk.risk_score,
                        'message': f"{position_risk.symbol} has high risk score: {position_risk.risk_score:.3f}",
                        'timestamp': datetime.now()
                    })
            
            # Add alerts to history
            self.risk_alerts.extend(alerts)
            
            # Keep only recent alerts
            cutoff_time = datetime.now() - timedelta(hours=24)
            self.risk_alerts = [alert for alert in self.risk_alerts if alert['timestamp'] > cutoff_time]
            
            if alerts:
                self.logger.warning(f"Generated {len(alerts)} risk alerts")
                self.performance_metrics['high_risk_alerts'] += len(alerts)
            
        except Exception as e:
            self.logger.warning(f"Error checking risk alerts: {e}")
    
    def _update_risk_history(self, portfolio_risk: PortfolioRisk):
        """Update risk assessment history."""
        try:
            risk_entry = {
                'timestamp': portfolio_risk.timestamp,
                'total_risk': portfolio_risk.total_risk,
                'var_1d': portfolio_risk.var_1d,
                'var_7d': portfolio_risk.var_7d,
                'max_drawdown': portfolio_risk.max_drawdown,
                'sharpe_ratio': portfolio_risk.sharpe_ratio,
                'overall_level': portfolio_risk.overall_level.value,
                'num_positions': len(portfolio_risk.position_risks)
            }
            
            self.risk_history.append(risk_entry)
            
            # Keep only recent history
            if len(self.risk_history) > self.max_history_size:
                self.risk_history = self.risk_history[-self.max_history_size:]
            
        except Exception as e:
            self.logger.warning(f"Error updating risk history: {e}")
    
    async def _update_performance_metrics(self):
        """Update risk management performance metrics."""
        try:
            self.performance_metrics['total_assessments'] += 1
            
            if self.risk_history:
                avg_risk = np.mean([entry['total_risk'] for entry in self.risk_history])
                self.performance_metrics['average_portfolio_risk'] = avg_risk
            
            self.performance_metrics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.warning(f"Error updating performance metrics: {e}")
    
    async def _update_market_data(self, market_data: Dict[str, Any]):
        """Update market data for risk calculations."""
        try:
            # Update price history
            if 'prices' in market_data:
                for symbol, price in market_data['prices'].items():
                    if symbol not in self.price_history:
                        self.price_history[symbol] = []
                    
                    self.price_history[symbol].append(price)
                    
                    # Keep only recent history
                    if len(self.price_history[symbol]) > 1000:
                        self.price_history[symbol] = self.price_history[symbol][-1000:]
            
            # Update volatility data
            if 'volatilities' in market_data:
                for symbol, volatility in market_data['volatilities'].items():
                    if symbol not in self.volatility_history:
                        self.volatility_history[symbol] = []
                    
                    self.volatility_history[symbol].append(volatility)
                    
                    # Keep only recent history
                    if len(self.volatility_history[symbol]) > 100:
                        self.volatility_history[symbol] = self.volatility_history[symbol][-100:]
            
        except Exception as e:
            self.logger.warning(f"Error updating market data: {e}")
    
    async def _initialize_risk_monitoring(self):
        """Initialize risk monitoring system."""
        try:
            # Load historical data if available
            # In a real implementation, this would load from database
            
            self.logger.debug("Risk monitoring initialization completed")
            
        except Exception as e:
            self.logger.warning(f"Error initializing risk monitoring: {e}")
    
    async def _save_risk_data(self):
        """Save risk data and history."""
        try:
            # In a real implementation, this would save to database
            self.logger.debug("Risk data saving skipped - not implemented")
            
        except Exception as e:
            self.logger.warning(f"Error saving risk data: {e}")
    
    def _create_empty_portfolio_risk(self) -> PortfolioRisk:
        """Create empty portfolio risk assessment."""
        return PortfolioRisk(
            total_value=0,
            total_risk=0,
            var_1d=0,
            var_7d=0,
            expected_shortfall=0,
            max_drawdown=0,
            sharpe_ratio=0,
            sortino_ratio=0,
            beta=1.0,
            correlation_matrix={},
            risk_metrics=[],
            position_risks=[],
            overall_level=RiskLevel.VERY_LOW,
            recommendations=["No positions to assess"],
            timestamp=datetime.now()
        )
    
    def get_current_portfolio_risk(self) -> Optional[PortfolioRisk]:
        """Get current portfolio risk assessment."""
        return self.current_portfolio_risk
    
    def get_risk_alerts(self) -> List[Dict[str, Any]]:
        """Get current risk alerts."""
        return self.risk_alerts.copy()
    
    def get_risk_history(self) -> List[Dict[str, Any]]:
        """Get risk assessment history."""
        return self.risk_history.copy()
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get risk management performance metrics."""
        return self.performance_metrics.copy()
    
    def update_risk_thresholds(self, new_thresholds: Dict[str, float]):
        """Update risk thresholds."""
        for threshold_name, value in new_thresholds.items():
            if threshold_name in self.risk_thresholds:
                self.risk_thresholds[threshold_name] = value
        
        self.logger.info(f"Updated risk thresholds: {self.risk_thresholds}")
    
    def _validate_risk_thresholds(self) -> None:
        """Validate risk threshold configuration."""
        required_thresholds = [
            'max_position_size', 'max_portfolio_risk', 'max_var_1d',
            'max_var_7d', 'max_drawdown', 'min_sharpe_ratio'
        ]
        
        for threshold in required_thresholds:
            if threshold not in self.risk_thresholds:
                self.logger.warning(f"Missing risk threshold: {threshold}")
                # Set default values
                defaults = {
                    'max_position_size': 0.1,
                    'max_portfolio_risk': 0.05,
                    'max_var_1d': 0.02,
                    'max_var_7d': 0.05,
                    'max_drawdown': 0.1,
                    'min_sharpe_ratio': 0.5
                }
                self.risk_thresholds[threshold] = defaults.get(threshold, 0.05)
    
    def _clear_caches(self) -> None:
        """Clear internal caches and temporary data."""
        try:
            # Clear market data cache
            if hasattr(self, 'market_data_cache'):
                self.market_data_cache.clear()
            
            # Clear correlation cache
            if hasattr(self, 'correlation_cache'):
                self.correlation_cache.clear()
            
            # Clear volatility cache
            if hasattr(self, 'volatility_cache'):
                self.volatility_cache.clear()
            
            # Reset performance metrics
            self.performance_metrics = {
                'total_assessments': 0,
                'avg_assessment_time': 0.0,
                'last_assessment_time': None,
                'error_count': 0,
                'cache_hit_rate': 0.0
            }
            
            self.logger.debug("Risk manager caches cleared")
            
        except Exception as e:
            self.logger.error(f"Error clearing caches: {e}")