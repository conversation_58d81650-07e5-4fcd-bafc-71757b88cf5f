#!/usr/bin/env python3
"""
Execution Manager - Central trade execution orchestrator for AI trading agent

Author: inkbytefo
Description: Manages trade execution, order routing, and execution monitoring
"""

import asyncio
import logging
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime, timedelta
import uuid
from dataclasses import dataclass
from enum import Enum
import json
import numpy as np

# Hummingbot integration imports
try:
    from .hummingbot_service import HummingbotService, TradingSignal, ExecutionResult as HBExecutionResult
    from .order_manager import OrderManager
    from ..config.hummingbot_config import HummingbotConfigManager
    HUMMINGBOT_AVAILABLE = True
except ImportError:
    HUMMINGBOT_AVAILABLE = False

# Performance monitoring import
from ..monitoring.performance_monitor import PerformanceMonitor
from ..config.settings import ExecutionSettings

# Performance optimization imports
from ..utils.performance_optimization import (
    profile, memoize, cached, BatchProcessor, MemoryOptimizer,
    AsyncPoolExecutor, CacheStrategy
)


class ExecutionStatus(Enum):
    """Execution status types."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"


class OrderType(Enum):
    """Order types."""
    MARKET = "market"
    LIMIT = "limit"
    STOP_LOSS = "stop_loss"
    TAKE_PROFIT = "take_profit"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"


class OrderSide(Enum):
    """Order sides."""
    BUY = "buy"
    SELL = "sell"


class ExecutionVenue(Enum):
    """Execution venues."""
    HUMMINGBOT = "hummingbot"
    BINANCE = "binance"
    COINBASE = "coinbase"
    KRAKEN = "kraken"
    UNISWAP = "uniswap"
    SIMULATION = "simulation"


@dataclass
class ExecutionOrder:
    """Execution order details."""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float]
    stop_price: Optional[float]
    time_in_force: str
    venue: ExecutionVenue
    status: ExecutionStatus
    filled_quantity: float
    average_fill_price: float
    remaining_quantity: float
    commission: float
    commission_asset: str
    created_time: datetime
    updated_time: datetime
    execution_time: Optional[datetime]
    client_order_id: str
    venue_order_id: Optional[str]
    error_message: Optional[str]
    metadata: Dict[str, Any]


@dataclass
class ExecutionResult:
    """Execution result summary."""
    execution_id: str
    orders: List[ExecutionOrder]
    total_executed_value: float
    total_commission: float
    execution_time: float
    success_rate: float
    slippage: float
    market_impact: float
    execution_quality: float
    venue_performance: Dict[str, float]
    recommendations: List[str]
    timestamp: datetime


@dataclass
class ExecutionRequest:
    """Execution request from decision system."""
    request_id: str
    symbol: str
    side: OrderSide
    target_quantity: float
    urgency: str  # 'low', 'medium', 'high', 'urgent'
    max_slippage: float
    time_horizon: timedelta
    execution_strategy: str
    price_limit: Optional[float]
    venue_preference: Optional[ExecutionVenue]
    split_orders: bool
    iceberg_size: Optional[float]
    metadata: Dict[str, Any]
    created_time: datetime


class ExecutionManager:
    """Central execution manager for AI trading agent."""

    def __init__(self, settings: ExecutionSettings):
        self.settings = settings
        self.logger = logging.getLogger(__name__)

        # Execution parameters
        self.execution_params = {
            'max_order_size': settings.max_order_size,
            'max_slippage': settings.max_slippage,
            'order_timeout': settings.order_timeout_seconds,
            'retry_attempts': settings.retry_attempts,
            'min_order_size': settings.min_order_size,
            'execution_delay': settings.execution_delay_seconds,
            'venue_timeout': settings.venue_timeout_seconds,
            'smart_routing': settings.smart_routing_enabled
        }
        
        # Venue configurations will be managed by HummingbotConfigManager
        # Removed self.venue_configs dictionary for centralized configuration
        
        # Active orders and execution tracking - optimized
        self.active_orders = {}
        self.execution_history = []
        self.execution_queue = asyncio.Queue(maxsize=1000)  # Limit queue size
        self.order_book = {}
        
        # Performance optimization components
        self.memory_optimizer = MemoryOptimizer()
        self.batch_processor = BatchProcessor(batch_size=5, max_wait_time=0.5)
        self.executor = AsyncPoolExecutor(max_workers=8, executor_type='thread')
        
        # Execution semaphore to limit concurrent operations
        self.execution_semaphore = asyncio.Semaphore(4)
        
        # Performance tracking - optimized with Decimal for precision
        self.execution_metrics = {
            'total_executions': 0,
            'successful_executions': 0,
            'total_volume': 0.0,
            'total_commission': 0.0,
            'average_slippage': 0.0,
            'average_execution_time': 0.0,
            'venue_performance': {},
            'last_update': None
        }
        
        # Cache for frequently accessed data
        self.market_data_cache = {}
        self.venue_status_cache = {}
        self.last_cache_update = datetime.now()
        
        # Execution strategies
        self.execution_strategies = {
            'aggressive': self._aggressive_execution,
            'conservative': self._conservative_execution,
            'twap': self._twap_execution,
            'vwap': self._vwap_execution,
            'iceberg': self._iceberg_execution,
            'smart': self._smart_execution
        }
        
        # Risk limits
        self.risk_limits = {
            'max_daily_volume': settings.max_daily_volume,
            'max_position_size': settings.max_position_size,
            'max_orders_per_minute': settings.max_orders_per_minute,
            'max_venues_per_order': settings.max_venues_per_order
        }
        
        # Current state
        self.is_running = False
        self.daily_volume = 0
        self.orders_this_minute = 0
        self.last_minute_reset = datetime.now()
        
        # Concurrency protection for portfolio operations
        self._portfolio_lock = asyncio.Lock()
        
        # Venue integrations
        self.venue_integrations = {}
        
        # Hummingbot integration
        self.hummingbot_config_manager = None
        self.hummingbot_service: Optional[HummingbotService] = None
        
        # Market data for execution
        self.market_data = {}
        self.liquidity_data = {}
        
        # Performance monitor for risk limits
        self.performance_monitor = PerformanceMonitor({})
        
    async def start(self):
        """Start the execution manager."""
        self.logger.info("Starting Execution Manager...")
        
        try:
            # Start performance monitor
            await self.performance_monitor.start()
            
            # Initialize Hummingbot integration
            await self._initialize_hummingbot()
            
            # Set ExecutionManager reference in HummingbotService for reconciliation callbacks
            if self.hummingbot_service:
                self.hummingbot_service.set_execution_manager_reference(self)
            
            # Initialize venue integrations
            await self._initialize_venues()
            
            # Start execution worker
            self.is_running = True
            asyncio.create_task(self._execution_worker())
            
            # Start monitoring tasks
            asyncio.create_task(self._monitor_orders())
            asyncio.create_task(self._update_market_data())
            asyncio.create_task(self._reset_rate_limits())
            
            # Perform initial state reconciliation
            await self.reconcile_initial_state()
            
            # Perform state reconciliation after all services are started
            await self.reconcile_state()
            
            self.logger.info("Execution Manager started")
            
        except Exception as e:
            self.logger.error(f"Error starting execution manager: {e}")
            raise
    
    async def stop(self):
        """Stop the execution manager."""
        self.logger.info("Stopping Execution Manager...")
        
        try:
            self.is_running = False
            
            # Cancel all active orders
            await self._cancel_all_orders()
            
            # Stop Hummingbot service
            if self.hummingbot_service:
                await self.hummingbot_service.stop()
            
            # Stop OrderManager
            if hasattr(self, 'order_manager') and self.order_manager:
                await self.order_manager.stop()
            
            # Close venue connections
            await self._close_venues()
            
            # Save execution data
            await self._save_execution_data()
            
            # Stop performance monitor
            await self.performance_monitor.stop()
            
            self.logger.info("Execution Manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping execution manager: {e}")
    
    @profile("trade_execution")
    async def execute_trades(self, execution_requests: List[ExecutionRequest]) -> List[ExecutionResult]:
        """Execute multiple trade requests with optimized batch processing."""
        execution_results = []
        
        try:
            self.logger.info(f"Executing {len(execution_requests)} trade requests")
            
            # Use batch processing for better performance
            async with self.execution_semaphore:
                # Process requests in batches
                batch_size = min(5, len(execution_requests))
                for i in range(0, len(execution_requests), batch_size):
                    batch = execution_requests[i:i + batch_size]
                    
                    # Process batch concurrently
                    batch_tasks = []
                    for request in batch:
                        # Validate request
                        if not await self._validate_execution_request(request):
                            continue
                        
                        # Check risk limits
                        if not await self._check_risk_limits(request):
                            continue
                        
                        # Create execution task
                        task = asyncio.create_task(self._execute_single_request(request))
                        batch_tasks.append(task)
                    
                    # Wait for batch completion
                    if batch_tasks:
                        batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
                        
                        for result in batch_results:
                            if isinstance(result, ExecutionResult):
                                execution_results.append(result)
                                # Update metrics asynchronously
                                asyncio.create_task(self._update_execution_metrics(result))
                            elif isinstance(result, Exception):
                                self.logger.error(f"Batch execution error: {result}")
                    
                    # Add delay between batches
                    if i + batch_size < len(execution_requests):
                        await asyncio.sleep(self.execution_params['execution_delay'])
            
            self.logger.info(f"Completed execution of {len(execution_results)} requests")
            
        except Exception as e:
            self.logger.error(f"Error executing trades: {e}")
        
        return execution_results
    
    async def _execute_single_request(self, request: ExecutionRequest) -> ExecutionResult:
        """Execute a single trade request."""
        execution_start = datetime.now()
        
        try:
            # Select execution strategy
            strategy_func = self.execution_strategies.get(
                request.execution_strategy, 
                self._smart_execution
            )
            
            # Execute using selected strategy
            orders = await strategy_func(request)
            
            # Calculate execution metrics
            total_executed_value = sum(order.filled_quantity * order.average_fill_price for order in orders)
            total_commission = sum(order.commission for order in orders)
            execution_time = (datetime.now() - execution_start).total_seconds()
            
            # Calculate success rate
            filled_orders = [order for order in orders if order.status == ExecutionStatus.FILLED]
            success_rate = len(filled_orders) / len(orders) if orders else 0
            
            # Calculate slippage
            slippage = await self._calculate_slippage(request, orders)
            
            # Calculate market impact
            market_impact = await self._calculate_market_impact(request, orders)
            
            # Calculate execution quality
            execution_quality = await self._calculate_execution_quality(request, orders)
            
            # Calculate venue performance
            venue_performance = await self._calculate_venue_performance(orders)
            
            # Generate recommendations
            recommendations = await self._generate_execution_recommendations(request, orders)
            
            # Create execution result
            result = ExecutionResult(
                execution_id=str(uuid.uuid4()),
                orders=orders,
                total_executed_value=total_executed_value,
                total_commission=total_commission,
                execution_time=execution_time,
                success_rate=success_rate,
                slippage=slippage,
                market_impact=market_impact,
                execution_quality=execution_quality,
                venue_performance=venue_performance,
                recommendations=recommendations,
                timestamp=datetime.now()
            )
            
            # Update execution history
            self.execution_history.append(result)
            
            # Keep only recent history
            if len(self.execution_history) > 1000:
                self.execution_history = self.execution_history[-1000:]
            
            self.logger.info(f"Execution completed: {request.symbol} {request.side.value} {request.target_quantity}")
            self.logger.info(f"Success rate: {success_rate:.1%}, Slippage: {slippage:.3%}, Time: {execution_time:.2f}s")
            
            return result
            
        except Exception as e:
            from .hummingbot_service import HummingbotAPIError
            
            if isinstance(e, HummingbotAPIError):
                # Handle Hummingbot API specific errors
                self.logger.error(f"Hummingbot API error during execution: {e}")
                
                # Mark order as failed in OrderManager if available
                if hasattr(self, 'order_manager') and self.order_manager:
                    try:
                        # Create a failed order to track
                        failed_order = ExecutionOrder(
                            order_id=str(uuid.uuid4()),
                            symbol=request.symbol,
                            side=request.side,
                            order_type=OrderType.MARKET,
                            quantity=request.target_quantity,
                            price=None,
                            stop_price=None,
                            time_in_force="IOC",
                            venue=request.venue_preference or ExecutionVenue.HUMMINGBOT,
                            status=ExecutionStatus.FAILED,
                            filled_quantity=0,
                            average_fill_price=0,
                            remaining_quantity=request.target_quantity,
                            commission=0,
                            commission_asset="",
                            created_time=datetime.now(),
                            updated_time=datetime.now(),
                            execution_time=None,
                            client_order_id=f"failed_{uuid.uuid4()}",
                            venue_order_id=None,
                            error_message=str(e),
                            metadata={"error_type": "HummingbotAPIError"}
                        )
                        # Could add to retry queue here if needed
                        self.logger.info(f"Order marked as failed due to API error: {request.symbol}")
                    except Exception as order_error:
                        self.logger.error(f"Failed to update order status: {order_error}")
                        
                recommendations = [
                    f"Hummingbot API execution failed: {str(e)}",
                    "Check Hummingbot service connection and configuration",
                    "Consider retrying the order or using alternative venue"
                ]
                
            elif isinstance(e, (KeyError, TypeError, AttributeError)):
                # Handle programming errors that shouldn't be retried
                self.logger.critical(f"Critical programming error in execution: {type(e).__name__}: {e}")
                self.logger.critical(f"Request details: {request}")
                recommendations = [
                    f"Critical programming error: {type(e).__name__}: {str(e)}",
                    "This error requires developer intervention",
                    "Do not retry - fix the underlying code issue first"
                ]
                
            elif isinstance(e, (ConnectionError, TimeoutError)):
                # Handle network/connection errors
                self.logger.warning(f"Network error during execution: {e}")
                recommendations = [
                    f"Network error: {str(e)}",
                    "Check network connectivity and venue availability",
                    "Order may be suitable for retry after connection is restored"
                ]
                
            else:
                # Handle other unexpected errors
                self.logger.error(f"Unexpected error executing single request: {type(e).__name__}: {e}")
                recommendations = [
                    f"Unexpected execution error: {type(e).__name__}: {str(e)}",
                    "Review logs for additional context"
                ]
            
            # Return failed execution result with detailed error information
            return ExecutionResult(
                execution_id=str(uuid.uuid4()),
                orders=[],
                total_executed_value=0,
                total_commission=0,
                execution_time=(datetime.now() - execution_start).total_seconds(),
                success_rate=0,
                slippage=0,
                market_impact=0,
                execution_quality=0,
                venue_performance={},
                recommendations=recommendations,
                timestamp=datetime.now()
            )
    
    async def _smart_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Smart execution strategy that adapts to market conditions."""
        orders = []
        
        try:
            # Analyze market conditions
            market_conditions = await self._analyze_market_conditions(request.symbol)
            
            # Select optimal venue
            venue = await self._select_optimal_venue(request, market_conditions)
            
            # Determine order splitting strategy
            if request.split_orders or request.target_quantity > self.execution_params['max_order_size']:
                # Split into multiple orders
                sub_orders = await self._split_order(request, market_conditions)
                
                for sub_request in sub_orders:
                    order = await self._execute_order(sub_request, venue)
                    orders.append(order)
                    
                    # Dynamic delay based on market conditions
                    delay = await self._calculate_execution_delay(market_conditions)
                    await asyncio.sleep(delay)
            else:
                # Single order execution
                order = await self._execute_order(request, venue)
                orders.append(order)
            
        except Exception as e:
            self.logger.error(f"Error in smart execution: {e}")
        
        return orders
    
    async def _aggressive_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Aggressive execution strategy for urgent trades."""
        orders = []
        
        try:
            # Use market orders for immediate execution
            order_request = ExecutionRequest(
                request_id=request.request_id,
                symbol=request.symbol,
                side=request.side,
                target_quantity=request.target_quantity,
                urgency='urgent',
                max_slippage=request.max_slippage * 2,  # Allow higher slippage
                time_horizon=timedelta(seconds=30),
                execution_strategy='aggressive',
                price_limit=None,  # No price limit
                venue_preference=request.venue_preference,
                split_orders=False,  # No splitting for speed
                iceberg_size=None,
                metadata=request.metadata,
                created_time=request.created_time
            )
            
            # Select fastest venue
            venue = await self._select_fastest_venue(request.symbol)
            
            # Execute immediately
            order = await self._execute_order(order_request, venue)
            orders.append(order)
            
        except Exception as e:
            self.logger.error(f"Error in aggressive execution: {e}")
        
        return orders
    
    async def _conservative_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Conservative execution strategy to minimize market impact."""
        orders = []
        
        try:
            # Split order into smaller pieces
            num_splits = max(3, int(request.target_quantity / 1000))  # At least 3 splits
            split_size = request.target_quantity / num_splits
            
            # Execute over extended time period
            execution_interval = request.time_horizon.total_seconds() / num_splits
            
            for i in range(num_splits):
                # Create sub-order
                sub_request = ExecutionRequest(
                    request_id=f"{request.request_id}_{i}",
                    symbol=request.symbol,
                    side=request.side,
                    target_quantity=split_size,
                    urgency='low',
                    max_slippage=request.max_slippage * 0.5,  # Lower slippage tolerance
                    time_horizon=timedelta(seconds=execution_interval),
                    execution_strategy='conservative',
                    price_limit=request.price_limit,
                    venue_preference=request.venue_preference,
                    split_orders=False,
                    iceberg_size=None,
                    metadata=request.metadata,
                    created_time=request.created_time
                )
                
                # Select venue with best liquidity
                venue = await self._select_liquidity_venue(request.symbol)
                
                # Execute with limit order
                order = await self._execute_order(sub_request, venue)
                orders.append(order)
                
                # Wait between executions
                if i < num_splits - 1:
                    await asyncio.sleep(execution_interval)
            
        except Exception as e:
            self.logger.error(f"Error in conservative execution: {e}")
        
        return orders
    
    async def _twap_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Time-Weighted Average Price execution strategy."""
        orders = []
        
        try:
            # Calculate TWAP parameters
            total_time = request.time_horizon.total_seconds()
            num_intervals = max(5, int(total_time / 60))  # At least 5 intervals, 1 minute each
            interval_duration = total_time / num_intervals
            quantity_per_interval = request.target_quantity / num_intervals
            
            self.logger.info(f"TWAP execution: {num_intervals} intervals, {quantity_per_interval:.2f} per interval")
            
            for i in range(num_intervals):
                # Create interval order
                interval_request = ExecutionRequest(
                    request_id=f"{request.request_id}_twap_{i}",
                    symbol=request.symbol,
                    side=request.side,
                    target_quantity=quantity_per_interval,
                    urgency='medium',
                    max_slippage=request.max_slippage,
                    time_horizon=timedelta(seconds=interval_duration),
                    execution_strategy='twap',
                    price_limit=request.price_limit,
                    venue_preference=request.venue_preference,
                    split_orders=False,
                    iceberg_size=None,
                    metadata=request.metadata,
                    created_time=request.created_time
                )
                
                # Select venue
                venue = await self._select_optimal_venue(interval_request, {})
                
                # Execute interval order
                order = await self._execute_order(interval_request, venue)
                orders.append(order)
                
                # Wait for next interval
                if i < num_intervals - 1:
                    await asyncio.sleep(interval_duration)
            
        except Exception as e:
            self.logger.error(f"Error in TWAP execution: {e}")
        
        return orders
    
    async def _vwap_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Volume-Weighted Average Price execution strategy."""
        orders = []
        
        try:
            # Get historical volume profile
            volume_profile = await self._get_volume_profile(request.symbol)
            
            # Calculate VWAP intervals based on volume
            total_time = request.time_horizon.total_seconds()
            intervals = await self._calculate_vwap_intervals(volume_profile, total_time)
            
            for i, interval in enumerate(intervals):
                # Create volume-weighted order
                vwap_request = ExecutionRequest(
                    request_id=f"{request.request_id}_vwap_{i}",
                    symbol=request.symbol,
                    side=request.side,
                    target_quantity=interval['quantity'],
                    urgency='medium',
                    max_slippage=request.max_slippage,
                    time_horizon=timedelta(seconds=interval['duration']),
                    execution_strategy='vwap',
                    price_limit=request.price_limit,
                    venue_preference=request.venue_preference,
                    split_orders=False,
                    iceberg_size=None,
                    metadata=request.metadata,
                    created_time=request.created_time
                )
                
                # Select venue
                venue = await self._select_optimal_venue(vwap_request, {})
                
                # Execute order
                order = await self._execute_order(vwap_request, venue)
                orders.append(order)
                
                # Wait for next interval
                if i < len(intervals) - 1:
                    await asyncio.sleep(interval['duration'])
            
        except Exception as e:
            self.logger.error(f"Error in VWAP execution: {e}")
        
        return orders
    
    async def _iceberg_execution(self, request: ExecutionRequest) -> List[ExecutionOrder]:
        """Iceberg execution strategy to hide large orders."""
        orders = []
        
        try:
            # Determine iceberg size
            iceberg_size = request.iceberg_size or (request.target_quantity * 0.1)  # 10% of total
            remaining_quantity = request.target_quantity
            
            while remaining_quantity > 0:
                # Calculate current slice size
                current_slice = min(iceberg_size, remaining_quantity)
                
                # Create iceberg slice order
                slice_request = ExecutionRequest(
                    request_id=f"{request.request_id}_iceberg_{len(orders)}",
                    symbol=request.symbol,
                    side=request.side,
                    target_quantity=current_slice,
                    urgency=request.urgency,
                    max_slippage=request.max_slippage,
                    time_horizon=timedelta(minutes=5),  # 5 minutes per slice
                    execution_strategy='iceberg',
                    price_limit=request.price_limit,
                    venue_preference=request.venue_preference,
                    split_orders=False,
                    iceberg_size=None,
                    metadata=request.metadata,
                    created_time=request.created_time
                )
                
                # Select venue
                venue = await self._select_optimal_venue(slice_request, {})
                
                # Execute slice
                order = await self._execute_order(slice_request, venue)
                orders.append(order)
                
                # Update remaining quantity
                remaining_quantity -= order.filled_quantity
                
                # Break if order wasn't filled
                if order.status != ExecutionStatus.FILLED:
                    break
                
                # Random delay to avoid detection
                delay = np.uniform(30, 120)  # 30-120 seconds
                await asyncio.sleep(delay)
            
        except Exception as e:
            self.logger.error(f"Error in iceberg execution: {e}")
        
        return orders
    
    async def _execute_order(self, request: ExecutionRequest, venue: ExecutionVenue) -> ExecutionOrder:
        """Execute a single order on specified venue."""
        try:
            # Create order
            order = ExecutionOrder(
                order_id=str(uuid.uuid4()),
                symbol=request.symbol,
                side=request.side,
                order_type=OrderType.LIMIT if request.price_limit else OrderType.MARKET,
                quantity=request.target_quantity,
                price=request.price_limit,
                stop_price=None,
                time_in_force='GTC',  # Good Till Cancelled
                venue=venue,
                status=ExecutionStatus.PENDING,
                filled_quantity=0,
                average_fill_price=0,
                remaining_quantity=request.target_quantity,
                commission=0,
                commission_asset='USDT',
                created_time=datetime.now(),
                updated_time=datetime.now(),
                execution_time=None,
                client_order_id=request.request_id,
                venue_order_id=None,
                error_message=None,
                metadata=request.metadata
            )
            
            # Submit order to venue
            if venue == ExecutionVenue.SIMULATION:
                order = await self._simulate_order_execution(order)
            elif venue == ExecutionVenue.HUMMINGBOT:
                order = await self._execute_hummingbot_order(order)
            else:
                # Other venue integrations would go here
                order.status = ExecutionStatus.REJECTED
                order.error_message = f"Venue {venue.value} not implemented"
            
            # Add to active orders if not immediately filled
            if order.status in [ExecutionStatus.SUBMITTED, ExecutionStatus.PARTIALLY_FILLED]:
                self.active_orders[order.order_id] = order
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error executing order: {e}")
            
            # Return failed order
            return ExecutionOrder(
                order_id=str(uuid.uuid4()),
                symbol=request.symbol,
                side=request.side,
                order_type=OrderType.MARKET,
                quantity=request.target_quantity,
                price=request.price_limit,
                stop_price=None,
                time_in_force='GTC',
                venue=venue,
                status=ExecutionStatus.FAILED,
                filled_quantity=0,
                average_fill_price=0,
                remaining_quantity=request.target_quantity,
                commission=0,
                commission_asset='USDT',
                created_time=datetime.now(),
                updated_time=datetime.now(),
                execution_time=None,
                client_order_id=request.request_id,
                venue_order_id=None,
                error_message=str(e),
                metadata=request.metadata
            )
    
    async def _simulate_order_execution(self, order: ExecutionOrder) -> ExecutionOrder:
        """Simulate order execution for testing."""
        try:
            # Simulate execution delay
            await asyncio.sleep(0.1)
            
            # Get current market price
            market_price = await self._get_market_price(order.symbol)
            
            if order.order_type == OrderType.MARKET:
                # Market order - immediate fill with slippage
                slippage = np.uniform(0.0005, 0.002)  # 0.05% to 0.2% slippage
                
                if order.side == OrderSide.BUY:
                    fill_price = market_price * (1 + slippage)
                else:
                    fill_price = market_price * (1 - slippage)
                
                order.status = ExecutionStatus.FILLED
                order.filled_quantity = order.quantity
                order.average_fill_price = fill_price
                order.remaining_quantity = 0
                order.execution_time = datetime.now()
                
            else:
                # Limit order - check if price is achievable
                if ((order.side == OrderSide.BUY and order.price >= market_price) or
                    (order.side == OrderSide.SELL and order.price <= market_price)):
                    
                    # Simulate partial or full fill
                    fill_probability = 0.8  # 80% chance of fill
                    
                    if np.random() < fill_probability:
                        order.status = ExecutionStatus.FILLED
                        order.filled_quantity = order.quantity
                        order.average_fill_price = order.price
                        order.remaining_quantity = 0
                        order.execution_time = datetime.now()
                    else:
                        order.status = ExecutionStatus.SUBMITTED
                        order.venue_order_id = f"SIM_{order.order_id[:8]}"
                else:
                    order.status = ExecutionStatus.SUBMITTED
                    order.venue_order_id = f"SIM_{order.order_id[:8]}"
            
            # Calculate commission
            if order.filled_quantity > 0:
                order.commission = order.filled_quantity * order.average_fill_price * 0.001  # 0.1% fee
            
            order.updated_time = datetime.now()
            
        except Exception as e:
            order.status = ExecutionStatus.FAILED
            order.error_message = str(e)
        
        return order
    
    async def _initialize_hummingbot(self):
        """Initialize Hummingbot integration with centralized configuration."""
        if not HUMMINGBOT_AVAILABLE:
            self.logger.warning("Hummingbot integration not available")
            return
            
        try:
            # Initialize Hummingbot config manager and load all configurations
            self.hummingbot_config_manager = HummingbotConfigManager()
            
            # Load all necessary configurations
            api_config = self.hummingbot_config_manager.get_api_config()
            gateway_config = self.hummingbot_config_manager.get_gateway_config()
            enabled_exchanges = self.hummingbot_config_manager.get_enabled_exchanges()
            trading_config = self.hummingbot_config_manager.get_trading_config()
            risk_config = self.hummingbot_config_manager.get_risk_config()
            
            # Validate configurations
            if not self.hummingbot_config_manager.validate_config():
                self.logger.error("Hummingbot configuration validation failed")
                return
            
            # Initialize OrderManager with centralized config
            order_config = {
                'trading': trading_config.__dict__,
                'risk': risk_config.__dict__,
                'exchanges': {ex.name: ex.__dict__ for ex in enabled_exchanges}
            }
            self.order_manager = OrderManager(order_config)
            await self.order_manager.start()
            
            # Initialize Hummingbot service with all configurations
            self.hummingbot_service = HummingbotService(
                config_manager=self.hummingbot_config_manager,
                performance_monitor=self.performance_monitor,
                risk_monitor=self.risk_monitor
            )
            self.hummingbot_service.set_order_manager(self.order_manager)
            
            # Start Hummingbot service
            await self.hummingbot_service.start()
            
            self.logger.info(f"Hummingbot integration initialized with {len(enabled_exchanges)} enabled exchanges")
            self.logger.info(f"Enabled exchanges: {[ex.name for ex in enabled_exchanges]}")
            
        except Exception as e:
            self.logger.error(f"Error initializing Hummingbot: {e}")
            self.hummingbot_service = None
            self.order_manager = None
            self.hummingbot_config_manager = None
    
    async def _execute_hummingbot_order(self, order: ExecutionOrder) -> ExecutionOrder:
        """Execute order through Hummingbot integration."""
        try:
            # Check if Hummingbot service is available and connected
            if not self.hummingbot_service or not await self._check_hummingbot_connection():
                self.logger.warning("Hummingbot service not available or disconnected, falling back to simulation")
                order = await self._simulate_order_execution(order)
                order.venue_order_id = f"HB_SIM_{order.order_id[:8]}"
                return order
            
            self.logger.info(f"Executing order via Hummingbot: {order.symbol} {order.side.value} {order.quantity}")
            
            # Create trading signal for Hummingbot
            trading_signal = TradingSignal(
                symbol=order.symbol,
                side=order.side.value.lower(),
                order_type=order.order_type.value.lower(),
                amount=order.quantity,
                price=order.price,
                timestamp=datetime.now()
            )
            
            # Execute through Hummingbot service
            hb_result = await self.hummingbot_service.execute_signal(trading_signal)
            
            # Update order with Hummingbot result
            if hb_result.success:
                order.status = ExecutionStatus.SUBMITTED if hb_result.filled_amount == 0 else ExecutionStatus.FILLED
                order.filled_quantity = hb_result.filled_amount
                order.average_fill_price = hb_result.average_price or order.price
                order.venue_order_id = hb_result.order_id
                order.execution_time = datetime.now()
                order.commission = sum(fee.get('amount', 0) for fee in (hb_result.fees or []))
                order.remaining_quantity = order.quantity - order.filled_quantity
                
                # Log successful execution
                self.logger.info(f"Hummingbot order executed successfully: {order.venue_order_id}")
            else:
                order.status = ExecutionStatus.FAILED
                order.error_message = hb_result.error_message
                self.logger.error(f"Hummingbot order failed: {hb_result.error_message}")
            
        except Exception as e:
            self.logger.error(f"Hummingbot execution failed: {e}")
            order.status = ExecutionStatus.FAILED
            order.error_message = f"Hummingbot execution failed: {str(e)}"
        
        return order
    
    # Helper methods for execution strategies
    async def _analyze_market_conditions(self, symbol: str) -> Dict[str, Any]:
        """Analyze current market conditions."""
        try:
            conditions = {
                'volatility': 'medium',
                'liquidity': 'high',
                'spread': 0.001,
                'volume': 1000000,
                'trend': 'neutral',
                'market_impact_estimate': 0.0005
            }
            
            # In a real implementation, this would analyze:
            # - Order book depth
            # - Recent volatility
            # - Trading volume
            # - Price trends
            # - Market microstructure
            
            return conditions
            
        except Exception as e:
            self.logger.warning(f"Error analyzing market conditions: {e}")
            return {'volatility': 'medium', 'liquidity': 'medium'}
    
    async def _select_optimal_venue(self, request: ExecutionRequest, 
                                  market_conditions: Dict[str, Any]) -> ExecutionVenue:
        """Select optimal execution venue using centralized configuration."""
        try:
            # Check if Hummingbot service and config manager are available
            if not self.hummingbot_service or not hasattr(self.hummingbot_service, 'config_manager'):
                self.logger.warning("Hummingbot service not available, falling back to simulation")
                return ExecutionVenue.SIMULATION
            
            # Get enabled exchanges from centralized configuration
            enabled_exchanges = self.hummingbot_service.config_manager.get_enabled_exchanges()
            trading_config = self.hummingbot_service.config_manager.get_trading_config()
            
            if not enabled_exchanges:
                self.logger.warning("No enabled exchanges found, falling back to simulation")
                return ExecutionVenue.SIMULATION
            
            # If venue preference is specified, check if it's available
            if request.venue_preference:
                for exchange in enabled_exchanges:
                    if exchange.name.upper() == request.venue_preference.value:
                        if request.symbol in exchange.trading_pairs or not exchange.trading_pairs:
                            return request.venue_preference
            
            # Score exchanges based on multiple factors
            exchange_scores = {}
            
            for exchange in enabled_exchanges:
                # Check if exchange supports the trading pair
                if exchange.trading_pairs and request.symbol not in exchange.trading_pairs:
                    continue
                
                score = 0
                
                # Base score for enabled exchange
                score += 50
                
                # Connector type preference (spot > paper > perpetual for most cases)
                if exchange.connector_type == 'spot':
                    score += 30
                elif exchange.connector_type == 'paper':
                    score += 10
                elif exchange.connector_type == 'perpetual':
                    score += 20
                
                # Rate limit score (higher is better)
                score += min(exchange.rate_limit / 100, 20)
                
                # Size capacity score based on trading config
                if request.target_quantity <= trading_config.max_order_amount:
                    score += 20
                elif request.target_quantity <= trading_config.max_position_size:
                    score += 10
                else:
                    score += 5  # Penalty for oversized orders
                
                # Sandbox mode penalty for live trading
                if exchange.sandbox_mode:
                    score -= 15
                
                # Market conditions adjustment
                if market_conditions.get('volatility') == 'high':
                    # Prefer non-paper trading in high volatility
                    if exchange.connector_type != 'paper':
                        score += 10
                
                exchange_scores[exchange.name] = score
            
            # Select exchange with highest score
            if exchange_scores:
                best_exchange_name = max(exchange_scores.keys(), key=lambda x: exchange_scores[x])
                self.logger.info(f"Selected optimal venue: {best_exchange_name} (score: {exchange_scores[best_exchange_name]})")
                
                # Map exchange name to ExecutionVenue enum
                # For now, all exchanges route through HUMMINGBOT venue
                return ExecutionVenue.HUMMINGBOT
            
            # Fallback to simulation
            self.logger.warning("No suitable exchange found, falling back to simulation")
            return ExecutionVenue.SIMULATION
            
        except Exception as e:
            self.logger.warning(f"Error selecting optimal venue: {e}")
            return ExecutionVenue.SIMULATION
    
    async def _select_fastest_venue(self, symbol: str) -> ExecutionVenue:
        """Select venue with lowest latency using centralized configuration."""
        try:
            # Check if Hummingbot service is available
            if not self.hummingbot_service or not hasattr(self.hummingbot_service, 'config_manager'):
                return ExecutionVenue.SIMULATION
            
            # Get enabled exchanges from centralized configuration
            enabled_exchanges = self.hummingbot_service.config_manager.get_enabled_exchanges()
            
            if not enabled_exchanges:
                return ExecutionVenue.SIMULATION
            
            # Find exchange with highest rate limit (proxy for speed)
            fastest_exchange = None
            highest_rate_limit = 0
            
            for exchange in enabled_exchanges:
                # Check if exchange supports the trading pair
                if exchange.trading_pairs and symbol not in exchange.trading_pairs:
                    continue
                
                # Use rate limit as proxy for speed/latency
                if exchange.rate_limit > highest_rate_limit:
                    highest_rate_limit = exchange.rate_limit
                    fastest_exchange = exchange
            
            # Return HUMMINGBOT venue if we found a suitable exchange
            if fastest_exchange:
                return ExecutionVenue.HUMMINGBOT
            
            return ExecutionVenue.SIMULATION
            
        except Exception:
            return ExecutionVenue.SIMULATION
    
    async def _select_liquidity_venue(self, symbol: str) -> ExecutionVenue:
        """Select venue with best liquidity using centralized configuration."""
        try:
            # Check if Hummingbot service is available
            if not self.hummingbot_service or not hasattr(self.hummingbot_service, 'config_manager'):
                return ExecutionVenue.SIMULATION
            
            # Get enabled exchanges from centralized configuration
            enabled_exchanges = self.hummingbot_service.config_manager.get_enabled_exchanges()
            
            if not enabled_exchanges:
                return ExecutionVenue.SIMULATION
            
            # Find exchange that supports the symbol
            for exchange in enabled_exchanges:
                # Check if exchange supports the trading pair
                if not exchange.trading_pairs or symbol in exchange.trading_pairs:
                    # Prefer spot exchanges for liquidity
                    if exchange.connector_type == 'spot':
                        return ExecutionVenue.HUMMINGBOT
            
            # If no spot exchange found, use any available exchange
            for exchange in enabled_exchanges:
                if not exchange.trading_pairs or symbol in exchange.trading_pairs:
                    return ExecutionVenue.HUMMINGBOT
            
            return ExecutionVenue.SIMULATION
            
        except Exception:
            return ExecutionVenue.SIMULATION
    
    async def _split_order(self, request: ExecutionRequest, 
                         market_conditions: Dict[str, Any]) -> List[ExecutionRequest]:
        """Split large order into smaller pieces."""
        try:
            max_order_size = self.execution_params['max_order_size']
            
            if request.target_quantity <= max_order_size:
                return [request]
            
            # Calculate number of splits
            num_splits = int(np.ceil(request.target_quantity / max_order_size))
            split_size = request.target_quantity / num_splits
            
            split_requests = []
            
            for i in range(num_splits):
                # Adjust last split for rounding
                if i == num_splits - 1:
                    current_size = request.target_quantity - (split_size * i)
                else:
                    current_size = split_size
                
                split_request = ExecutionRequest(
                    request_id=f"{request.request_id}_split_{i}",
                    symbol=request.symbol,
                    side=request.side,
                    target_quantity=current_size,
                    urgency=request.urgency,
                    max_slippage=request.max_slippage,
                    time_horizon=request.time_horizon,
                    execution_strategy=request.execution_strategy,
                    price_limit=request.price_limit,
                    venue_preference=request.venue_preference,
                    split_orders=False,  # Don't split splits
                    iceberg_size=request.iceberg_size,
                    metadata=request.metadata,
                    created_time=request.created_time
                )
                
                split_requests.append(split_request)
            
            return split_requests
            
        except Exception as e:
            self.logger.warning(f"Error splitting order: {e}")
            return [request]
    
    async def _calculate_execution_delay(self, market_conditions: Dict[str, Any]) -> float:
        """Calculate optimal delay between order executions."""
        try:
            base_delay = self.execution_params['execution_delay']
            
            # Adjust based on market conditions
            if market_conditions.get('volatility') == 'high':
                return base_delay * 0.5  # Faster execution in volatile markets
            elif market_conditions.get('liquidity') == 'low':
                return base_delay * 2.0  # Slower execution in illiquid markets
            
            return base_delay
            
        except Exception:
            return self.execution_params['execution_delay']
    
    async def _get_market_price(self, symbol: str) -> float:
        """Get current market price for symbol."""
        try:
            # In a real implementation, this would fetch from market data
            if symbol in self.market_data:
                return self.market_data[symbol]['price']
            
            # Default prices for simulation
            default_prices = {
                'BTC/USDT': 45000,
                'ETH/USDT': 3000,
                'ADA/USDT': 0.5,
                'DOT/USDT': 8.0,
                'LINK/USDT': 15.0
            }
            
            return default_prices.get(symbol, 100.0)
            
        except Exception:
            return 100.0
    
    async def _get_volume_profile(self, symbol: str) -> Dict[str, Any]:
        """Get historical volume profile for VWAP calculation."""
        try:
            # Simplified volume profile
            # In reality, this would be based on historical data
            return {
                'hourly_volumes': [100, 150, 200, 300, 250, 180, 120, 100] * 3,  # 24 hours
                'total_volume': 4000,
                'peak_hours': [9, 10, 11, 14, 15, 16],  # UTC hours
                'low_hours': [0, 1, 2, 3, 4, 5, 22, 23]
            }
            
        except Exception as e:
            self.logger.warning(f"Error getting volume profile: {e}")
            return {'hourly_volumes': [100] * 24, 'total_volume': 2400}
    
    async def _calculate_vwap_intervals(self, volume_profile: Dict[str, Any], 
                                      total_time: float) -> List[Dict[str, Any]]:
        """Calculate VWAP execution intervals."""
        try:
            intervals = []
            hourly_volumes = volume_profile['hourly_volumes']
            total_volume = sum(hourly_volumes)
            
            # Calculate intervals based on volume distribution
            num_intervals = min(8, int(total_time / 300))  # Max 8 intervals, 5 min minimum
            interval_duration = total_time / num_intervals
            
            for i in range(num_intervals):
                # Calculate volume weight for this interval
                hour_index = int((i * interval_duration) / 3600) % 24
                volume_weight = hourly_volumes[hour_index] / total_volume
                
                intervals.append({
                    'duration': interval_duration,
                    'volume_weight': volume_weight,
                    'quantity': 0  # Will be set by caller
                })
            
            return intervals
            
        except Exception as e:
            self.logger.warning(f"Error calculating VWAP intervals: {e}")
            # Fallback to equal intervals
            num_intervals = max(1, int(total_time / 300))
            interval_duration = total_time / num_intervals
            
            return [{
                'duration': interval_duration,
                'volume_weight': 1.0 / num_intervals,
                'quantity': 0
            } for _ in range(num_intervals)]
    
    # Validation and risk management methods
    async def _validate_execution_request(self, request: ExecutionRequest) -> bool:
        """Validate execution request."""
        try:
            # Check minimum order size
            if request.target_quantity < self.execution_params['min_order_size']:
                self.logger.warning(f"Order size too small: {request.target_quantity}")
                return False
            
            # Check symbol format
            if '/' not in request.symbol:
                self.logger.warning(f"Invalid symbol format: {request.symbol}")
                return False
            
            # Check slippage limits
            if request.max_slippage > self.execution_params['max_slippage']:
                self.logger.warning(f"Slippage too high: {request.max_slippage}")
                return False
            
            # Check time horizon
            if request.time_horizon.total_seconds() < 1:
                self.logger.warning(f"Time horizon too short: {request.time_horizon}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error validating execution request: {e}")
            return False
    
    async def _check_risk_limits(self, request: ExecutionRequest) -> bool:
        """Check risk limits before execution."""
        try:
            # Check daily volume limit
            estimated_value = request.target_quantity * await self._get_market_price(request.symbol)
            
            async with self._portfolio_lock:
                if self.daily_volume + estimated_value > self.risk_limits['max_daily_volume']:
                    self.logger.warning(f"Daily volume limit exceeded: {self.daily_volume + estimated_value}")
                    return False
                
                # Check position size limit
                if estimated_value > self.risk_limits['max_position_size']:
                    self.logger.warning(f"Position size limit exceeded: {estimated_value}")
                    return False
                
                # Check rate limits
                if self.orders_this_minute >= self.risk_limits['max_orders_per_minute']:
                    self.logger.warning(f"Rate limit exceeded: {self.orders_this_minute} orders this minute")
                    return False
            
            # Check performance-based risk limits from PerformanceMonitor
            try:
                performance_metrics = await self.performance_monitor.get_current_metrics()
                
                # Check max daily loss limit
                daily_pnl = performance_metrics.get('daily_pnl', 0)
                max_daily_loss = performance_metrics.get('max_daily_loss', float('inf'))
                if daily_pnl < -abs(max_daily_loss):
                    self.logger.warning(f"Max daily loss limit exceeded: {daily_pnl} < -{max_daily_loss}")
                    return False
                
                # Check max drawdown limit
                current_drawdown = performance_metrics.get('current_drawdown', 0)
                max_drawdown = performance_metrics.get('max_drawdown', float('inf'))
                if current_drawdown > max_drawdown:
                    self.logger.warning(f"Max drawdown limit exceeded: {current_drawdown} > {max_drawdown}")
                    return False
                    
            except Exception as perf_error:
                self.logger.warning(f"Error checking performance-based risk limits: {perf_error}")
                # Continue with execution if performance monitoring fails
            
            return True
            
        except Exception as e:
            self.logger.warning(f"Error checking risk limits: {e}")
            return False
    
    # Calculation methods
    async def _calculate_slippage(self, request: ExecutionRequest, 
                                orders: List[ExecutionOrder]) -> float:
        """Calculate execution slippage."""
        try:
            if not orders:
                return 0
            
            # Get reference price (market price at time of request)
            reference_price = await self._get_market_price(request.symbol)
            
            # Calculate weighted average execution price
            total_quantity = sum(order.filled_quantity for order in orders)
            
            if total_quantity == 0:
                return 0
            
            weighted_price = sum(order.filled_quantity * order.average_fill_price 
                               for order in orders) / total_quantity
            
            # Calculate slippage
            if request.side == OrderSide.BUY:
                slippage = (weighted_price - reference_price) / reference_price
            else:
                slippage = (reference_price - weighted_price) / reference_price
            
            return max(0, slippage)  # Only positive slippage
            
        except Exception as e:
            self.logger.warning(f"Error calculating slippage: {e}")
            return 0
    
    async def _calculate_market_impact(self, request: ExecutionRequest, 
                                     orders: List[ExecutionOrder]) -> float:
        """Calculate market impact of execution."""
        try:
            # Simplified market impact calculation
            # In reality, this would consider order book depth, volume, etc.
            
            total_value = sum(order.filled_quantity * order.average_fill_price for order in orders)
            daily_volume = await self._get_daily_volume(request.symbol)
            
            if daily_volume == 0:
                return 0
            
            # Market impact as percentage of daily volume
            impact = total_value / daily_volume
            
            # Apply square root law for market impact
            return np.sqrt(impact) * 0.1  # 10% coefficient
            
        except Exception as e:
            self.logger.warning(f"Error calculating market impact: {e}")
            return 0
    
    async def _calculate_execution_quality(self, request: ExecutionRequest, 
                                         orders: List[ExecutionOrder]) -> float:
        """Calculate overall execution quality score."""
        try:
            if not orders:
                return 0
            
            score = 0
            
            # Fill rate score (0-30 points)
            filled_orders = [order for order in orders if order.status == ExecutionStatus.FILLED]
            fill_rate = len(filled_orders) / len(orders)
            score += fill_rate * 30
            
            # Slippage score (0-25 points)
            slippage = await self._calculate_slippage(request, orders)
            slippage_score = max(0, 25 - (slippage * 5000))  # Penalty for high slippage
            score += slippage_score
            
            # Speed score (0-25 points)
            avg_execution_time = np.mean([order.execution_time.timestamp() - order.created_time.timestamp() 
                                        for order in filled_orders if order.execution_time])
            speed_score = max(0, 25 - avg_execution_time)  # Penalty for slow execution
            score += speed_score
            
            # Cost score (0-20 points)
            total_commission = sum(order.commission for order in orders)
            total_value = sum(order.filled_quantity * order.average_fill_price for order in orders)
            
            if total_value > 0:
                commission_rate = total_commission / total_value
                cost_score = max(0, 20 - (commission_rate * 2000))  # Penalty for high fees
                score += cost_score
            
            return min(100, score)  # Cap at 100
            
        except Exception as e:
            self.logger.warning(f"Error calculating execution quality: {e}")
            return 50  # Default medium score
    
    async def _calculate_venue_performance(self, orders: List[ExecutionOrder]) -> Dict[str, float]:
        """Calculate performance by venue."""
        try:
            venue_performance = {}
            
            # Group orders by venue
            venue_orders = {}
            for order in orders:
                if order.venue not in venue_orders:
                    venue_orders[order.venue] = []
                venue_orders[order.venue].append(order)
            
            # Calculate performance for each venue
            for venue, venue_order_list in venue_orders.items():
                filled_orders = [order for order in venue_order_list if order.status == ExecutionStatus.FILLED]
                
                if venue_order_list:
                    fill_rate = len(filled_orders) / len(venue_order_list)
                    
                    if filled_orders:
                        avg_execution_time = np.mean([
                            (order.execution_time.timestamp() - order.created_time.timestamp()) 
                            for order in filled_orders if order.execution_time
                        ])
                        
                        total_commission = sum(order.commission for order in filled_orders)
                        total_value = sum(order.filled_quantity * order.average_fill_price for order in filled_orders)
                        commission_rate = total_commission / total_value if total_value > 0 else 0
                        
                        # Combined performance score
                        performance = (fill_rate * 50 + 
                                     max(0, 25 - avg_execution_time) + 
                                     max(0, 25 - commission_rate * 2500))
                    else:
                        performance = 0
                    
                    venue_performance[venue.value] = performance
            
            return venue_performance
            
        except Exception as e:
            self.logger.warning(f"Error calculating venue performance: {e}")
            return {}
    
    async def _generate_execution_recommendations(self, request: ExecutionRequest, 
                                                orders: List[ExecutionOrder]) -> List[str]:
        """Generate execution recommendations."""
        recommendations = []
        
        try:
            # Analyze execution results
            filled_orders = [order for order in orders if order.status == ExecutionStatus.FILLED]
            fill_rate = len(filled_orders) / len(orders) if orders else 0
            
            # Fill rate recommendations
            if fill_rate < 0.8:
                recommendations.append("Consider using more aggressive execution strategy")
                recommendations.append("Review price limits and market conditions")
            
            # Slippage recommendations
            slippage = await self._calculate_slippage(request, orders)
            if slippage > request.max_slippage:
                recommendations.append("Slippage exceeded target - consider smaller order sizes")
                recommendations.append("Use limit orders or iceberg strategy for large orders")
            
            # Timing recommendations
            if filled_orders:
                avg_execution_time = np.mean([
                    (order.execution_time.timestamp() - order.created_time.timestamp()) 
                    for order in filled_orders if order.execution_time
                ])
                
                if avg_execution_time > 60:  # More than 1 minute
                    recommendations.append("Execution time was slow - consider market orders for urgency")
            
            # Venue recommendations
            venue_performance = await self._calculate_venue_performance(orders)
            if venue_performance:
                best_venue = max(venue_performance.keys(), key=lambda x: venue_performance[x])
                recommendations.append(f"Best performing venue: {best_venue}")
            
            # Strategy recommendations
            if request.execution_strategy == 'aggressive' and slippage > 0.002:
                recommendations.append("Consider TWAP or VWAP strategy for better price execution")
            
            if not recommendations:
                recommendations.append("Execution completed successfully")
            
        except Exception as e:
            self.logger.warning(f"Error generating recommendations: {e}")
            recommendations = ["Review execution parameters"]
        
        return recommendations
    
    async def _get_daily_volume(self, symbol: str) -> float:
        """Get daily trading volume for symbol."""
        try:
            # In a real implementation, this would fetch from market data
            default_volumes = {
                'BTC/USDT': 1000000000,  # $1B
                'ETH/USDT': 500000000,   # $500M
                'ADA/USDT': 100000000,   # $100M
                'DOT/USDT': 50000000,    # $50M
                'LINK/USDT': 30000000    # $30M
            }
            
            return default_volumes.get(symbol, 10000000)  # $10M default
            
        except Exception:
            return 10000000
    
    # Background tasks
    async def _execution_worker(self):
        """Background worker for processing execution queue."""
        while self.is_running:
            try:
                # Process execution queue
                if not self.execution_queue.empty():
                    request = await self.execution_queue.get()
                    await self._execute_single_request(request)
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error in execution worker: {e}")
                await asyncio.sleep(1)
    
    async def _monitor_orders(self):
        """Monitor active orders for updates."""
        while self.is_running:
            try:
                # Check active orders for updates
                orders_to_remove = []
                
                for order_id, order in self.active_orders.items():
                    # Check if order has expired
                    if (datetime.now() - order.created_time).total_seconds() > self.execution_params['order_timeout']:
                        order.status = ExecutionStatus.EXPIRED
                        orders_to_remove.append(order_id)
                        continue
                    
                    # Update order status (in real implementation, this would query the venue)
                    if order.status == ExecutionStatus.SUBMITTED:
                        # Simulate order updates
                        if np.random() < 0.1:  # 10% chance of update
                            if np.random() < 0.8:  # 80% chance of fill
                                order.status = ExecutionStatus.FILLED
                                order.filled_quantity = order.quantity
                                order.average_fill_price = order.price or await self._get_market_price(order.symbol)
                                order.remaining_quantity = 0
                                order.execution_time = datetime.now()
                                order.commission = order.filled_quantity * order.average_fill_price * 0.001
                                orders_to_remove.append(order_id)
                
                # Remove completed orders
                for order_id in orders_to_remove:
                    del self.active_orders[order_id]
                
                await asyncio.sleep(5)  # Check every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring orders: {e}")
                await asyncio.sleep(10)
    
    async def _update_market_data(self):
        """Update market data for execution."""
        while self.is_running:
            try:
                # Update market data (in real implementation, this would fetch from data sources)
                for symbol in ['BTC/USDT', 'ETH/USDT', 'ADA/USDT', 'DOT/USDT', 'LINK/USDT']:
                    if symbol not in self.market_data:
                        self.market_data[symbol] = {}
                    
                    # Simulate price updates
                    current_price = await self._get_market_price(symbol)
                    price_change = np.normal(0, current_price * 0.001)  # 0.1% volatility
                    new_price = max(0.01, current_price + price_change)
                    
                    self.market_data[symbol].update({
                        'price': new_price,
                        'bid': new_price * 0.999,
                        'ask': new_price * 1.001,
                        'volume': np.uniform(100000, 1000000),
                        'timestamp': datetime.now()
                    })
                
                await asyncio.sleep(1)  # Update every second
                
            except Exception as e:
                self.logger.error(f"Error updating market data: {e}")
                await asyncio.sleep(5)
    
    async def _reset_rate_limits(self):
        """Reset rate limits periodically."""
        while self.is_running:
            try:
                current_time = datetime.now()
                
                async with self._portfolio_lock:
                    # Reset minute counter
                    if (current_time - self.last_minute_reset).total_seconds() >= 60:
                        self.orders_this_minute = 0
                        self.last_minute_reset = current_time
                    
                    # Reset daily volume at midnight
                    if current_time.hour == 0 and current_time.minute == 0:
                        self.daily_volume = 0
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error resetting rate limits: {e}")
                await asyncio.sleep(60)
    
    # Venue management
    async def _initialize_venues(self):
        """Initialize venue integrations."""
        try:
            for venue, config in self.venue_configs.items():
                if config['enabled']:
                    if venue == ExecutionVenue.HUMMINGBOT:
                        # Initialize Hummingbot integration
                        # This would set up the actual connection
                        self.logger.info("Hummingbot integration initialized")
                    elif venue == ExecutionVenue.SIMULATION:
                        # Simulation venue is always ready
                        self.logger.info("Simulation venue initialized")
                    
                    self.venue_integrations[venue] = {'status': 'connected', 'last_ping': datetime.now()}
            
        except Exception as e:
            self.logger.error(f"Error initializing venues: {e}")
    
    async def _close_venues(self):
        """Close venue connections."""
        try:
            for venue in self.venue_integrations:
                # Close connections
                self.logger.info(f"Closing {venue.value} connection")
            
            self.venue_integrations.clear()
            
        except Exception as e:
            self.logger.error(f"Error closing venues: {e}")
    
    async def _cancel_all_orders(self):
        """Cancel all active orders."""
        try:
            for order_id, order in self.active_orders.items():
                order.status = ExecutionStatus.CANCELLED
                order.updated_time = datetime.now()
                self.logger.info(f"Cancelled order {order_id}")
            
            self.active_orders.clear()
            
        except Exception as e:
            self.logger.error(f"Error cancelling orders: {e}")
    
    async def _update_execution_metrics(self, result: ExecutionResult):
        """Update execution performance metrics."""
        try:
            async with self._portfolio_lock:
                self.execution_metrics['total_executions'] += 1
                
                if result.success_rate > 0.8:
                    self.execution_metrics['successful_executions'] += 1
                
                self.execution_metrics['total_volume'] += result.total_executed_value
                self.execution_metrics['total_commission'] += result.total_commission
                
                # Update running averages
                total_execs = self.execution_metrics['total_executions']
                
                # Update average slippage
                current_avg_slippage = self.execution_metrics['average_slippage']
                self.execution_metrics['average_slippage'] = (
                    (current_avg_slippage * (total_execs - 1) + result.slippage) / total_execs
                )
                
                # Update average execution time
                current_avg_time = self.execution_metrics['average_execution_time']
                self.execution_metrics['average_execution_time'] = (
                    (current_avg_time * (total_execs - 1) + result.execution_time) / total_execs
                )
                
                # Update venue performance
                for venue, performance in result.venue_performance.items():
                    if venue not in self.execution_metrics['venue_performance']:
                        self.execution_metrics['venue_performance'][venue] = []
                    
                    self.execution_metrics['venue_performance'][venue].append(performance)
                    
                    # Keep only recent performance data
                    if len(self.execution_metrics['venue_performance'][venue]) > 100:
                        self.execution_metrics['venue_performance'][venue] = \
                            self.execution_metrics['venue_performance'][venue][-100:]
                
                self.execution_metrics['last_update'] = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error updating execution metrics: {e}")
    
    async def _save_execution_data(self):
        """Save execution data and metrics."""
        try:
            # In a real implementation, this would save to database
            execution_data = {
                'metrics': self.execution_metrics,
                'recent_executions': self.execution_history[-100:],  # Last 100 executions
                'active_orders': len(self.active_orders),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"Saved execution data: {len(self.execution_history)} executions")
            
        except Exception as e:
            self.logger.error(f"Error saving execution data: {e}")
    
    # Public interface methods
    async def get_execution_status(self) -> Dict[str, Any]:
        """Get current execution manager status."""
        try:
            return {
                'is_running': self.is_running,
                'active_orders': len(self.active_orders),
                'execution_queue_size': self.execution_queue.qsize(),
                'daily_volume': self.daily_volume,
                'orders_this_minute': self.orders_this_minute,
                'venue_status': {venue.value: info['status'] 
                               for venue, info in self.venue_integrations.items()},
                'metrics': self.execution_metrics,
                'last_update': datetime.now()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting execution status: {e}")
            return {'error': str(e)}
    
    async def get_active_orders(self) -> List[Dict[str, Any]]:
        """Get list of active orders."""
        try:
            return [
                {
                    'order_id': order.order_id,
                    'symbol': order.symbol,
                    'side': order.side.value,
                    'quantity': order.quantity,
                    'filled_quantity': order.filled_quantity,
                    'status': order.status.value,
                    'venue': order.venue.value,
                    'created_time': order.created_time.isoformat(),
                    'updated_time': order.updated_time.isoformat()
                }
                for order in self.active_orders.values()
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting active orders: {e}")
            return []
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel a specific order."""
        try:
            if order_id in self.active_orders:
                order = self.active_orders[order_id]
                order.status = ExecutionStatus.CANCELLED
                order.updated_time = datetime.now()
                
                # In real implementation, would send cancel request to venue
                self.logger.info(f"Cancelled order {order_id}")
                
                del self.active_orders[order_id]
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    async def get_execution_history(self, limit: int = 50) -> List[Dict[str, Any]]:
        """Get execution history."""
        try:
            recent_executions = self.execution_history[-limit:]
            
            return [
                {
                    'execution_id': result.execution_id,
                    'orders_count': len(result.orders),
                    'total_value': result.total_executed_value,
                    'success_rate': result.success_rate,
                    'slippage': result.slippage,
                    'execution_time': result.execution_time,
                    'execution_quality': result.execution_quality,
                    'timestamp': result.timestamp.isoformat()
                }
                for result in recent_executions
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting execution history: {e}")
            return []
    
    async def update_venue_config(self, venue: ExecutionVenue, config: Dict[str, Any]):
        """Update venue configuration through HummingbotConfigManager."""
        try:
            if self.hummingbot_service and self.hummingbot_service.config_manager:
                # Update configuration through centralized config manager
                # This method would need to be implemented in HummingbotConfigManager
                # For now, log the update request
                self.logger.info(f"Configuration update requested for {venue.value}: {config}")
                self.logger.warning("Direct venue config updates should be done through HummingbotConfigManager")
            else:
                self.logger.warning("Cannot update venue config: HummingbotService not available")
            
        except Exception as e:
            self.logger.error(f"Error updating venue config: {e}")
    
    async def get_performance_summary(self) -> Dict[str, Any]:
        """Get execution performance summary."""
        try:
            metrics = self.execution_metrics
            
            # Calculate venue averages
            venue_averages = {}
            for venue, performances in metrics['venue_performance'].items():
                if performances:
                    venue_averages[venue] = {
                        'average_performance': np.mean(performances),
                        'best_performance': max(performances),
                        'worst_performance': min(performances),
                        'execution_count': len(performances)
                    }
            
            return {
                'total_executions': metrics['total_executions'],
                'success_rate': (metrics['successful_executions'] / 
                               max(1, metrics['total_executions'])),
                'total_volume': metrics['total_volume'],
                'total_commission': metrics['total_commission'],
                'average_slippage': metrics['average_slippage'],
                'average_execution_time': metrics['average_execution_time'],
                'venue_performance': venue_averages,
                'commission_rate': (metrics['total_commission'] / 
                                  max(1, metrics['total_volume'])),
                'last_update': metrics['last_update'].isoformat() if metrics['last_update'] else None
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance summary: {e}")
            return {}
    
    async def queue_execution(self, request: ExecutionRequest):
        """Queue an execution request for background processing."""
        try:
            await self.execution_queue.put(request)
            self.logger.info(f"Queued execution request: {request.symbol} {request.side.value}")
            
        except Exception as e:
            self.logger.error(f"Error queuing execution: {e}")
    
    async def get_portfolio_state(self) -> Dict[str, Any]:
        """Get current portfolio state directly from Hummingbot service.
        
        This method ensures portfolio state integrity by fetching real-time data
        directly from the execution venue without using cached or stale data.
        """
        async with self._portfolio_lock:
            try:
                if self.hummingbot_service and await self._check_hummingbot_connection():
                    # Directly call get_portfolio_snapshot to fetch latest real-time data
                    portfolio_snapshot = await self.hummingbot_service.get_portfolio_snapshot()
                    
                    # Ensure timestamp is current to validate data freshness
                    if portfolio_snapshot and 'timestamp' not in portfolio_snapshot:
                        portfolio_snapshot['timestamp'] = datetime.now().isoformat()
                    
                    self.logger.debug(f"Portfolio state fetched successfully at {portfolio_snapshot.get('timestamp')}")
                    return portfolio_snapshot
                else:
                    # Return empty portfolio state if Hummingbot not available
                    self.logger.warning("Hummingbot service not available, returning empty portfolio state")
                    return {
                        'balances': {},
                        'positions': {},
                        'total_value': 0.0,
                        'available_balance': 0.0,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'disconnected'
                    }
            except Exception as e:
                self.logger.error(f"Error getting portfolio state: {e}")
                return {
                    'balances': {},
                    'positions': {},
                    'total_value': 0.0,
                    'available_balance': 0.0,
                    'timestamp': datetime.now().isoformat(),
                    'error': str(e),
                    'status': 'error'
                }
    
    async def _check_hummingbot_connection(self) -> bool:
        """Check if Hummingbot service is connected and available."""
        try:
            if not self.hummingbot_service:
                return False
            
            # Check connection status
            status = await self.hummingbot_service.get_status()
            return status.get('is_connected', False)
            
        except Exception as e:
            self.logger.warning(f"Hummingbot connection check failed: {e}")
            return False
    
    async def reconcile_state(self):
        """Perform state reconciliation with venues.
        
        This is the main orchestration method that synchronizes the local state 
        with the actual state on exchanges by reconciling orders and updating 
        portfolio information.
        """
        try:
            self.logger.info("Starting state reconciliation...")
            
            if not self.hummingbot_service:
                self.logger.warning("Hummingbot service not available for reconciliation")
                return
            
            # Check if Hummingbot is connected
            if not await self._check_hummingbot_connection():
                self.logger.warning("Hummingbot not connected, skipping reconciliation")
                return
            
            # Step 1: Get venue open orders and balances
            self.logger.debug("Fetching venue open orders and balances...")
            venue_open_orders = await self.hummingbot_service.get_venue_open_orders()
            venue_balances = await self.hummingbot_service.get_venue_balances()
            
            self.logger.info(f"Found {len(venue_open_orders)} open orders and {len(venue_balances)} assets on venues")
            
            # Step 2: Reconcile orders through OrderManager
            if hasattr(self, 'order_manager') and self.order_manager:
                await self.order_manager.reconcile_orders(venue_open_orders)
                self.logger.info("Order reconciliation completed")
            else:
                self.logger.warning("OrderManager not available for order reconciliation")
            
            # Step 3: Update ExecutionManager's portfolio state with venue balances
            await self._update_portfolio_with_venue_balances(venue_balances)
            
            self.logger.info("State reconciliation completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error during state reconciliation: {e}")
            # Don't raise the exception to prevent operation failure
            # Reconciliation failure should not stop the execution manager
    
    async def _update_portfolio_with_venue_balances(self, venue_balances: Dict[str, float]):
        """Update ExecutionManager's portfolio state with venue balances.
        
        Args:
            venue_balances: Dictionary with asset symbols as keys and total balances as values
        """
        try:
            async with self._portfolio_lock:
                # Initialize portfolio_state if it doesn't exist
                if not hasattr(self, 'portfolio_state'):
                    self.portfolio_state = {}
                
                # Compare current portfolio state with venue balances
                differences_found = False
                
                for asset, venue_balance in venue_balances.items():
                    current_balance = self.portfolio_state.get(asset, 0.0)
                    
                    # Check for significant differences (more than 0.1% or 0.001 units)
                    balance_diff = abs(venue_balance - current_balance)
                    relative_diff = balance_diff / max(venue_balance, current_balance, 0.001)
                    
                    if balance_diff > 0.001 and relative_diff > 0.001:  # 0.1% threshold
                        self.logger.warning(
                            f"Balance difference detected for {asset}: "
                            f"Local: {current_balance:.6f}, Venue: {venue_balance:.6f}, "
                            f"Diff: {balance_diff:.6f} ({relative_diff:.2%})"
                        )
                        differences_found = True
                    
                    # Update portfolio state with venue balance
                    self.portfolio_state[asset] = venue_balance
                
                # Log assets that exist locally but not on venues
                for asset, local_balance in self.portfolio_state.items():
                    if asset not in venue_balances and local_balance > 0.001:
                        self.logger.warning(
                            f"Asset {asset} exists locally ({local_balance:.6f}) but not found on venues"
                        )
                        differences_found = True
                        # Set to zero since it's not on venues
                        self.portfolio_state[asset] = 0.0
                
                if differences_found:
                    self.logger.info("Portfolio state updated due to venue balance differences")
                else:
                    self.logger.debug("Portfolio state is in sync with venue balances")
                
        except Exception as e:
            self.logger.error(f"Error updating portfolio with venue balances: {e}")

    async def reconcile_initial_state(self):
        """Perform initial state reconciliation with venues.
        
        This method synchronizes the local state with the actual state on exchanges
        by reconciling orders and updating portfolio information.
        """
        try:
            self.logger.info("Starting initial state reconciliation...")
            
            # Use the main reconcile_state method for initial reconciliation
            await self.reconcile_state()
            
            self.logger.info("Initial state reconciliation completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error during initial state reconciliation: {e}")
            # Don't raise the exception to prevent startup failure
            # Reconciliation failure should not stop the execution manager


import math
import random