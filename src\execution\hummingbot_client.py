"""Hummingbot API Client Module.

This module provides a comprehensive client for interacting with Hummingbot Backend API,
including bot management, strategy execution, and real-time monitoring.

Author: inkbytefo
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum

import aiohttp
import paho.mqtt.client as mqtt
from ..config.hummingbot_config import HummingbotConfigManager
from ..utils.logger import get_logger


class BotStatus(Enum):
    """Bot status enumeration."""
    RUNNING = "RUNNING"
    STOPPED = "STOPPED"
    ERROR = "ERROR"
    STARTING = "STARTING"
    STOPPING = "STOPPING"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "BUY"
    SELL = "SELL"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    TAKE_PROFIT = "TAKE_PROFIT"


@dataclass
class BotConfig:
    """Bot configuration data class."""
    id: str
    name: str
    strategy: str
    exchange: str
    trading_pair: str
    base_asset: str
    quote_asset: str
    parameters: Dict[str, Any]
    status: BotStatus = BotStatus.STOPPED
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None


@dataclass
class OrderData:
    """Order data class."""
    id: str
    bot_id: str
    exchange: str
    trading_pair: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float] = None
    status: str = "PENDING"
    created_at: Optional[datetime] = None
    filled_amount: float = 0.0
    average_price: Optional[float] = None


@dataclass
class PerformanceMetrics:
    """Performance metrics data class."""
    bot_id: str
    total_pnl: float
    total_trades: int
    win_rate: float
    sharpe_ratio: float
    max_drawdown: float
    current_balance: Dict[str, float]
    start_time: datetime
    end_time: datetime


class HummingbotAPIError(Exception):
    """Custom exception for Hummingbot API errors."""
    
    def __init__(self, message: str, status_code: int = None, response_text: str = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


class HummingbotAPIClient:
    """Comprehensive client for Hummingbot Backend API.
    
    This client provides methods for bot management, strategy execution,
    portfolio monitoring, and real-time data streaming.
    """
    
    def __init__(self, 
                 api_host: str = "localhost",
                 api_port: int = 8000,
                 mqtt_host: str = "localhost",
                 mqtt_port: int = 1883,
                 username: str = None,
                 password: str = None):
        """Initialize Hummingbot API client.
        
        Args:
            api_host: Hummingbot API server host
            api_port: Hummingbot API server port
            mqtt_host: MQTT broker host for real-time updates
            mqtt_port: MQTT broker port
            username: API authentication username
            password: API authentication password
        """
        self.api_host = api_host
        self.api_port = api_port
        self.base_url = f"http://{api_host}:{api_port}"
        
        self.mqtt_host = mqtt_host
        self.mqtt_port = mqtt_port
        self.username = username
        self.password = password
        
        self.logger = get_logger(self.__class__.__name__)
        
        # Authentication token
        self.auth_token: Optional[str] = None
        self.token_expires_at: Optional[datetime] = None
        
        # Rate limiting
        self.rate_limiter = asyncio.Semaphore(20)
        self.last_request_time = 0
        self.min_request_interval = 0.05  # 50ms between requests
        
        # MQTT client for real-time updates
        self.mqtt_client: Optional[mqtt.Client] = None
        self.mqtt_callbacks: Dict[str, Callable] = {}
        
        # Session management
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
    
    async def connect(self):
        """Establish connections to API and MQTT broker."""
        # Create HTTP session
        timeout = aiohttp.ClientTimeout(total=30)
        self.session = aiohttp.ClientSession(timeout=timeout)
        
        # Authenticate
        if self.username and self.password:
            await self.authenticate()
        
        # Connect to MQTT broker
        await self.connect_mqtt()
        
        self.logger.info("Connected to Hummingbot API and MQTT broker")
    
    async def disconnect(self):
        """Close all connections."""
        if self.session:
            await self.session.close()
        
        if self.mqtt_client:
            self.mqtt_client.disconnect()
        
        self.logger.info("Disconnected from Hummingbot services")
    
    async def authenticate(self) -> str:
        """Authenticate with Hummingbot API.
        
        Returns:
            Authentication token
        """
        try:
            auth_data = {
                "username": self.username,
                "password": self.password
            }
            
            response = await self._make_request(
                "POST", "/auth/login", 
                data=auth_data
            )
            
            self.auth_token = response["access_token"]
            # Assume token expires in 1 hour if not specified
            expires_in = response.get("expires_in", 3600)
            self.token_expires_at = datetime.now() + timedelta(seconds=expires_in)
            
            self.logger.info("Successfully authenticated with Hummingbot API")
            return self.auth_token
            
        except Exception as e:
            self.logger.error(f"Authentication failed: {e}")
            raise HummingbotAPIError(f"Authentication failed: {e}")
    
    async def _ensure_authenticated(self):
        """Ensure we have a valid authentication token."""
        if not self.auth_token or (
            self.token_expires_at and 
            datetime.now() >= self.token_expires_at - timedelta(minutes=5)
        ):
            await self.authenticate()
    
    async def _make_request(self, 
                          method: str, 
                          endpoint: str,
                          data: Optional[Dict[str, Any]] = None,
                          params: Optional[Dict[str, Any]] = None,
                          require_auth: bool = True) -> Dict[str, Any]:
        """Make HTTP request to Hummingbot API.
        
        Args:
            method: HTTP method
            endpoint: API endpoint
            data: Request body data
            params: Query parameters
            require_auth: Whether authentication is required
            
        Returns:
            API response data
        """
        async with self.rate_limiter:
            # Rate limiting
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                await asyncio.sleep(self.min_request_interval - time_since_last)
            
            self.last_request_time = time.time()
            
            # Ensure authentication if required
            if require_auth:
                await self._ensure_authenticated()
            
            url = f"{self.base_url}{endpoint}"
            
            headers = {
                "Content-Type": "application/json",
                "User-Agent": "HummingbotAPIClient/1.0"
            }
            
            if self.auth_token and require_auth:
                headers["Authorization"] = f"Bearer {self.auth_token}"
            
            try:
                request_kwargs = {
                    "headers": headers,
                    "params": params
                }
                
                if data:
                    request_kwargs["json"] = data
                
                self.logger.debug(f"Making {method} request to {url}")
                
                async with self.session.request(method, url, **request_kwargs) as response:
                    response_text = await response.text()
                    
                    if response.status == 200:
                        return json.loads(response_text) if response_text else {}
                    elif response.status == 401:
                        # Token expired, try to re-authenticate
                        self.auth_token = None
                        if require_auth:
                            await self.authenticate()
                            # Retry the request
                            headers["Authorization"] = f"Bearer {self.auth_token}"
                            async with self.session.request(method, url, **request_kwargs) as retry_response:
                                retry_text = await retry_response.text()
                                if retry_response.status == 200:
                                    return json.loads(retry_text) if retry_text else {}
                                else:
                                    raise HummingbotAPIError(f"API error {retry_response.status}: {retry_text}", retry_response.status, retry_text)
                    else:
                        self.logger.error(f"API error {response.status}: {response_text}")
                        raise HummingbotAPIError(f"API error {response.status}: {response_text}", response.status, response_text)
                        
            except aiohttp.ClientError as e:
                self.logger.error(f"Request failed for {method} {url}: {e}")
                raise HummingbotAPIError(f"Request failed: {e}")
    
    # Bot Management Methods
    
    async def create_bot(self, bot_config: BotConfig) -> str:
        """Create a new trading bot.
        
        Args:
            bot_config: Bot configuration
            
        Returns:
            Bot ID
        """
        try:
            config_dict = asdict(bot_config)
            # Remove fields that shouldn't be sent to API
            config_dict.pop('id', None)
            config_dict.pop('created_at', None)
            config_dict.pop('updated_at', None)
            config_dict['status'] = config_dict['status'].value if isinstance(config_dict['status'], BotStatus) else config_dict['status']
            
            response = await self._make_request("POST", "/bots", data=config_dict)
            bot_id = response["bot_id"]
            
            self.logger.info(f"Created bot {bot_id} with strategy {bot_config.strategy}")
            return bot_id
            
        except Exception as e:
            self.logger.error(f"Failed to create bot: {e}")
            raise HummingbotAPIError(f"Failed to create bot: {e}")
    
    async def start_bot(self, bot_id: str) -> bool:
        """Start a trading bot.
        
        Args:
            bot_id: Bot identifier
            
        Returns:
            Success status
        """
        try:
            await self._make_request("POST", f"/bots/{bot_id}/start")
            self.logger.info(f"Started bot {bot_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start bot {bot_id}: {e}")
            raise HummingbotAPIError(f"Failed to start bot: {e}")
    
    async def stop_bot(self, bot_id: str) -> bool:
        """Stop a trading bot.
        
        Args:
            bot_id: Bot identifier
            
        Returns:
            Success status
        """
        try:
            await self._make_request("POST", f"/bots/{bot_id}/stop")
            self.logger.info(f"Stopped bot {bot_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop bot {bot_id}: {e}")
            raise HummingbotAPIError(f"Failed to stop bot: {e}")
    
    async def delete_bot(self, bot_id: str) -> bool:
        """Delete a trading bot.
        
        Args:
            bot_id: Bot identifier
            
        Returns:
            Success status
        """
        try:
            await self._make_request("DELETE", f"/bots/{bot_id}")
            self.logger.info(f"Deleted bot {bot_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to delete bot {bot_id}: {e}")
            raise HummingbotAPIError(f"Failed to delete bot: {e}")
    
    async def get_bot_status(self, bot_id: str) -> BotConfig:
        """Get bot status and configuration.
        
        Args:
            bot_id: Bot identifier
            
        Returns:
            Bot configuration with current status
        """
        try:
            response = await self._make_request("GET", f"/bots/{bot_id}")
            
            # Convert response to BotConfig
            bot_config = BotConfig(
                id=response["id"],
                name=response["name"],
                strategy=response["strategy"],
                exchange=response["exchange"],
                trading_pair=response["trading_pair"],
                base_asset=response["base_asset"],
                quote_asset=response["quote_asset"],
                parameters=response["parameters"],
                status=BotStatus(response["status"]),
                created_at=datetime.fromisoformat(response["created_at"]) if response.get("created_at") else None,
                updated_at=datetime.fromisoformat(response["updated_at"]) if response.get("updated_at") else None
            )
            
            return bot_config
            
        except Exception as e:
            self.logger.error(f"Failed to get bot status {bot_id}: {e}")
            raise HummingbotAPIError(f"Failed to get bot status: {e}")
    
    async def list_bots(self) -> List[BotConfig]:
        """List all trading bots.
        
        Returns:
            List of bot configurations
        """
        try:
            response = await self._make_request("GET", "/bots")
            
            bots = []
            for bot_data in response["bots"]:
                bot_config = BotConfig(
                    id=bot_data["id"],
                    name=bot_data["name"],
                    strategy=bot_data["strategy"],
                    exchange=bot_data["exchange"],
                    trading_pair=bot_data["trading_pair"],
                    base_asset=bot_data["base_asset"],
                    quote_asset=bot_data["quote_asset"],
                    parameters=bot_data["parameters"],
                    status=BotStatus(bot_data["status"]),
                    created_at=datetime.fromisoformat(bot_data["created_at"]) if bot_data.get("created_at") else None,
                    updated_at=datetime.fromisoformat(bot_data["updated_at"]) if bot_data.get("updated_at") else None
                )
                bots.append(bot_config)
            
            return bots
            
        except Exception as e:
            self.logger.error(f"Failed to list bots: {e}")
            raise HummingbotAPIError(f"Failed to list bots: {e}")
    
    # Portfolio and Performance Methods
    
    async def get_portfolio_balance(self, exchange: str = None) -> Dict[str, float]:
        """Get portfolio balance.
        
        Args:
            exchange: Specific exchange to query (optional)
            
        Returns:
            Balance by asset
        """
        try:
            params = {"exchange": exchange} if exchange else None
            response = await self._make_request("GET", "/portfolio/balance", params=params)
            return response["balances"]
            
        except Exception as e:
            self.logger.error(f"Failed to get portfolio balance: {e}")
            raise HummingbotAPIError(f"Failed to get portfolio balance: {e}")
    
    async def get_performance_metrics(self, bot_id: str, 
                                    start_date: datetime = None,
                                    end_date: datetime = None) -> PerformanceMetrics:
        """Get bot performance metrics.
        
        Args:
            bot_id: Bot identifier
            start_date: Start date for metrics calculation
            end_date: End date for metrics calculation
            
        Returns:
            Performance metrics
        """
        try:
            params = {}
            if start_date:
                params["start_date"] = start_date.isoformat()
            if end_date:
                params["end_date"] = end_date.isoformat()
            
            response = await self._make_request(
                "GET", 
                f"/bots/{bot_id}/performance", 
                params=params
            )
            
            metrics = PerformanceMetrics(
                bot_id=bot_id,
                total_pnl=response["total_pnl"],
                total_trades=response["total_trades"],
                win_rate=response["win_rate"],
                sharpe_ratio=response["sharpe_ratio"],
                max_drawdown=response["max_drawdown"],
                current_balance=response["current_balance"],
                start_time=datetime.fromisoformat(response["start_time"]),
                end_time=datetime.fromisoformat(response["end_time"])
            )
            
            return metrics
            
        except Exception as e:
            self.logger.error(f"Failed to get performance metrics for bot {bot_id}: {e}")
            raise HummingbotAPIError(f"Failed to get performance metrics: {e}")
    
    # Order Management Methods
    
    async def place_order(self, 
                         exchange: str,
                         trading_pair: str,
                         side: OrderSide,
                         order_type: OrderType,
                         amount: float,
                         price: Optional[float] = None) -> str:
        """Place a manual order.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair (e.g., 'BTC-USDT')
            side: Order side (BUY/SELL)
            order_type: Order type
            amount: Order amount
            price: Order price (required for limit orders)
            
        Returns:
            Order ID
        """
        try:
            order_data = {
                "exchange": exchange,
                "trading_pair": trading_pair,
                "side": side.value,
                "order_type": order_type.value,
                "amount": amount
            }
            
            if price is not None:
                order_data["price"] = price
            
            response = await self._make_request("POST", "/orders", data=order_data)
            order_id = response["order_id"]
            
            self.logger.info(f"Placed {side.value} order {order_id} for {amount} {trading_pair}")
            return order_id
            
        except Exception as e:
            self.logger.error(f"Failed to place order: {e}")
            raise HummingbotAPIError(f"Failed to place order: {e}")
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order.
        
        Args:
            order_id: Order identifier
            
        Returns:
            Success status
        """
        try:
            await self._make_request("DELETE", f"/orders/{order_id}")
            self.logger.info(f"Cancelled order {order_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            raise HummingbotAPIError(f"Failed to cancel order: {e}")
    
    async def get_order_status(self, order_id: str) -> OrderData:
        """Get order status.
        
        Args:
            order_id: Order identifier
            
        Returns:
            Order data
        """
        try:
            response = await self._make_request("GET", f"/orders/{order_id}")
            
            order_data = OrderData(
                id=response["id"],
                bot_id=response.get("bot_id", ""),
                exchange=response["exchange"],
                trading_pair=response["trading_pair"],
                side=OrderSide(response["side"]),
                order_type=OrderType(response["order_type"]),
                amount=response["amount"],
                price=response.get("price"),
                status=response["status"],
                created_at=datetime.fromisoformat(response["created_at"]) if response.get("created_at") else None,
                filled_amount=response.get("filled_amount", 0.0),
                average_price=response.get("average_price")
            )
            
            return order_data
            
        except Exception as e:
            self.logger.error(f"Failed to get order status {order_id}: {e}")
            raise HummingbotAPIError(f"Failed to get order status: {e}")
    
    # Market Data Methods
    
    async def get_ticker(self, exchange: str, trading_pair: str) -> Dict[str, Any]:
        """Get ticker data.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair
            
        Returns:
            Ticker data
        """
        try:
            params = {
                "exchange": exchange,
                "trading_pair": trading_pair
            }
            
            response = await self._make_request(
                "GET", 
                "/market/ticker", 
                params=params,
                require_auth=False
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to get ticker for {trading_pair} on {exchange}: {e}")
            raise HummingbotAPIError(f"Failed to get ticker: {e}")
    
    async def get_orderbook(self, exchange: str, trading_pair: str, depth: int = 20) -> Dict[str, Any]:
        """Get orderbook data.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair
            depth: Orderbook depth
            
        Returns:
            Orderbook data
        """
        try:
            params = {
                "exchange": exchange,
                "trading_pair": trading_pair,
                "depth": depth
            }
            
            response = await self._make_request(
                "GET", 
                "/market/orderbook", 
                params=params,
                require_auth=False
            )
            
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to get orderbook for {trading_pair} on {exchange}: {e}")
            raise HummingbotAPIError(f"Failed to get orderbook: {e}")
    
    # MQTT Real-time Updates
    
    async def connect_mqtt(self):
        """Connect to MQTT broker for real-time updates."""
        try:
            self.mqtt_client = mqtt.Client()
            
            # Set callbacks
            self.mqtt_client.on_connect = self._on_mqtt_connect
            self.mqtt_client.on_message = self._on_mqtt_message
            self.mqtt_client.on_disconnect = self._on_mqtt_disconnect
            
            # Connect to broker
            self.mqtt_client.connect(self.mqtt_host, self.mqtt_port, 60)
            self.mqtt_client.loop_start()
            
            self.logger.info(f"Connected to MQTT broker at {self.mqtt_host}:{self.mqtt_port}")
            
        except Exception as e:
            self.logger.error(f"Failed to connect to MQTT broker: {e}")
    
    def _on_mqtt_connect(self, client, userdata, flags, rc):
        """MQTT connection callback."""
        if rc == 0:
            self.logger.info("Successfully connected to MQTT broker")
            # Subscribe to relevant topics
            topics = [
                "hummingbot/bots/+/status",
                "hummingbot/bots/+/orders",
                "hummingbot/bots/+/performance",
                "hummingbot/market/+/ticker",
                "hummingbot/portfolio/balance"
            ]
            
            for topic in topics:
                client.subscribe(topic)
                self.logger.debug(f"Subscribed to MQTT topic: {topic}")
        else:
            self.logger.error(f"Failed to connect to MQTT broker with code {rc}")
    
    def _on_mqtt_message(self, client, userdata, msg):
        """MQTT message callback."""
        try:
            topic = msg.topic
            payload = json.loads(msg.payload.decode())
            
            self.logger.debug(f"Received MQTT message on topic {topic}: {payload}")
            
            # Route message to appropriate callback
            for pattern, callback in self.mqtt_callbacks.items():
                if pattern in topic:
                    asyncio.create_task(callback(topic, payload))
                    break
                    
        except Exception as e:
            self.logger.error(f"Error processing MQTT message: {e}")
    
    def _on_mqtt_disconnect(self, client, userdata, rc):
        """MQTT disconnection callback."""
        self.logger.warning(f"Disconnected from MQTT broker with code {rc}")
    
    def subscribe_to_bot_updates(self, bot_id: str, callback: Callable):
        """Subscribe to bot status updates.
        
        Args:
            bot_id: Bot identifier
            callback: Callback function for updates
        """
        topic_pattern = f"bots/{bot_id}"
        self.mqtt_callbacks[topic_pattern] = callback
        self.logger.info(f"Subscribed to updates for bot {bot_id}")
    
    def subscribe_to_market_updates(self, exchange: str, trading_pair: str, callback: Callable):
        """Subscribe to market data updates.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair
            callback: Callback function for updates
        """
        topic_pattern = f"market/{exchange}/{trading_pair}"
        self.mqtt_callbacks[topic_pattern] = callback
        self.logger.info(f"Subscribed to market updates for {trading_pair} on {exchange}")
    
    # Health Check
    
    async def health_check(self) -> Dict[str, Any]:
        """Check API health status.
        
        Returns:
            Health status information
        """
        try:
            response = await self._make_request(
                "GET", 
                "/health", 
                require_auth=False
            )
            return response
            
        except Exception as e:
            self.logger.error(f"Health check failed: {e}")
            raise HummingbotAPIError(f"Health check failed: {e}")


# Convenience function for creating client
async def create_hummingbot_client(
    api_host: str = "hummingbot-api",
    api_port: int = 8000,
    mqtt_host: str = "hummingbot-broker",
    mqtt_port: int = 1883,
    username: str = "admin",
    password: str = "admin"
) -> HummingbotAPIClient:
    """Create and connect Hummingbot API client.
    
    Args:
        api_host: Hummingbot API server host
        api_port: Hummingbot API server port
        mqtt_host: MQTT broker host
        mqtt_port: MQTT broker port
        username: API username
        password: API password
        
    Returns:
        Connected Hummingbot API client
    """
    client = HummingbotAPIClient(
        api_host=api_host,
        api_port=api_port,
        mqtt_host=mqtt_host,
        mqtt_port=mqtt_port,
        username=username,
        password=password
    )
    
    await client.connect()
    return client