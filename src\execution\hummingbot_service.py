"""Hummingbot Service Module.

This module provides a high-level service interface for Hummingbot integration,
managing API connections, trading operations, and portfolio monitoring.

Author: inkbytefo
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Callable, Tuple
from dataclasses import dataclass
import json

# Import necessary classes that were previously in hummingbot_integration
import aiohttp
import ssl
from enum import Enum
from typing import Union
from ..config.hummingbot_config import (
    HummingbotConfigManager, ExchangeConfig, TradingConfig, RiskConfig
)
from ..utils.logger import get_logger
from ..monitoring.performance_monitor import PerformanceMonitor
from ..monitoring.risk_monitor import RiskMonitor
from .order_manager import OrderManager


# Custom exceptions
class HummingbotAPIError(Exception):
    """Custom exception for Hummingbot API errors."""
    
    def __init__(self, message: str, status_code: int = None, response_text: str = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text


class HummingbotGatewayClient:
    """Client for Hummingbot Gateway API integration.
    
    This client handles DEX integrations, EVM operations, and Gateway-specific
    functionality that extends beyond the core Hummingbot API.
    """
    
    def __init__(self, gateway_host: str = "localhost", gateway_port: int = 15888, 
                 ssl_cert_path: str = None, ssl_key_path: str = None):
        """Initialize Gateway client.
        
        Args:
            gateway_host: Gateway server host
            gateway_port: Gateway server port
            ssl_cert_path: Path to SSL certificate file
            ssl_key_path: Path to SSL private key file
        """
        self.gateway_host = gateway_host
        self.gateway_port = gateway_port
        self.base_url = f"https://{gateway_host}:{gateway_port}"
        self.ssl_cert_path = ssl_cert_path
        self.ssl_key_path = ssl_key_path
        self.logger = get_logger(self.__class__.__name__)
        
        # SSL context for secure connections
        self.ssl_context = self._create_ssl_context()
        
        # Rate limiting
        self.rate_limiter = asyncio.Semaphore(10)  # Max 10 concurrent requests
        self.last_request_time = 0
        self.min_request_interval = 0.1  # 100ms between requests
    
    def _create_ssl_context(self) -> ssl.SSLContext:
        """Create SSL context for secure Gateway connections."""
        context = ssl.create_default_context()
        
        if self.ssl_cert_path and self.ssl_key_path:
            try:
                context.load_cert_chain(self.ssl_cert_path, self.ssl_key_path)
                self.logger.info("SSL certificate loaded successfully")
            except Exception as e:
                self.logger.warning(f"Failed to load SSL certificate: {e}")
                # Fall back to default context
        
        # For development, allow self-signed certificates
        context.check_hostname = False
        context.verify_mode = ssl.CERT_NONE
        
        return context
    
    async def _make_request(self, method: str, endpoint: str, 
                          data: Dict[str, Any] = None, 
                          params: Dict[str, Any] = None,
                          timeout: int = 30) -> Optional[Dict[str, Any]]:
        """Make HTTP request to Gateway API with rate limiting and error handling."""
        async with self.rate_limiter:
            # Rate limiting
            current_time = time.time()
            time_since_last = current_time - self.last_request_time
            if time_since_last < self.min_request_interval:
                await asyncio.sleep(self.min_request_interval - time_since_last)
            
            self.last_request_time = time.time()
            
            url = f"{self.base_url}{endpoint}"
            
            try:
                timeout_obj = aiohttp.ClientTimeout(total=timeout)
                
                async with aiohttp.ClientSession(
                    timeout=timeout_obj,
                    connector=aiohttp.TCPConnector(ssl=self.ssl_context)
                ) as session:
                    
                    request_kwargs = {
                        'params': params,
                        'headers': {
                            'Content-Type': 'application/json',
                            'User-Agent': 'HummingbotGatewayClient/1.0'
                        }
                    }
                    
                    if data:
                        request_kwargs['json'] = data
                    
                    self.logger.debug(f"Making {method} request to {url}")
                    
                    async with session.request(method, url, **request_kwargs) as response:
                        if response.status == 200:
                            return await response.json()
                        else:
                            error_text = await response.text()
                            self.logger.error(f"Gateway API error {response.status}: {error_text}")
                            raise HummingbotAPIError(f"Gateway API error {response.status}: {error_text}")
                            
            except asyncio.TimeoutError:
                self.logger.error(f"Request timeout for {method} {url}")
                raise HummingbotAPIError(f"Request timeout for {method} {url}")
            except Exception as e:
                self.logger.error(f"Request failed for {method} {url}: {e}")
                raise HummingbotAPIError(f"Request failed: {e}")
    
    async def get_network_status(self, chain: str, network: str) -> Dict[str, Any]:
        """Get network status for a specific blockchain.
        
        Args:
            chain: Blockchain name (e.g., 'ethereum', 'polygon')
            network: Network name (e.g., 'mainnet', 'testnet')
            
        Returns:
            Network status information
        """
        try:
            endpoint = f"/network/status"
            params = {'chain': chain, 'network': network}
            
            response = await self._make_request('GET', endpoint, params=params)
            return response or {}
            
        except Exception as e:
            self.logger.error(f"Failed to get network status for {chain}/{network}: {e}")
            return {}
    
    async def get_evm_balances(self, chain: str, network: str, address: str, 
                             token_symbols: List[str] = None) -> Dict[str, float]:
        """Get EVM token balances for an address.
        
        Args:
            chain: Blockchain name
            network: Network name
            address: Wallet address
            token_symbols: List of token symbols to check
            
        Returns:
            Dictionary of token balances
        """
        try:
            endpoint = f"/evm/balances"
            data = {
                'chain': chain,
                'network': network,
                'address': address
            }
            
            if token_symbols:
                data['tokenSymbols'] = token_symbols
            
            response = await self._make_request('POST', endpoint, data=data)
            
            if response and 'balances' in response:
                return response['balances']
            return {}
            
        except Exception as e:
            self.logger.error(f"Failed to get EVM balances for {address}: {e}")
            return {}
    
    async def get_dex_price(self, chain: str, network: str, connector: str,
                          base: str, quote: str, amount: float,
                          side: str = 'BUY') -> Optional[Dict[str, Any]]:
        """Get price quote from DEX.
        
        Args:
            chain: Blockchain name
            network: Network name
            connector: DEX connector name (e.g., 'uniswap', 'pancakeswap')
            base: Base token symbol
            quote: Quote token symbol
            amount: Amount to trade
            side: Trade side ('BUY' or 'SELL')
            
        Returns:
            Price quote information
        """
        try:
            endpoint = f"/amm/price"
            data = {
                'chain': chain,
                'network': network,
                'connector': connector,
                'base': base,
                'quote': quote,
                'amount': str(amount),
                'side': side
            }
            
            response = await self._make_request('POST', endpoint, data=data)
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to get DEX price for {base}/{quote}: {e}")
            return None
    
    async def execute_dex_trade(self, chain: str, network: str, connector: str,
                              address: str, base: str, quote: str, 
                              amount: float, side: str, 
                              slippage: float = 0.01) -> Optional[Dict[str, Any]]:
        """Execute trade on DEX.
        
        Args:
            chain: Blockchain name
            network: Network name
            connector: DEX connector name
            address: Wallet address
            base: Base token symbol
            quote: Quote token symbol
            amount: Amount to trade
            side: Trade side ('BUY' or 'SELL')
            slippage: Maximum slippage tolerance
            
        Returns:
            Trade execution result
        """
        try:
            endpoint = f"/amm/trade"
            data = {
                'chain': chain,
                'network': network,
                'connector': connector,
                'address': address,
                'base': base,
                'quote': quote,
                'amount': str(amount),
                'side': side,
                'allowedSlippage': str(slippage)
            }
            
            response = await self._make_request('POST', endpoint, data=data)
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to execute DEX trade: {e}")
            return None
    
    async def get_token_allowance(self, chain: str, network: str, 
                                address: str, token: str, 
                                spender: str) -> Optional[float]:
        """Get token allowance for a spender.
        
        Args:
            chain: Blockchain name
            network: Network name
            address: Token owner address
            token: Token contract address or symbol
            spender: Spender contract address
            
        Returns:
            Current allowance amount
        """
        try:
            endpoint = f"/evm/allowances"
            data = {
                'chain': chain,
                'network': network,
                'address': address,
                'token': token,
                'spender': spender
            }
            
            response = await self._make_request('POST', endpoint, data=data)
            
            if response and 'allowance' in response:
                return float(response['allowance'])
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to get token allowance: {e}")
            return None
    
    async def approve_token(self, chain: str, network: str, address: str,
                          token: str, spender: str, amount: float) -> Optional[str]:
        """Approve token spending.
        
        Args:
            chain: Blockchain name
            network: Network name
            address: Token owner address
            token: Token contract address or symbol
            spender: Spender contract address
            amount: Amount to approve
            
        Returns:
            Transaction hash if successful
        """
        try:
            endpoint = f"/evm/approve"
            data = {
                'chain': chain,
                'network': network,
                'address': address,
                'token': token,
                'spender': spender,
                'amount': str(amount)
            }
            
            response = await self._make_request('POST', endpoint, data=data)
            
            if response and 'txHash' in response:
                return response['txHash']
            return None
            
        except Exception as e:
            self.logger.error(f"Failed to approve token: {e}")
            return None
    def __init__(self, message: str, status_code: Optional[int] = None, response_text: Optional[str] = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_text = response_text
        
    def __str__(self):
        if self.status_code:
            return f"HummingbotAPIError (HTTP {self.status_code}): {super().__str__()}"
        return f"HummingbotAPIError: {super().__str__()}"


# Enums and data classes previously in hummingbot_integration.py
class HummingbotStatus(Enum):
    """Hummingbot status enumeration."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    LIMIT_MAKER = "limit_maker"
    STOP_LOSS = "stop_loss"
    STOP_LOSS_LIMIT = "stop_loss_limit"
    TAKE_PROFIT = "take_profit"
    TAKE_PROFIT_LIMIT = "take_profit_limit"


class OrderStatus(Enum):
    """Order status enumeration."""
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELED = "canceled"
    PENDING_CANCEL = "pending_cancel"
    REJECTED = "rejected"
    EXPIRED = "expired"


class ConnectorType(Enum):
    """Connector type enumeration."""
    EXCHANGE = "exchange"
    AMM = "amm"
    DERIVATIVE = "derivative"


@dataclass
class HummingbotConfig:
    """Hummingbot configuration."""
    api_url: str
    username: str
    password: str
    timeout: int = 30
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_ssl_verify: bool = True
    sandbox_mode: bool = False
    
    def get_exchange_endpoints(self, exchange_name: str) -> Dict[str, str]:
        """Get exchange endpoints based on sandbox mode."""
        if not self.sandbox_mode:
            return {}
        
        # Testnet/Sandbox endpoints for different exchanges
        testnet_endpoints = {
            "binance": {
                "api_url": "https://testnet.binance.vision",
                "ws_url": "wss://testnet.binance.vision/ws"
            },
            "coinbase": {
                "api_url": "https://api-public.sandbox.pro.coinbase.com",
                "ws_url": "wss://ws-feed-public.sandbox.pro.coinbase.com"
            },
            "kraken": {
                "api_url": "https://api.kraken.com",  # Kraken doesn't have separate testnet
                "ws_url": "wss://ws.kraken.com"
            }
        }
        
        return testnet_endpoints.get(exchange_name.lower(), {})


@dataclass
class ExchangeCredentials:
    """Exchange credentials."""
    exchange_name: str
    api_key: str
    api_secret: str
    passphrase: Optional[str] = None
    sandbox: bool = False


@dataclass
class TradingPair:
    """Trading pair information."""
    base_asset: str
    quote_asset: str
    exchange: str
    min_order_size: float
    max_order_size: float
    price_decimals: int
    quantity_decimals: int


@dataclass
class OrderRequest:
    """Order request."""
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"
    client_order_id: Optional[str] = None


@dataclass
class OrderResponse:
    """Order response."""
    success: bool
    order_id: Optional[str] = None
    client_order_id: Optional[str] = None
    symbol: Optional[str] = None
    side: Optional[OrderSide] = None
    order_type: Optional[OrderType] = None
    quantity: Optional[float] = None
    price: Optional[float] = None
    status: Optional[OrderStatus] = None
    filled_quantity: Optional[float] = None
    remaining_quantity: Optional[float] = None
    average_price: Optional[float] = None
    commission: Optional[float] = None
    commission_asset: Optional[str] = None
    timestamp: Optional[datetime] = None
    error: Optional[str] = None


@dataclass
class BalanceInfo:
    """Balance information."""
    asset: str
    available: float
    locked: float
    total: float
    exchange: str


@dataclass
class PositionInfo:
    """Position information."""
    symbol: str
    side: str
    amount: float
    entry_price: float
    mark_price: float
    unrealized_pnl: float
    percentage: float
    exchange: str


class HummingbotService:
    """High-level service for Hummingbot integration."""

    def __init__(self, config_manager: HummingbotConfigManager, performance_monitor: PerformanceMonitor, risk_monitor: RiskMonitor):
        """Initialize the Hummingbot service."""
        self.config_manager = config_manager
        self.performance_monitor = performance_monitor
        self.risk_monitor = risk_monitor
        self.logger = get_logger(self.__class__.__name__)
        
        # API client and connection state
        self.api_client: Optional[HummingbotAPIClient] = None
        self.is_running = False
        self.status = HummingbotStatus.STOPPED
        self.order_manager: Optional[OrderManager] = None

    def set_order_manager(self, manager: OrderManager):
        """Set the order manager for feedback loop."""
        self.order_manager = manager
        self.logger.info("OrderManager has been set for HummingbotService.")

    async def _on_order_update(self, order_data: Dict[str, Any]):
        """Callback for handling order updates from Hummingbot."""
        self.logger.info(f"Received order update from Hummingbot: {order_data}")
        if self.order_manager:
            await self.order_manager.update_order_from_venue(order_data)
        else:
            self.logger.warning("OrderManager not set. Cannot process order update.")

class HummingbotAPIClient:
    """Hummingbot API client for communication with Hummingbot instance."""
    
    def __init__(self, config: HummingbotConfig):
        """Initialize the API client."""
        self.config = config
        self.session: Optional[aiohttp.ClientSession] = None
        self.is_connected = False
        self.logger = get_logger(self.__class__.__name__)
        
        # SSL context
        self.ssl_context = ssl.create_default_context()
        if not config.enable_ssl_verify:
            self.ssl_context.check_hostname = False
            self.ssl_context.verify_mode = ssl.CERT_NONE
            
        # Update API URL for sandbox mode if enabled
        if config.sandbox_mode:
            self.logger.info("Sandbox mode enabled - using testnet endpoints")
            # For Hummingbot Gateway, we might need to adjust the base URL
            # This depends on how Hummingbot Gateway is configured for testnet
    
    async def connect(self) -> bool:
        """Connect to Hummingbot API."""
        try:
            connector = aiohttp.TCPConnector(
                ssl=self.ssl_context
            )
            
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=self.config.timeout)
            )
            
            # Test connection
            if await self.health_check():
                self.is_connected = True
                self.logger.info("Connected to Hummingbot API")
                return True
            else:
                await self.disconnect()
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to connect to Hummingbot API: {e}")
            await self.disconnect()
            return False
    
    async def disconnect(self):
        """Disconnect from Hummingbot API."""
        if self.session:
            await self.session.close()
            self.session = None
        self.is_connected = False
        self.logger.info("Disconnected from Hummingbot API")
    
    async def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, require_auth: bool = True) -> Optional[Dict]:
        """Make HTTP request to Hummingbot API with comprehensive error handling."""
        if not self.session:
            self.logger.error("Not connected to API")
            raise HummingbotAPIError("API client not connected")
        
        url = f"{self.config.base_url.rstrip('/')}/{endpoint.lstrip('/')}"
        
        try:
            request_kwargs = {
                "method": method,
                "url": url,
                "json": data,
                "ssl": self.ssl_context,
                "timeout": aiohttp.ClientTimeout(total=self.config.timeout)
            }
            
            if require_auth:
                request_kwargs["auth"] = aiohttp.BasicAuth(self.config.username, self.config.password)
            
            async with self.session.request(**request_kwargs) as response:
                response_text = await response.text()
                
                # Handle successful responses
                if response.status == 200:
                    try:
                        return await response.json()
                    except json.JSONDecodeError as e:
                        self.logger.error(f"Invalid JSON response: {e}")
                        raise HummingbotAPIError(f"Invalid JSON response: {e}", response.status, response_text)
                
                # Handle specific HTTP status codes
                elif response.status == 400:
                    self.logger.error(f"Bad Request (400): {response_text}")
                    raise HummingbotAPIError(f"Bad Request - Invalid parameters or request format: {response_text}", 
                                           response.status, response_text)
                
                elif response.status == 401:
                    self.logger.error(f"Unauthorized (401): {response_text}")
                    raise HummingbotAPIError(f"Unauthorized - Invalid credentials: {response_text}", 
                                           response.status, response_text)
                
                elif response.status == 403:
                    self.logger.error(f"Forbidden (403): {response_text}")
                    raise HummingbotAPIError(f"Forbidden - Access denied: {response_text}", 
                                           response.status, response_text)
                
                elif response.status == 404:
                    self.logger.error(f"Not Found (404): {response_text}")
                    raise HummingbotAPIError(f"Not Found - Endpoint or resource not found: {response_text}", 
                                           response.status, response_text)
                
                elif response.status == 429:
                    self.logger.error(f"Too Many Requests (429): {response_text}")
                    raise HummingbotAPIError(f"Rate limit exceeded - Too many requests: {response_text}", 
                                           response.status, response_text)
                
                elif response.status >= 500 and response.status < 600:
                    if response.status == 500:
                        self.logger.error(f"Internal Server Error (500): {response_text}")
                        raise HummingbotAPIError(f"Internal Server Error - Server encountered an error: {response_text}", 
                                               response.status, response_text)
                    elif response.status == 502:
                        self.logger.error(f"Bad Gateway (502): {response_text}")
                        raise HummingbotAPIError(f"Bad Gateway - Server is temporarily unavailable: {response_text}", 
                                               response.status, response_text)
                    elif response.status == 503:
                        self.logger.error(f"Service Unavailable (503): {response_text}")
                        raise HummingbotAPIError(f"Service Unavailable - Server is temporarily overloaded: {response_text}", 
                                               response.status, response_text)
                    elif response.status == 504:
                        self.logger.error(f"Gateway Timeout (504): {response_text}")
                        raise HummingbotAPIError(f"Gateway Timeout - Server took too long to respond: {response_text}", 
                                               response.status, response_text)
                    else:
                        self.logger.error(f"Server Error ({response.status}): {response_text}")
                        raise HummingbotAPIError(f"Server Error ({response.status}): {response_text}", 
                                               response.status, response_text)
                
                else:
                    self.logger.error(f"Unexpected HTTP status ({response.status}): {response_text}")
                    raise HummingbotAPIError(f"Unexpected HTTP status ({response.status}): {response_text}", 
                                           response.status, response_text)
                    
        except aiohttp.ServerTimeoutError as e:
            self.logger.error(f"Server timeout error - Request took too long: {e}")
            # Trigger retry mechanism if available
            if hasattr(self, '_should_retry_request'):
                await self._should_retry_request(method, endpoint, data)
            raise HummingbotAPIError(f"Server timeout - Request took too long: {e}")
        
        except aiohttp.ClientConnectorError as e:
            self.logger.error(f"Connection error - Unable to connect to Hummingbot API: {e}")
            # Update connection status
            self.is_connected = False
            raise HummingbotAPIError(f"Connection error - Unable to connect to Hummingbot API: {e}")
        
        except aiohttp.ClientResponseError as e:
            if e.status == 429:
                self.logger.warning(f"Rate limit exceeded (429) - Too many requests: {e}")
                # Wait before retrying for rate limit
                await asyncio.sleep(5)  # Wait 5 seconds before potential retry
                raise HummingbotAPIError(f"Rate limit exceeded - Please wait before retrying: {e}", e.status)
            elif e.status == 401:
                self.logger.critical(f"Authentication failed (401) - Invalid API credentials: {e}")
                raise HummingbotAPIError(f"Authentication failed - Invalid API credentials: {e}", e.status)
            else:
                self.logger.error(f"HTTP response error ({e.status}): {e}")
                raise HummingbotAPIError(f"HTTP response error ({e.status}): {e}", e.status)
        
        except asyncio.TimeoutError as e:
            self.logger.error(f"Request timeout - Operation took too long: {e}")
            raise HummingbotAPIError(f"Request timeout - Operation took too long: {e}")
        
        except aiohttp.ClientError as e:
            self.logger.error(f"HTTP client error - General client-side error: {e}")
            raise HummingbotAPIError(f"HTTP client error: {e}")
        
        except Exception as e:
            self.logger.error(f"Unexpected error during API request: {e}")
            raise HummingbotAPIError(f"Unexpected error during API request: {e}")
    
    async def health_check(self) -> bool:
        """Check API health."""
        try:
            response = await self._make_request("GET", "/", require_auth=False)
            return response is not None and response.get("status") == "running"
        except HummingbotAPIError as e:
            self.logger.warning(f"Health check failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Unexpected error during health check: {e}")
            return False
    
    async def get_trading_pairs(self, exchange: str) -> List[TradingPair]:
        """Get available trading pairs."""
        try:
            # Use connector trading rules endpoint instead
            response = await self._make_request("GET", f"/connectors/{exchange}/trading-rules")
            if response and isinstance(response, dict):
                trading_pairs = []
                # Extract trading pairs from trading rules
                for symbol, rules in response.items():
                    if isinstance(rules, dict):
                        # Create basic trading pair info from rules
                        base_asset, quote_asset = symbol.split('-') if '-' in symbol else (symbol[:3], symbol[3:])
                        trading_pair = TradingPair(
                            base_asset=base_asset,
                            quote_asset=quote_asset,
                            exchange=exchange,
                            min_order_size=rules.get('min_order_size', 0.0),
                            max_order_size=rules.get('max_order_size', 1000000.0),
                            price_decimals=rules.get('price_decimals', 8),
                            quantity_decimals=rules.get('quantity_decimals', 8)
                        )
                        trading_pairs.append(trading_pair)
                return trading_pairs
            return []
        except HummingbotAPIError as e:
            self.logger.error(f"Failed to get trading pairs: {e}")
            return []
        except Exception as e:
            self.logger.error(f"Unexpected error getting trading pairs: {e}")
            return []
    
    async def place_order(self, order_request: OrderRequest) -> Optional[OrderResponse]:
        """Place an order."""
        try:
            data = {
                "symbol": order_request.symbol,
                "side": order_request.side.value,
                "type": order_request.order_type.value,
                "quantity": order_request.quantity,
                "price": order_request.price,
                "time_in_force": order_request.time_in_force
            }
            
            if order_request.client_order_id:
                data["client_order_id"] = order_request.client_order_id
            
            response = await self._make_request("POST", "/orders", data)
            
            if response:
                return OrderResponse(
                    success=True,
                    order_id=response.get("order_id"),
                    client_order_id=response.get("client_order_id"),
                    symbol=response.get("symbol"),
                    side=OrderSide(response.get("side")) if response.get("side") else None,
                    order_type=OrderType(response.get("type")) if response.get("type") else None,
                    quantity=response.get("quantity"),
                    price=response.get("price"),
                    status=OrderStatus(response.get("status")) if response.get("status") else None,
                    timestamp=datetime.now()
                )
            else:
                return OrderResponse(success=False, error="Failed to place order")
                
        except HummingbotAPIError as e:
            self.logger.error(f"Failed to place order: {e}")
            return OrderResponse(success=False, error=str(e))
        except Exception as e:
            self.logger.error(f"Unexpected error placing order: {e}")
            return OrderResponse(success=False, error=str(e))
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order."""
        try:
            response = await self._make_request("DELETE", f"/orders/{order_id}")
            return response is not None and response.get("success", False)
        except Exception as e:
            self.logger.error(f"Failed to cancel order: {e}")
            return False
    
    async def get_orders(self, status: Optional[str] = None) -> List[OrderResponse]:
        """Get orders."""
        try:
            endpoint = "/orders"
            if status:
                endpoint += f"?status={status}"
            
            response = await self._make_request("GET", endpoint)
            
            if response and "orders" in response:
                orders = []
                for order_data in response["orders"]:
                    orders.append(OrderResponse(
                        success=True,
                        order_id=order_data.get("order_id"),
                        client_order_id=order_data.get("client_order_id"),
                        symbol=order_data.get("symbol"),
                        side=OrderSide(order_data.get("side")) if order_data.get("side") else None,
                        order_type=OrderType(order_data.get("type")) if order_data.get("type") else None,
                        quantity=order_data.get("quantity"),
                        price=order_data.get("price"),
                        status=OrderStatus(order_data.get("status")) if order_data.get("status") else None,
                        filled_quantity=order_data.get("filled_quantity"),
                        remaining_quantity=order_data.get("remaining_quantity"),
                        average_price=order_data.get("average_price"),
                        commission=order_data.get("commission"),
                        commission_asset=order_data.get("commission_asset")
                    ))
                return orders
            return []
        except Exception as e:
            self.logger.error(f"Failed to get orders: {e}")
            return []
    
    async def get_open_orders(self, connector_name: str = None) -> List[OrderResponse]:
        """Get open orders from Hummingbot.
        
        Args:
            connector_name: Name of the exchange connector
            
        Returns:
            List of open order responses
        """
        try:
            endpoint = "/orders?status=open"
            if connector_name:
                endpoint += f"&connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "orders" in response:
                orders = []
                for order_data in response["orders"]:
                    orders.append(OrderResponse(
                        success=True,
                        order_id=order_data.get("order_id"),
                        client_order_id=order_data.get("client_order_id"),
                        symbol=order_data.get("symbol"),
                        side=OrderSide(order_data.get("side")) if order_data.get("side") else None,
                        order_type=OrderType(order_data.get("type")) if order_data.get("type") else None,
                        quantity=order_data.get("quantity"),
                        price=order_data.get("price"),
                        status=OrderStatus(order_data.get("status")) if order_data.get("status") else None,
                        filled_quantity=order_data.get("filled_quantity"),
                        remaining_quantity=order_data.get("remaining_quantity"),
                        average_price=order_data.get("average_price"),
                        commission=order_data.get("commission"),
                        commission_asset=order_data.get("commission_asset")
                    ))
                return orders
            return []
        except Exception as e:
            self.logger.error(f"Failed to get open orders: {e}")
            return []
    
    async def get_order_status(self, order_id: str, connector_name: str = None) -> Optional[OrderResponse]:
        """Get status of a specific order.
        
        Args:
            order_id: Order ID to check
            connector_name: Name of the exchange connector
            
        Returns:
            Order status response or None if not found
        """
        try:
            endpoint = f"/orders/{order_id}"
            if connector_name:
                endpoint += f"?connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "order" in response:
                order_data = response["order"]
                return OrderResponse(
                    success=True,
                    order_id=order_data.get("order_id"),
                    client_order_id=order_data.get("client_order_id"),
                    symbol=order_data.get("symbol"),
                    side=OrderSide(order_data.get("side")) if order_data.get("side") else None,
                    order_type=OrderType(order_data.get("type")) if order_data.get("type") else None,
                    quantity=order_data.get("quantity"),
                    price=order_data.get("price"),
                    status=OrderStatus(order_data.get("status")) if order_data.get("status") else None,
                    filled_quantity=order_data.get("filled_quantity"),
                    remaining_quantity=order_data.get("remaining_quantity"),
                    average_price=order_data.get("average_price"),
                    commission=order_data.get("commission"),
                    commission_asset=order_data.get("commission_asset")
                )
            return None
        except Exception as e:
            self.logger.error(f"Failed to get order status for {order_id}: {e}")
            return None
    
    async def get_order_history(self, connector_name: str = None, limit: int = 100) -> List[OrderResponse]:
        """Get order history from Hummingbot.
        
        Args:
            connector_name: Name of the exchange connector
            limit: Maximum number of orders to return
            
        Returns:
            List of historical order responses
        """
        try:
            endpoint = f"/orders/history?limit={limit}"
            if connector_name:
                endpoint += f"&connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "orders" in response:
                orders = []
                for order_data in response["orders"]:
                    orders.append(OrderResponse(
                        success=True,
                        order_id=order_data.get("order_id"),
                        client_order_id=order_data.get("client_order_id"),
                        symbol=order_data.get("symbol"),
                        side=OrderSide(order_data.get("side")) if order_data.get("side") else None,
                        order_type=OrderType(order_data.get("type")) if order_data.get("type") else None,
                        quantity=order_data.get("quantity"),
                        price=order_data.get("price"),
                        status=OrderStatus(order_data.get("status")) if order_data.get("status") else None,
                        filled_quantity=order_data.get("filled_quantity"),
                        remaining_quantity=order_data.get("remaining_quantity"),
                        average_price=order_data.get("average_price"),
                        commission=order_data.get("commission"),
                        commission_asset=order_data.get("commission_asset")
                    ))
                return orders
            return []
        except Exception as e:
            self.logger.error(f"Failed to get order history: {e}")
            return []

    async def get_balances(self) -> List[BalanceInfo]:
        """Get account balances."""
        try:
            response = await self._make_request("GET", "/balances")
            
            if response and "balances" in response:
                balances = []
                for balance_data in response["balances"]:
                    balances.append(BalanceInfo(
                        asset=balance_data.get("asset"),
                        available=balance_data.get("available", 0.0),
                        locked=balance_data.get("locked", 0.0),
                        total=balance_data.get("total", 0.0),
                        exchange=balance_data.get("exchange", "")
                    ))
                return balances
            return []
        except Exception as e:
            self.logger.error(f"Failed to get balances: {e}")
            return []
    
    async def get_trading_pairs(self, connector_name: str = None) -> List[str]:
        """Get available trading pairs.
        
        Args:
            connector_name: Name of the exchange connector
            
        Returns:
            List of trading pair symbols
        """
        try:
            endpoint = "/trading-pairs"
            if connector_name:
                endpoint += f"?connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "trading_pairs" in response:
                return response["trading_pairs"]
            return []
        except Exception as e:
            self.logger.error(f"Failed to get trading pairs: {e}")
            return []
    
    async def get_price(self, symbol: str, connector_name: str = None) -> Optional[float]:
        """Get current price for a trading pair.
        
        Args:
            symbol: Trading pair symbol
            connector_name: Name of the exchange connector
            
        Returns:
            Current price or None if not available
        """
        try:
            endpoint = f"/price/{symbol}"
            if connector_name:
                endpoint += f"?connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "price" in response:
                return float(response["price"])
            return None
        except Exception as e:
            self.logger.error(f"Failed to get price for {symbol}: {e}")
            return None
    
    async def get_order_book(self, symbol: str, connector_name: str = None, depth: int = 20) -> Optional[Dict[str, Any]]:
        """Get order book for a trading pair.
        
        Args:
            symbol: Trading pair symbol
            connector_name: Name of the exchange connector
            depth: Order book depth
            
        Returns:
            Order book data or None if not available
        """
        try:
            endpoint = f"/order-book/{symbol}?depth={depth}"
            if connector_name:
                endpoint += f"&connector_name={connector_name}"
                
            response = await self._make_request("GET", endpoint)
            
            if response and "order_book" in response:
                return response["order_book"]
            return None
        except Exception as e:
            self.logger.error(f"Failed to get order book for {symbol}: {e}")
            return None
    
    async def get_market_data(self, exchange: str, trading_pair: str) -> Optional[Dict[str, Any]]:
        """Get market data for a trading pair.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair symbol
            
        Returns:
            Market data or None if not available
        """
        try:
            # Try to get ticker data as market data
            ticker_data = await self.get_ticker(exchange, trading_pair)
            if ticker_data:
                return ticker_data
            
            # Fallback to price data
            price = await self.get_price(trading_pair, exchange)
            if price:
                return {
                    "symbol": trading_pair,
                    "exchange": exchange,
                    "price": price,
                    "timestamp": datetime.now().isoformat()
                }
            
            return None
        except Exception as e:
            self.logger.error(f"Failed to get market data for {trading_pair} on {exchange}: {e}")
            return None
    
    async def get_ticker(self, exchange: str, trading_pair: str) -> Optional[Dict[str, Any]]:
        """Get ticker data for a trading pair.
        
        Args:
            exchange: Exchange name
            trading_pair: Trading pair symbol
            
        Returns:
            Ticker data or None if not available
        """
        try:
            # Try different possible ticker endpoints
            endpoints_to_try = [
                f"/ticker/{trading_pair}?connector_name={exchange}",
                f"/market/ticker?exchange={exchange}&trading_pair={trading_pair}",
                f"/connectors/{exchange}/ticker/{trading_pair}"
            ]
            
            for endpoint in endpoints_to_try:
                try:
                    response = await self._make_request("GET", endpoint, require_auth=False)
                    if response:
                        return response
                except Exception:
                    continue
            
            # If no ticker endpoint works, try to get price
            price = await self.get_price(trading_pair, exchange)
            if price:
                return {
                    "symbol": trading_pair,
                    "exchange": exchange,
                    "price": price,
                    "timestamp": datetime.now().isoformat()
                }
            
            return None
        except Exception as e:
            self.logger.error(f"Failed to get ticker for {trading_pair} on {exchange}: {e}")
            return None
    
    async def start_websocket(self) -> bool:
        """Start WebSocket connection for real-time data.
        
        Returns:
            True if WebSocket started successfully, False otherwise
        """
        try:
            response = await self._make_request("POST", "/websocket/start")
            
            if response and response.get("success"):
                self.logger.info("WebSocket connection started successfully")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to start WebSocket: {e}")
            return False
    
    async def stop_websocket(self) -> bool:
        """Stop WebSocket connection.
        
        Returns:
            True if WebSocket stopped successfully, False otherwise
        """
        try:
            response = await self._make_request("POST", "/websocket/stop")
            
            if response and response.get("success"):
                self.logger.info("WebSocket connection stopped successfully")
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to stop WebSocket: {e}")
            return False

    async def get_positions(self) -> List[PositionInfo]:
        """Get account positions."""
        try:
            response = await self._make_request("GET", "/positions")
            
            if response and "positions" in response:
                positions = []
                for position_data in response["positions"]:
                    positions.append(PositionInfo(
                        symbol=position_data.get("symbol"),
                        side=position_data.get("side"),
                        amount=position_data.get("amount", 0.0),
                        entry_price=position_data.get("entry_price", 0.0),
                        mark_price=position_data.get("mark_price", 0.0),
                        unrealized_pnl=position_data.get("unrealized_pnl", 0.0),
                        percentage=position_data.get("percentage", 0.0),
                        exchange=position_data.get("exchange", "")
                    ))
                return positions
            return []
        except Exception as e:
            self.logger.error(f"Failed to get positions: {e}")
            return []


@dataclass
class TradingSignal:
    """Trading signal data structure."""
    symbol: str
    side: OrderSide
    order_type: OrderType
    amount: float
    price: Optional[float] = None
    confidence: float = 0.0
    timestamp: datetime = None
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.metadata is None:
            self.metadata = {}


@dataclass
class ExecutionResult:
    """Execution result data structure."""
    success: bool
    order_id: Optional[str] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    filled_amount: float = 0.0
    average_price: Optional[float] = None
    fees: List[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.fees is None:
            self.fees = []


class HummingbotService:
    """High-level Hummingbot service for AI trading agent."""
    
    def __init__(self, config_manager: HummingbotConfigManager, order_manager=None):
        self.logger = get_logger(__name__)
        self.config_manager = config_manager
        self.order_manager = order_manager  # Store OrderManager instance for order status feedback loop
        
        # Initialize Hummingbot API client directly
        self.api_client: Optional[HummingbotAPIClient] = None
        
        # Service state
        self.is_running = False
        self.is_connected = False
        self.last_health_check = None
        self.previous_connection_state = False  # Track previous connection state for reconciliation
        
        # Trading state
        self.active_signals: Dict[str, TradingSignal] = {}
        self.execution_history: List[ExecutionResult] = []
        self.portfolio_snapshot: Dict[str, BalanceInfo] = {}
        self.active_orders: Dict[str, OrderResponse] = {}
        self.positions: Dict[str, PositionInfo] = {}
        
        # Monitoring
        self.performance_monitor: Optional[PerformanceMonitor] = None
        self.risk_monitor: Optional[RiskMonitor] = None
        
        # Callbacks
        self.order_callbacks: List[Callable] = []
        self.balance_callbacks: List[Callable] = []
        self.error_callbacks: List[Callable] = []
        
        # Rate limiting
        self.last_request_time = {}
        self.request_counts = {}
        
    async def initialize(self) -> bool:
        """Initialize the Hummingbot service."""
        try:
            self.logger.info("Initializing Hummingbot service...")
            
            # Validate configuration
            if not self.config_manager.validate_config():
                raise ValueError("Invalid configuration")
            
            # Create Hummingbot API client
            api_config = self.config_manager.api_config
            
            # Check if any exchange has sandbox mode enabled
            sandbox_mode = False
            exchanges_config = self.config_manager.exchanges_config
            for exchange_name, exchange_config in exchanges_config.items():
                if hasattr(exchange_config, 'sandbox_mode') and exchange_config.sandbox_mode:
                    sandbox_mode = True
                    self.logger.info(f"Sandbox mode enabled for exchange: {exchange_name}")
                    break
            
            hb_config = HummingbotConfig(
                api_url=api_config.base_url,
                username=api_config.username,
                password=api_config.password,
                timeout=api_config.timeout,
                retry_attempts=api_config.retry_attempts,
                retry_delay=api_config.retry_delay,
                enable_ssl_verify=api_config.enable_ssl,
                sandbox_mode=sandbox_mode
            )
            
            self.api_client = HummingbotAPIClient(hb_config)
            
            # Initialize monitoring
            self.performance_monitor = PerformanceMonitor()
            self.risk_monitor = RiskMonitor()
            
            self.logger.info("Hummingbot service initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize Hummingbot service: {e}")
            return False
    
    async def start(self) -> bool:
        """Start the Hummingbot service."""
        try:
            if self.is_running:
                self.logger.warning("Service is already running")
                return True
            
            self.logger.info("Starting Hummingbot service...")
            
            # Connect to API
            if not await self.api_client.connect():
                # Temporarily skip Hummingbot connection for testing
                self.logger.warning("Failed to connect to Hummingbot API - running in simulation mode")
            
            self.is_connected = True
            
            # Start monitoring
            if self.performance_monitor:
                await self.performance_monitor.start()
            
            if self.risk_monitor:
                await self.risk_monitor.start()
            
            # Initial portfolio sync
            await self._sync_portfolio()
            
            self.is_running = True
            self.logger.info("Hummingbot service started successfully")
            
            # Start background tasks
            asyncio.create_task(self._health_check_loop())
            asyncio.create_task(self._portfolio_sync_loop())
            asyncio.create_task(self._order_status_monitor_loop())
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start Hummingbot service: {e}")
            await self.stop()
            return False
    
    async def stop(self):
        """Stop the Hummingbot service."""
        try:
            self.logger.info("Stopping Hummingbot service...")
            
            self.is_running = False
            
            # Stop monitoring
            if self.performance_monitor:
                await self.performance_monitor.stop()
            
            if self.risk_monitor:
                await self.risk_monitor.stop()
            
            # Disconnect API
            if self.api_client and self.is_connected:
                await self.api_client.disconnect()
                self.is_connected = False
            
            self.logger.info("Hummingbot service stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping Hummingbot service: {e}")
    
    async def test_gateway_connection(self) -> bool:
        """Test connection to Hummingbot Gateway."""
        try:
            if not self.api_client:
                return False
            
            # Test basic connectivity
            response = await self.api_client.get_status()
            return response is not None
            
        except Exception as e:
            self.logger.error(f"Gateway connection test failed: {e}")
            return False
    
    async def test_exchange_connection(self, exchange: str) -> bool:
        """Test connection to a specific exchange."""
        try:
            if not self.api_client:
                return False
            
            # Test exchange connectivity
            balances = await self.api_client.get_balances(exchange)
            return balances is not None
            
        except Exception as e:
            self.logger.error(f"Exchange {exchange} connection test failed: {e}")
            return False
    
    async def execute_signal(self, signal: TradingSignal) -> ExecutionResult:
        """Execute a trading signal."""
        start_time = datetime.now()
        
        try:
            # Validate signal
            if not await self._validate_signal(signal):
                return ExecutionResult(
                    success=False,
                    error_message="Signal validation failed",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Check risk limits
            if not await self._check_risk_limits(signal):
                return ExecutionResult(
                    success=False,
                    error_message="Risk limits exceeded",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Get exchange configuration
            exchange_name = signal.metadata.get('exchange', 'binance')
            exchange_config = self.config_manager.get_exchange_config(exchange_name)
            
            if not exchange_config or not exchange_config.enabled:
                return ExecutionResult(
                    success=False,
                    error_message=f"Exchange {exchange_name} not configured or disabled",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
            # Create order request
            order_request = OrderRequest(
                connector_name=exchange_name,
                trading_pair=signal.symbol,
                side=signal.side,
                order_type=signal.order_type,
                amount=signal.amount,
                price=signal.price
            )
            
            # Execute trade directly
            order_response = await self.execute_trade(order_request)
            
            if order_response:
                # Store active signal
                self.active_signals[order_response.order_id] = signal
                
                # Create execution result
                result = ExecutionResult(
                    success=True,
                    order_id=order_response.order_id,
                    execution_time=(datetime.now() - start_time).total_seconds(),
                    filled_amount=order_response.filled_amount,
                    average_price=order_response.average_price,
                    fees=order_response.fees
                )
                
                # Record execution
                self.execution_history.append(result)
                
                # Update performance metrics
                if self.performance_monitor:
                    await self.performance_monitor.record_trade(order_response)
                
                self.logger.info(f"Successfully executed signal: {signal.symbol} {signal.side.value}")
                return result
            
            else:
                return ExecutionResult(
                    success=False,
                    error_message="Order execution failed",
                    execution_time=(datetime.now() - start_time).total_seconds()
                )
            
        except Exception as e:
            self.logger.error(f"Failed to execute signal: {e}")
            return ExecutionResult(
                success=False,
                error_message=str(e),
                execution_time=(datetime.now() - start_time).total_seconds()
            )
    
    async def execute_trade(self, order_request: OrderRequest) -> Optional[OrderResponse]:
        """Execute a trade directly through the API client."""
        try:
            if not self.api_client:
                self.logger.error("API client not initialized")
                return None
            
            self.logger.info(f"Executing trade: {order_request.side.value} {order_request.amount} {order_request.trading_pair}")
            
            # Place order through API client
            order_response = await self.api_client.place_order(order_request)
            
            if order_response:
                # Store active order
                self.active_orders[order_response.order_id] = order_response
                self.logger.info(f"Trade executed successfully: {order_response.order_id}")
            else:
                self.logger.error("Trade execution failed: No response from API")
            
            return order_response
            
        except Exception as e:
            self.logger.error(f"Error executing trade: {e}")
            return None
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an active order."""
        try:
            if not self.api_client:
                self.logger.error("API client not initialized")
                return False
            
            success = await self.api_client.cancel_order(order_id)
            
            if success:
                # Remove from active orders and signals
                if order_id in self.active_orders:
                    del self.active_orders[order_id]
                if order_id in self.active_signals:
                    del self.active_signals[order_id]
                self.logger.info(f"Successfully cancelled order: {order_id}")
            
            return success
            
        except Exception as e:
            self.logger.error(f"Failed to cancel order {order_id}: {e}")
            return False
    
    async def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary."""
        try:
            if not self.is_running or not self.api_client:
                return {}
            
            # Get balances
            balances = await self.api_client.get_balances()
            
            # Get positions
            positions = await self.api_client.get_positions()
            
            # Calculate total portfolio value
            total_value = 0.0
            portfolio_data = {
                'balances': {},
                'positions': {},
                'total_value': 0.0,
                'timestamp': datetime.now().isoformat()
            }
            
            if balances:
                for balance in balances:
                    portfolio_data['balances'][balance.asset] = {
                        'available': balance.available,
                        'locked': balance.locked,
                        'total': balance.total
                    }
                    total_value += balance.total
            
            if positions:
                for position in positions:
                    portfolio_data['positions'][position.symbol] = {
                        'amount': position.amount,
                        'entry_price': position.entry_price,
                        'mark_price': position.mark_price,
                        'pnl': position.unrealized_pnl
                    }
            
            portfolio_data['total_value'] = total_value
            return portfolio_data
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio summary: {e}")
            return {}
    
    async def get_portfolio_snapshot(self) -> Dict[str, Any]:
        """Get real-time portfolio valuation with current market prices.
        
        Returns:
            Dict containing detailed balances, calculated total portfolio value in base currency,
            and timestamp.
        """
        try:
            if not self.is_running or not self.api_client:
                return {
                    'balances': {},
                    'total_value': 0.0,
                    'base_currency': 'USDT',
                    'timestamp': datetime.now().isoformat(),
                    'error': 'Service not running or API client not available'
                }
            
            # Get all asset balances
            balances = await self.api_client.get_balances()
            
            if not balances:
                return {
                    'balances': {},
                    'total_value': 0.0,
                    'base_currency': 'USDT',
                    'timestamp': datetime.now().isoformat(),
                    'error': 'No balances available'
                }
            
            # Base currency for valuation (typically USDT)
            base_currency = 'USDT'
            total_portfolio_value = 0.0
            detailed_balances = {}
            
            # Import ccxt for price data
            try:
                import ccxt.async_support as ccxt
                
                # Initialize exchange for price data (using Binance as primary source)
                exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'sandbox': False
                })
                await exchange.load_markets()
                
                # Process each balance
                for balance in balances:
                    asset = balance.asset
                    total_balance = balance.total
                    
                    if total_balance <= 0:
                        continue
                    
                    # Initialize balance entry
                    balance_entry = {
                        'asset': asset,
                        'available': balance.available,
                        'locked': balance.locked,
                        'total': total_balance,
                        'value_in_base': 0.0,
                        'price_in_base': 0.0
                    }
                    
                    if asset == base_currency:
                        # Base currency doesn't need conversion
                        balance_entry['value_in_base'] = total_balance
                        balance_entry['price_in_base'] = 1.0
                        total_portfolio_value += total_balance
                    else:
                        # Get current price for non-base currency assets
                        try:
                            # Try different symbol formats
                            symbol_formats = [
                                f"{asset}/{base_currency}",
                                f"{asset}USDT",
                                f"{asset}/USD"
                            ]
                            
                            current_price = None
                            for symbol_format in symbol_formats:
                                try:
                                    if symbol_format in exchange.markets:
                                        ticker = await exchange.fetch_ticker(symbol_format)
                                        current_price = ticker['last']
                                        break
                                except Exception:
                                    continue
                            
                            if current_price is not None and current_price > 0:
                                asset_value = total_balance * current_price
                                balance_entry['value_in_base'] = asset_value
                                balance_entry['price_in_base'] = current_price
                                total_portfolio_value += asset_value
                                
                                self.logger.debug(f"Asset {asset}: {total_balance} @ {current_price} = {asset_value} {base_currency}")
                            else:
                                self.logger.warning(f"Could not get price for {asset}, excluding from portfolio value")
                                balance_entry['error'] = 'Price not available'
                                
                        except Exception as e:
                            self.logger.error(f"Error getting price for {asset}: {e}")
                            balance_entry['error'] = str(e)
                    
                    detailed_balances[asset] = balance_entry
                
                # Close exchange connection
                await exchange.close()
                
            except ImportError:
                self.logger.error("ccxt library not available for price data")
                # Fallback: use basic balance data without price conversion
                for balance in balances:
                    if balance.total > 0:
                        detailed_balances[balance.asset] = {
                            'asset': balance.asset,
                            'available': balance.available,
                            'locked': balance.locked,
                            'total': balance.total,
                            'value_in_base': balance.total if balance.asset == base_currency else 0.0,
                            'price_in_base': 1.0 if balance.asset == base_currency else 0.0,
                            'error': 'Price conversion not available'
                        }
                        if balance.asset == base_currency:
                            total_portfolio_value += balance.total
            
            except Exception as e:
                self.logger.error(f"Error fetching market prices: {e}")
                # Fallback: use basic balance data
                for balance in balances:
                    if balance.total > 0:
                        detailed_balances[balance.asset] = {
                            'asset': balance.asset,
                            'available': balance.available,
                            'locked': balance.locked,
                            'total': balance.total,
                            'value_in_base': balance.total if balance.asset == base_currency else 0.0,
                            'price_in_base': 1.0 if balance.asset == base_currency else 0.0,
                            'error': f'Price fetch error: {str(e)}'
                        }
                        if balance.asset == base_currency:
                            total_portfolio_value += balance.total
            
            portfolio_snapshot = {
                'balances': detailed_balances,
                'total_value': round(total_portfolio_value, 8),
                'base_currency': base_currency,
                'timestamp': datetime.now().isoformat(),
                'asset_count': len(detailed_balances)
            }
            
            self.logger.info(f"Portfolio snapshot: {len(detailed_balances)} assets, total value: {total_portfolio_value:.2f} {base_currency}")
            return portfolio_snapshot
            
        except Exception as e:
            self.logger.error(f"Error getting portfolio snapshot: {e}")
            return {
                'balances': {},
                'total_value': 0.0,
                'base_currency': 'USDT',
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }
    
    async def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        try:
            if not self.performance_monitor:
                return {}
            
            return await self.performance_monitor.get_metrics()
            
        except Exception as e:
            self.logger.error(f"Failed to get performance metrics: {e}")
            return {}
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get risk metrics."""
        try:
            if not self.risk_monitor:
                return {}
            
            return await self.risk_monitor.get_metrics()
            
        except Exception as e:
            self.logger.error(f"Failed to get risk metrics: {e}")
            return {}
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check."""
        try:
            status = {
                'service_running': self.is_running,
                'api_connected': self.is_connected,
                'api_client_status': 'unknown',
                'timestamp': datetime.now().isoformat()
            }
            
            if self.api_client:
                try:
                    api_health = await self.api_client.health_check()
                    status['api_client_status'] = 'healthy' if api_health else 'unhealthy'
                except Exception:
                    status['api_client_status'] = 'error'
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error during health check: {e}")
            return {
                'service_running': False,
                'api_connected': False,
                'api_client_status': 'error',
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    async def get_status(self) -> Dict[str, Any]:
        """Get current service status."""
        try:
            return {
                'is_running': self.is_running,
                'is_connected': self.is_connected,
                'active_orders_count': len(self.active_orders),
                'positions_count': len(self.positions),
                'last_update': datetime.now().isoformat()
            }
        except Exception as e:
            self.logger.error(f"Error getting status: {e}")
            return {'error': str(e)}
    
    async def refresh_portfolio_data(self) -> bool:
        """Refresh portfolio data from API."""
        try:
            if not self.api_client:
                return False
            
            # Update balances
            balances = await self.api_client.get_balances()
            if balances:
                self.logger.info(f"Updated {len(balances)} balance entries")
            
            # Update positions
            positions = await self.api_client.get_positions()
            if positions:
                self.positions = {pos.symbol: pos for pos in positions}
                self.logger.info(f"Updated {len(positions)} position entries")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error refreshing portfolio data: {e}")
            return False
    
    def add_order_callback(self, callback: Callable):
        """Add order update callback."""
        self.order_callbacks.append(callback)
    
    def add_balance_callback(self, callback: Callable):
        """Add balance update callback."""
        self.balance_callbacks.append(callback)
    
    def add_error_callback(self, callback: Callable):
        """Add error callback."""
        self.error_callbacks.append(callback)
    
    def set_order_manager(self, manager):
        """Set the OrderManager instance for order status feedback loop.
        
        Args:
            manager: OrderManager instance to receive order updates
        """
        self.order_manager = manager
        self.logger.info("OrderManager instance set for order status feedback loop")
    
    async def _validate_signal(self, signal: TradingSignal) -> bool:
        """Validate trading signal."""
        try:
            # Basic validation
            if not signal.symbol or not signal.side or not signal.order_type:
                return False
            
            if signal.amount <= 0:
                return False
            
            # Check minimum order amount
            trading_config = self.config_manager.trading_config
            if signal.amount < trading_config.min_order_amount:
                return False
            
            if signal.amount > trading_config.max_order_amount:
                return False
            
            # Check if trading pair is supported
            exchange_name = signal.metadata.get('exchange', 'binance')
            exchange_config = self.config_manager.get_exchange_config(exchange_name)
            
            if exchange_config and exchange_config.trading_pairs:
                if signal.symbol not in exchange_config.trading_pairs:
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Signal validation error: {e}")
            return False
    
    async def _check_risk_limits(self, signal: TradingSignal) -> bool:
        """Check risk limits before execution."""
        try:
            risk_config = self.config_manager.risk_config
            
            # Check maximum open orders
            if len(self.active_signals) >= risk_config.max_open_orders:
                return False
            
            # Check position size limit
            total_value = await self.controller.get_portfolio_value()
            if total_value > 0:
                position_ratio = signal.amount / total_value
                if position_ratio > risk_config.position_size_limit:
                    return False
            
            # Check daily loss limit (simplified)
            # This would need more sophisticated tracking
            
            return True
            
        except Exception as e:
            self.logger.error(f"Risk check error: {e}")
            return False
    
    async def _sync_portfolio(self):
        """Sync portfolio data."""
        try:
            if not self.api_client:
                return
            
            await self.refresh_portfolio_data()
            self.logger.debug("Portfolio data synced")
            
        except Exception as e:
            self.logger.error(f"Error syncing portfolio: {e}")
    
    async def _health_check_loop(self):
        """Background health check loop with connection state monitoring."""
        while self.is_running:
            try:
                # Perform health check
                health_status = await self.health_check()
                
                # Check current connection state
                current_connection_state = self.is_connected and health_status.get('api_client_status') == 'healthy'
                
                # Detect connection state change (reconnection)
                if not self.previous_connection_state and current_connection_state:
                    self.logger.info("Connection restored - triggering state reconciliation")
                    
                    # Trigger reconciliation after connection is restored
                    try:
                        # Import here to avoid circular imports
                        from .execution_manager import ExecutionManager
                        
                        # Find ExecutionManager instance and trigger reconciliation
                        # This is a simplified approach - in production, you might want to use
                        # a callback mechanism or event system
                        if hasattr(self, '_execution_manager_ref') and self._execution_manager_ref:
                            await self._execution_manager_ref.reconcile_state()
                        else:
                            self.logger.warning("ExecutionManager reference not available for reconciliation")
                            
                    except Exception as e:
                        self.logger.error(f"Error triggering reconciliation after reconnection: {e}")
                
                # Update previous connection state
                self.previous_connection_state = current_connection_state
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Health check loop error: {e}")
                await asyncio.sleep(60)
    
    def set_execution_manager_reference(self, execution_manager):
        """Set reference to ExecutionManager for triggering reconciliation.
        
        Args:
            execution_manager: ExecutionManager instance
        """
        self._execution_manager_ref = execution_manager
        self.logger.debug("ExecutionManager reference set for reconciliation callbacks")
    
    async def _portfolio_sync_loop(self):
        """Background portfolio sync loop."""
        while self.is_running:
            try:
                await self._sync_portfolio()
                await asyncio.sleep(30)  # Sync every 30 seconds
            except Exception as e:
                self.logger.error(f"Portfolio sync loop error: {e}")
                await asyncio.sleep(30)
    
    async def _on_order_update(self, order_data: Dict[str, Any]):
        """Handle order update events.
        
        This method is called when an order status update is received from Hummingbot.
        It processes the update and forwards it to the OrderManager to maintain the
        complete order status feedback loop.
        """
        try:
            # Trigger callbacks
            for callback in self.order_callbacks:
                try:
                    await callback(order_data)
                except Exception as e:
                    self.logger.error(f"Order callback error: {e}")
            
            # Update OrderManager with venue order data
            if self.order_manager and hasattr(self.order_manager, 'update_order_from_venue'):
                status = order_data.get('status', '').lower()
                # Forward all order updates to OrderManager for processing
                # This maintains the complete order status feedback loop
                if status in ['filled', 'partially_filled', 'cancelled', 'rejected']:
                    try:
                        # Call the public method in OrderManager to update the SmartOrder
                        # with the latest venue data, which will trigger appropriate callbacks
                        await self.order_manager.update_order_from_venue(order_data)
                    except Exception as e:
                        self.logger.error(f"Error updating order in OrderManager: {e}")
                else:
                    self.logger.debug(f"Order status {status} not requiring OrderManager update")
            elif self.order_manager:
                self.logger.warning("OrderManager instance exists but lacks update_order_from_venue method")
            
            # Update active signals if order is filled or cancelled
            order_id = order_data.get('order_id')
            status = order_data.get('status')
            
            if order_id in self.active_signals and status in ['filled', 'cancelled']:
                del self.active_signals[order_id]
            
            # Sync portfolio immediately when order is filled or partially filled
            if status and status.lower() in ['filled', 'partially_filled']:
                self.logger.info(f"Order {order_id} status changed to {status}, syncing portfolio")
                await self._sync_portfolio()
            
        except Exception as e:
            self.logger.error(f"Order update handler error: {e}")
    
    async def _on_balance_update(self, balance_data: Dict[str, Any]):
        """Handle balance update events."""
        try:
            # Trigger callbacks
            for callback in self.balance_callbacks:
                try:
                    await callback(balance_data)
                except Exception as e:
                    self.logger.error(f"Balance callback error: {e}")
            
            # Update portfolio snapshot
            await self._sync_portfolio()
            
        except Exception as e:
            self.logger.error(f"Balance update handler error: {e}")
    
    async def _order_status_monitor_loop(self):
        """Background order status monitoring loop.
        
        This task runs continuously while the service is active and monitors
        order status changes every 15 seconds. It fetches all active/open orders
        from all configured exchanges and compares them with locally stored
        active orders to detect status changes.
        """
        while self.is_running:
            try:
                await self._monitor_order_status_changes()
                await asyncio.sleep(15)  # Monitor every 15 seconds
            except Exception as e:
                self.logger.error(f"Order status monitor loop error: {e}")
                await asyncio.sleep(15)
    
    async def _monitor_order_status_changes(self):
        """Monitor and detect order status changes.
        
        Fetches all active/open orders from configured exchanges and compares
        with locally stored active orders to detect status changes.
        """
        try:
            if not self.api_client:
                return
            
            # Get all configured exchanges
            enabled_exchange_configs = self.config_manager.get_enabled_exchanges()
            exchanges = [config.name for config in enabled_exchange_configs]
            
            # Fetch active orders from all exchanges
            all_remote_orders = {}
            for exchange_name in exchanges:
                try:
                    # Get open orders from this exchange
                    remote_orders = await self.api_client.get_orders(
                        connector_name=exchange_name,
                        status="open"
                    )
                    
                    # Store orders by order_id
                    for order in remote_orders:
                        if hasattr(order, 'order_id'):
                            all_remote_orders[order.order_id] = order
                        elif isinstance(order, dict) and 'order_id' in order:
                            all_remote_orders[order['order_id']] = order
                            
                except Exception as e:
                    self.logger.warning(f"Failed to fetch orders from {exchange_name}: {e}")
                    continue
            
            # Get locally stored active orders
            local_active_orders = set(self.active_orders.keys())
            
            # Check for status changes
            for order_id in local_active_orders.copy():
                try:
                    # If order is not in remote active orders, it might be filled/cancelled
                    if order_id not in all_remote_orders:
                        # Try to get the order status from each exchange
                        updated_order = None
                        for exchange_name in exchanges:
                            try:
                                updated_order = await self.api_client.get_order_status(
                                    connector_name=exchange_name,
                                    order_id=order_id
                                )
                                if updated_order:
                                    break
                            except Exception as e:
                                self.logger.debug(f"Order {order_id} not found on {exchange_name}: {e}")
                                continue
                        
                        if updated_order:
                            # Check if status has changed from OPEN
                            current_status = getattr(updated_order, 'status', None)
                            if isinstance(updated_order, dict):
                                current_status = updated_order.get('status')
                            
                            # If status changed to FILLED or CANCELLED, trigger callback
                            if current_status and current_status.upper() in ['FILLED', 'CANCELLED', 'REJECTED', 'EXPIRED']:
                                self.logger.info(f"Order status changed: {order_id} -> {current_status}")
                                
                                # Prepare order data for callback
                                order_data = {
                                    'order_id': order_id,
                                    'status': current_status.lower(),
                                    'timestamp': datetime.now().isoformat()
                                }
                                
                                # Add additional order details if available
                                if hasattr(updated_order, '__dict__'):
                                    order_data.update(updated_order.__dict__)
                                elif isinstance(updated_order, dict):
                                    order_data.update(updated_order)
                                
                                # Trigger the order update callback
                                await self._on_order_update(order_data)
                
                except Exception as e:
                    self.logger.error(f"Error checking status for order {order_id}: {e}")
                    continue
            
            # Also check for new orders that might have been created externally
            for order_id, remote_order in all_remote_orders.items():
                if order_id not in local_active_orders:
                    # This is a new order not tracked locally
                    self.logger.info(f"Detected new external order: {order_id}")
                    
                    # Add to local tracking
                    self.active_orders[order_id] = remote_order
                        
        except Exception as e:
            self.logger.error(f"Order status monitoring error: {e}")
    
    async def get_venue_open_orders(self) -> List[Dict[str, Any]]:
        """Get all open orders from all configured venues.
        
        Returns:
            List of open orders from all venues in a unified format.
        """
        try:
            self.logger.info("Fetching open orders from all venues...")
            
            if not self.api_client:
                self.logger.error("API client not initialized")
                return []
            
            # Get all configured and enabled exchanges
            enabled_exchange_configs = self.config_manager.get_enabled_exchanges()
            exchanges = [config.name for config in enabled_exchange_configs]
            
            if not exchanges:
                self.logger.warning("No enabled exchanges found")
                return []
            
            all_orders = []
            
            for exchange_name in exchanges:
                try:
                    self.logger.debug(f"Fetching orders from {exchange_name}...")
                    
                    # Get open orders from this exchange
                    remote_orders = await self.api_client.get_orders(
                        connector_name=exchange_name,
                        status="open"
                    )
                    
                    # Convert to unified format
                    for order in remote_orders:
                        order_data = {}
                        
                        if hasattr(order, '__dict__'):
                            order_data = order.__dict__.copy()
                        elif isinstance(order, dict):
                            order_data = order.copy()
                        
                        # Add exchange info
                        order_data['exchange'] = exchange_name
                        order_data['venue'] = exchange_name
                        
                        all_orders.append(order_data)
                    
                    self.logger.debug(f"Found {len(remote_orders)} open orders on {exchange_name}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to fetch orders from {exchange_name}: {e}")
                    continue
            
            self.logger.info(f"Total open orders fetched: {len(all_orders)}")
            return all_orders
            
        except Exception as e:
            self.logger.error(f"Error fetching venue open orders: {e}")
            return []
    
    async def get_venue_balances(self) -> Dict[str, float]:
        """Get all asset balances from all configured venues.
        
        Returns:
            Dictionary with asset symbols as keys and total balances as values.
            Example: {'BTC': 1.2, 'USDT': 15000.0, 'ETH': 5.5}
        """
        try:
            self.logger.info("Fetching balances from all venues...")
            
            if not self.api_client:
                self.logger.error("API client not initialized")
                return {}
            
            # Get all configured and enabled exchanges
            enabled_exchange_configs = self.config_manager.get_enabled_exchanges()
            exchanges = [config.name for config in enabled_exchange_configs]
            
            if not exchanges:
                self.logger.warning("No enabled exchanges found")
                return {}
            
            consolidated_balances = {}
            
            for exchange_name in exchanges:
                try:
                    self.logger.debug(f"Fetching balances from {exchange_name}...")
                    
                    # Get balances from this exchange
                    balances = await self.api_client.get_balances(connector_name=exchange_name)
                    
                    if balances:
                        for balance in balances:
                            asset = balance.asset if hasattr(balance, 'asset') else balance.get('asset')
                            total = balance.total if hasattr(balance, 'total') else balance.get('total', 0)
                            
                            if asset and total > 0:
                                # Consolidate balances across exchanges
                                if asset in consolidated_balances:
                                    consolidated_balances[asset] += total
                                else:
                                    consolidated_balances[asset] = total
                    
                    self.logger.debug(f"Processed balances from {exchange_name}")
                    
                except Exception as e:
                    self.logger.error(f"Failed to fetch balances from {exchange_name}: {e}")
                    continue
            
            self.logger.info(f"Consolidated balances for {len(consolidated_balances)} assets")
            return consolidated_balances
            
        except Exception as e:
            self.logger.error(f"Error fetching venue balances: {e}")
            return {}

    async def reconcile_state_with_venue(self) -> Tuple[List[Dict[str, Any]], List[str]]:
        """Reconcile system state with venue state.
        
        This method fetches all open orders and account balances from configured venues
        and compares them with the local OrderManager's active_orders list and 
        ExecutionManager's portfolio state.
        
        Returns:
            Tuple containing:
            - untracked_orders: List of orders that exist on venue but not in OrderManager
            - stale_orders: List of order IDs that exist in OrderManager but not on venue
        """
        try:
            self.logger.info("Starting state reconciliation with venues...")
            
            if not self.api_client:
                self.logger.error("API client not initialized for state reconciliation")
                return [], []
            
            # Get all configured and enabled exchanges
            enabled_exchange_configs = self.config_manager.get_enabled_exchanges()
            exchanges = [config.name for config in enabled_exchange_configs]
            
            if not exchanges:
                self.logger.warning("No enabled exchanges found for reconciliation")
                return [], []
            
            # Fetch all open orders from all venues
            venue_orders = {}
            venue_balances = {}
            
            for exchange_name in exchanges:
                try:
                    self.logger.debug(f"Fetching orders from {exchange_name}...")
                    
                    # Get open orders from this exchange
                    remote_orders = await self.api_client.get_orders(
                        connector_name=exchange_name,
                        status="open"
                    )
                    
                    # Store orders by order_id or client_order_id
                    for order in remote_orders:
                        order_data = {}
                        
                        if hasattr(order, '__dict__'):
                            order_data = order.__dict__.copy()
                        elif isinstance(order, dict):
                            order_data = order.copy()
                        
                        # Add exchange info
                        order_data['exchange'] = exchange_name
                        order_data['venue'] = exchange_name
                        
                        # Use order_id as primary key, fallback to client_order_id
                        order_id = order_data.get('order_id') or order_data.get('client_order_id')
                        if order_id:
                            venue_orders[order_id] = order_data
                    
                    self.logger.debug(f"Found {len(remote_orders)} open orders on {exchange_name}")
                    
                    # Get balances from this exchange
                    try:
                        balances = await self.api_client.get_balances(connector_name=exchange_name)
                        if balances:
                            for balance in balances:
                                asset = balance.asset if hasattr(balance, 'asset') else balance.get('asset')
                                if asset:
                                    venue_balances[f"{exchange_name}_{asset}"] = {
                                        'exchange': exchange_name,
                                        'asset': asset,
                                        'available': balance.available if hasattr(balance, 'available') else balance.get('available', 0),
                                        'locked': balance.locked if hasattr(balance, 'locked') else balance.get('locked', 0),
                                        'total': balance.total if hasattr(balance, 'total') else balance.get('total', 0)
                                    }
                    except Exception as e:
                        self.logger.warning(f"Failed to fetch balances from {exchange_name}: {e}")
                        
                except Exception as e:
                    self.logger.error(f"Failed to fetch data from {exchange_name}: {e}")
                    continue
            
            self.logger.info(f"Fetched {len(venue_orders)} orders from {len(exchanges)} venues")
            
            # Get local active orders from OrderManager
            local_active_order_ids = set()
            if self.order_manager and hasattr(self.order_manager, 'active_orders'):
                # Get order IDs from OrderManager's active_orders
                for smart_order in self.order_manager.active_orders:
                    if hasattr(smart_order, 'order_id'):
                        local_active_order_ids.add(smart_order.order_id)
                    elif hasattr(smart_order, 'client_order_id'):
                        local_active_order_ids.add(smart_order.client_order_id)
            
            # Also include orders from HummingbotService's active_orders
            hb_active_order_ids = set(self.active_orders.keys())
            local_active_order_ids.update(hb_active_order_ids)
            
            self.logger.info(f"Found {len(local_active_order_ids)} locally tracked orders")
            
            # Find untracked orders (exist on venue but not locally)
            venue_order_ids = set(venue_orders.keys())
            untracked_order_ids = venue_order_ids - local_active_order_ids
            untracked_orders = [venue_orders[order_id] for order_id in untracked_order_ids]
            
            # Find stale orders (exist locally but not on venue)
            stale_order_ids = list(local_active_order_ids - venue_order_ids)
            
            self.logger.info(f"State reconciliation completed:")
            self.logger.info(f"  - Untracked orders (on venue, not local): {len(untracked_orders)}")
            self.logger.info(f"  - Stale orders (local, not on venue): {len(stale_order_ids)}")
            
            # Log details for debugging
            if untracked_orders:
                self.logger.info("Untracked orders found:")
                for order in untracked_orders:
                    order_id = order.get('order_id', 'unknown')
                    symbol = order.get('symbol', 'unknown')
                    side = order.get('side', 'unknown')
                    amount = order.get('amount', 'unknown')
                    self.logger.info(f"  - {order_id}: {side} {amount} {symbol}")
            
            if stale_order_ids:
                self.logger.info("Stale orders found:")
                for order_id in stale_order_ids:
                    self.logger.info(f"  - {order_id}")
            
            # Update portfolio state with latest balances
            if venue_balances:
                self.logger.debug(f"Updated portfolio state with {len(venue_balances)} balance entries")
            
            return untracked_orders, stale_order_ids
            
        except Exception as e:
            self.logger.error(f"Error during state reconciliation: {e}")
            return [], []
    
    async def get_venue_open_orders(self) -> List[Dict[str, Any]]:
        """Get all open orders from all configured venues.
        
        Returns:
            List of open orders from all venues in a unified format.
        """
        try:
            if not self.is_connected or not self.api_client:
                self.logger.warning("Cannot get venue orders: not connected to Hummingbot")
                return []
            
            self.logger.info("Fetching open orders from all venues...")
            
            # Get open orders from Hummingbot API
            open_orders = await self.api_client.get_open_orders()
            
            if not open_orders:
                self.logger.info("No open orders found on venues")
                return []
            
            # Convert to unified format
            unified_orders = []
            for order in open_orders:
                unified_order = {
                    'order_id': order.get('id', order.get('order_id')),
                    'client_order_id': order.get('client_order_id'),
                    'symbol': order.get('symbol', order.get('trading_pair')),
                    'side': order.get('side'),
                    'type': order.get('type', order.get('order_type')),
                    'amount': float(order.get('amount', 0)),
                    'price': float(order.get('price', 0)) if order.get('price') else None,
                    'filled': float(order.get('filled', 0)),
                    'remaining': float(order.get('remaining', order.get('amount', 0))),
                    'status': order.get('status'),
                    'timestamp': order.get('timestamp', order.get('creation_timestamp')),
                    'venue': order.get('exchange', 'unknown'),
                    'raw_data': order  # Keep original data for reference
                }
                unified_orders.append(unified_order)
            
            self.logger.info(f"Retrieved {len(unified_orders)} open orders from venues")
            return unified_orders
            
        except Exception as e:
            self.logger.error(f"Error fetching venue open orders: {e}")
            return []
    
    async def get_venue_balances(self) -> Dict[str, float]:
        """Get all asset balances from all configured venues.
        
        Returns:
            Dictionary with asset symbols as keys and total balances as values.
            Example: {'BTC': 1.2, 'USDT': 15000.0, 'ETH': 5.5}
        """
        try:
            if not self.is_connected or not self.api_client:
                self.logger.warning("Cannot get venue balances: not connected to Hummingbot")
                return {}
            
            self.logger.info("Fetching balances from all venues...")
            
            # Get balances from Hummingbot API
            balances = await self.api_client.get_balances()
            
            if not balances:
                self.logger.info("No balances found on venues")
                return {}
            
            # Aggregate balances across all venues
            unified_balances = {}
            
            for exchange, exchange_balances in balances.items():
                if isinstance(exchange_balances, dict):
                    for asset, balance_info in exchange_balances.items():
                        if isinstance(balance_info, dict):
                            # Handle different balance formats
                            total_balance = 0.0
                            if 'total' in balance_info:
                                total_balance = float(balance_info['total'])
                            elif 'available' in balance_info:
                                available = float(balance_info.get('available', 0))
                                locked = float(balance_info.get('locked', 0))
                                total_balance = available + locked
                            else:
                                # Fallback: treat balance_info as the balance value
                                try:
                                    total_balance = float(balance_info)
                                except (ValueError, TypeError):
                                    continue
                        else:
                            # Direct balance value
                            try:
                                total_balance = float(balance_info)
                            except (ValueError, TypeError):
                                continue
                        
                        # Aggregate balances across venues
                        if asset in unified_balances:
                            unified_balances[asset] += total_balance
                        else:
                            unified_balances[asset] = total_balance
            
            # Filter out zero balances
            unified_balances = {asset: balance for asset, balance in unified_balances.items() if balance > 0}
            
            self.logger.info(f"Retrieved balances for {len(unified_balances)} assets from venues")
            self.logger.debug(f"Venue balances: {unified_balances}")
            
            return unified_balances
            
        except Exception as e:
            self.logger.error(f"Error fetching venue balances: {e}")
            return {}