"""Order Management System for AI Trading Agent.

This module provides comprehensive order management capabilities including
order lifecycle management, smart order routing, order validation,
and execution monitoring.

Author: inkbytefo
"""

import asyncio
import logging
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
import uuid
from collections import defaultdict


class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"
    UNKNOWN = "unknown"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"
    STOP_LOSS = "stop_loss"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


class TimeInForce(Enum):
    """Time in force enumeration."""
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill
    DAY = "day"  # Day order
    GTD = "gtd"  # Good Till Date


class OrderPriority(Enum):
    """Order priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


@dataclass
class OrderFill:
    """Represents a partial or complete order fill."""
    fill_id: str
    order_id: str
    quantity: float
    price: float
    commission: float
    timestamp: datetime
    venue: str
    trade_id: Optional[str] = None
    liquidity: Optional[str] = None  # maker/taker


@dataclass
class OrderValidation:
    """Order validation result."""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    risk_score: float = 0.0
    estimated_slippage: float = 0.0
    estimated_commission: float = 0.0


@dataclass
class SmartOrder:
    """Enhanced order with smart routing and management features."""
    order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: TimeInForce = TimeInForce.GTC
    
    # Smart order features
    priority: OrderPriority = OrderPriority.NORMAL
    max_slippage: float = 0.005  # 0.5%
    min_fill_size: float = 0.0
    iceberg_size: Optional[float] = None
    twap_duration: Optional[int] = None  # minutes
    
    # Order state
    status: OrderStatus = OrderStatus.PENDING
    filled_quantity: float = 0.0
    remaining_quantity: float = 0.0
    average_fill_price: float = 0.0
    total_commission: float = 0.0
    
    # Timestamps
    created_time: datetime = field(default_factory=datetime.now)
    submitted_time: Optional[datetime] = None
    updated_time: datetime = field(default_factory=datetime.now)
    expiry_time: Optional[datetime] = None
    
    # Execution tracking
    fills: List[OrderFill] = field(default_factory=list)
    venue_orders: Dict[str, str] = field(default_factory=dict)  # venue -> venue_order_id
    execution_venues: List[str] = field(default_factory=list)
    
    # Metadata
    client_order_id: Optional[str] = None
    strategy_id: Optional[str] = None
    parent_order_id: Optional[str] = None
    child_order_ids: List[str] = field(default_factory=list)
    tags: Dict[str, Any] = field(default_factory=dict)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Initialize remaining quantity."""
        if self.remaining_quantity == 0.0:
            self.remaining_quantity = self.quantity
    
    @property
    def is_active(self) -> bool:
        """Check if order is in active state."""
        return self.status in [OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED]
    
    @property
    def is_complete(self) -> bool:
        """Check if order is completely filled."""
        return self.status == OrderStatus.FILLED
    
    @property
    def is_cancelled(self) -> bool:
        """Check if order is cancelled."""
        return self.status in [OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED]
    
    @property
    def fill_percentage(self) -> float:
        """Get fill percentage."""
        return (self.filled_quantity / self.quantity) * 100 if self.quantity > 0 else 0
    
    def add_fill(self, fill: OrderFill):
        """Add a fill to the order."""
        self.fills.append(fill)
        self.filled_quantity += fill.quantity
        self.remaining_quantity = max(0, self.quantity - self.filled_quantity)
        self.total_commission += fill.commission
        
        # Update average fill price
        total_value = sum(f.quantity * f.price for f in self.fills)
        total_quantity = sum(f.quantity for f in self.fills)
        self.average_fill_price = total_value / total_quantity if total_quantity > 0 else 0
        
        # Update status
        if self.remaining_quantity <= 0:
            self.status = OrderStatus.FILLED
        elif self.filled_quantity > 0:
            self.status = OrderStatus.PARTIALLY_FILLED
        
        self.updated_time = datetime.now()


class OrderManager:
    """Comprehensive order management system."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize order manager."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Order storage
        self.orders: Dict[str, SmartOrder] = {}
        self.active_orders: Dict[str, SmartOrder] = {}
        self.order_history: List[SmartOrder] = []
        
        # Order queues by priority
        self.order_queues: Dict[OrderPriority, asyncio.Queue] = {
            priority: asyncio.Queue() for priority in OrderPriority
        }
        
        # Venue connections and configurations
        self.venue_configs = config.get('venues', {})
        self.venue_connections: Dict[str, Any] = {}
        
        # Order validation rules
        self.validation_rules = {
            'min_order_size': config.get('min_order_size', 0.001),
            'max_order_size': config.get('max_order_size', 1000000),
            'max_price_deviation': config.get('max_price_deviation', 0.1),
            'max_daily_orders': config.get('max_daily_orders', 1000),
            'max_position_size': config.get('max_position_size', 100000)
        }
        
        # Risk limits
        self.risk_limits = {
            'max_order_value': config.get('max_order_value', 50000),
            'max_daily_volume': config.get('max_daily_volume', 500000),
            'max_open_orders': config.get('max_open_orders', 100),
            'max_slippage': config.get('max_slippage', 0.02)
        }
        
        # Performance tracking
        self.order_metrics = {
            'total_orders': 0,
            'successful_orders': 0,
            'cancelled_orders': 0,
            'rejected_orders': 0,
            'total_volume': 0.0,
            'total_commission': 0.0,
            'average_fill_time': 0.0,
            'average_slippage': 0.0,
            'venue_performance': defaultdict(list),
            'daily_stats': defaultdict(dict),
            'last_update': None
        }
        
        # Smart routing configuration
        self.routing_config = {
            'enable_smart_routing': config.get('enable_smart_routing', True),
            'venue_weights': config.get('venue_weights', {}),
            'latency_weights': config.get('latency_weights', 0.3),
            'cost_weights': config.get('cost_weights', 0.4),
            'liquidity_weights': config.get('liquidity_weights', 0.3)
        }
        
        # Order processing
        self.is_running = False
        self.processing_tasks: List[asyncio.Task] = []
        
        # Callbacks
        self.order_callbacks: Dict[str, List[Callable]] = defaultdict(list)
        
        # Concurrency protection
        self._orders_lock = asyncio.Lock()
        
        self.logger.info("OrderManager initialized")
    
    def register_callback(self, event: str, callback: Callable):
        """Register a callback for order events."""
        self.order_callbacks[event].append(callback)
    
    async def start(self):
        """Start the order manager."""
        try:
            self.is_running = True
            
            # Start order processing tasks
            for priority in OrderPriority:
                task = asyncio.create_task(
                    self._process_order_queue(priority)
                )
                self.processing_tasks.append(task)
            
            # Start monitoring tasks
            monitor_task = asyncio.create_task(self._monitor_orders())
            self.processing_tasks.append(monitor_task)
            
            # Start metrics update task
            metrics_task = asyncio.create_task(self._update_metrics_periodically())
            self.processing_tasks.append(metrics_task)
            
            self.logger.info("OrderManager started")
            
        except Exception as e:
            self.logger.error(f"Error starting OrderManager: {e}")
            raise
    
    async def stop(self):
        """Stop the order manager."""
        try:
            self.is_running = False
            
            # Cancel all processing tasks
            for task in self.processing_tasks:
                task.cancel()
            
            # Wait for tasks to complete
            await asyncio.gather(*self.processing_tasks, return_exceptions=True)
            
            # Cancel all active orders
            for order_id in list(self.active_orders.keys()):
                await self.cancel_order(order_id, "System shutdown")
            
            self.logger.info("OrderManager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping OrderManager: {e}")
    
    async def submit_order(self, order: SmartOrder) -> OrderValidation:
        """Submit a new order."""
        try:
            # Validate order
            validation = await self._validate_order(order)
            if not validation.is_valid:
                order.status = OrderStatus.REJECTED
                self.logger.warning(f"Order rejected: {validation.errors}")
                return validation
            
            async with self._orders_lock:
                # Generate order ID if not provided
                if not order.order_id:
                    order.order_id = str(uuid.uuid4())
                
                # Set timestamps
                order.created_time = datetime.now()
                order.updated_time = datetime.now()
                
                # Store order
                self.orders[order.order_id] = order
                self.active_orders[order.order_id] = order
            
            # Queue order for processing based on priority
            await self.order_queues[order.priority].put(order)
            
            # Trigger callbacks
            await self._trigger_callbacks('order_submitted', order)
            
            self.logger.info(f"Order submitted: {order.order_id} - {order.symbol} {order.side.value} {order.quantity}")
            
            return validation
            
        except Exception as e:
            self.logger.error(f"Error submitting order: {e}")
            order.status = OrderStatus.FAILED
            return OrderValidation(is_valid=False, errors=[str(e)])
    
    async def cancel_order(self, order_id: str, reason: str = "") -> bool:
        """Cancel an order."""
        try:
            async with self._orders_lock:
                if order_id not in self.active_orders:
                    self.logger.warning(f"Order {order_id} not found or not active")
                    return False
                
                order = self.active_orders[order_id]
                
                # Cancel on all venues
                for venue, venue_order_id in order.venue_orders.items():
                    await self._cancel_venue_order(venue, venue_order_id)
                
                # Update order status
                order.status = OrderStatus.CANCELLED
                order.updated_time = datetime.now()
                order.tags['cancellation_reason'] = reason
                
                # Remove from active orders
                del self.active_orders[order_id]
                
                # Add to history
                self.order_history.append(order)
            
            # Trigger callbacks
            await self._trigger_callbacks('order_cancelled', order)
            
            self.logger.info(f"Order cancelled: {order_id} - {reason}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    async def modify_order(self, order_id: str, modifications: Dict[str, Any]) -> bool:
        """Modify an existing order."""
        try:
            async with self._orders_lock:
                if order_id not in self.active_orders:
                    self.logger.warning(f"Order {order_id} not found or not active")
                    return False
                
                order = self.active_orders[order_id]
                
                # Validate modifications
                if not await self._validate_modifications(order, modifications):
                    return False
                
                # Apply modifications
                for field, value in modifications.items():
                    if hasattr(order, field):
                        setattr(order, field, value)
                
                order.updated_time = datetime.now()
            
            # Update on venues
            for venue, venue_order_id in order.venue_orders.items():
                await self._modify_venue_order(venue, venue_order_id, modifications)
            
            # Trigger callbacks
            await self._trigger_callbacks('order_modified', order)
            
            self.logger.info(f"Order modified: {order_id}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error modifying order {order_id}: {e}")
            return False
    
    async def get_order(self, order_id: str) -> Optional[SmartOrder]:
        """Get order by ID."""
        return self.orders.get(order_id)
    
    async def get_active_orders(self, symbol: Optional[str] = None) -> List[SmartOrder]:
        """Get active orders, optionally filtered by symbol."""
        orders = list(self.active_orders.values())
        
        if symbol:
            orders = [order for order in orders if order.symbol == symbol]
        
        return orders
    
    async def get_order_history(self, limit: int = 100, symbol: Optional[str] = None) -> List[SmartOrder]:
        """Get order history."""
        history = self.order_history[-limit:]
        
        if symbol:
            history = [order for order in history if order.symbol == symbol]
        
        return history
    
    async def get_order_metrics(self) -> Dict[str, Any]:
        """Get order performance metrics."""
        return self.order_metrics.copy()
    
    async def get_venue_performance(self) -> Dict[str, Any]:
        """Get venue performance statistics."""
        try:
            venue_stats = {}
            
            for venue, performances in self.order_metrics['venue_performance'].items():
                if performances:
                    venue_stats[venue] = {
                        'average_performance': sum(performances) / len(performances),
                        'best_performance': max(performances),
                        'worst_performance': min(performances),
                        'execution_count': len(performances)
                    }
            
            return venue_stats
            
        except Exception as e:
            self.logger.error(f"Error getting venue performance: {e}")
            return {}
    
    async def get_daily_stats(self, date: Optional[datetime] = None) -> Dict[str, Any]:
        """Get daily trading statistics."""
        target_date = (date or datetime.now()).date()
        return self.order_metrics['daily_stats'].get(target_date, {})
    
    async def cancel_all_orders(self, symbol: Optional[str] = None, reason: str = "Cancel all") -> int:
        """Cancel all active orders, optionally filtered by symbol."""
        try:
            orders_to_cancel = list(self.active_orders.keys())
            
            if symbol:
                orders_to_cancel = [
                    order_id for order_id in orders_to_cancel
                    if self.active_orders[order_id].symbol == symbol
                ]
            
            cancelled_count = 0
            for order_id in orders_to_cancel:
                if await self.cancel_order(order_id, reason):
                    cancelled_count += 1
            
            return cancelled_count
            
        except Exception as e:
            self.logger.error(f"Error cancelling all orders: {e}")
            return 0
    
    async def update_order_from_venue(self, order_data: Dict[str, Any]):
        """Update order status and fills from venue data."""
        try:
            # Extract order identification
            venue_order_id = order_data.get('order_id')
            client_order_id = order_data.get('client_order_id')
            
            if not venue_order_id and not client_order_id:
                self.logger.warning("Order update received without order_id or client_order_id")
                return
            
            # Find the corresponding SmartOrder
            target_order = None
            target_order_id = None
            
            # First try to find by client_order_id (our internal ID)
            if client_order_id:
                for order_id, order in self.active_orders.items():
                    if order.client_order_id == client_order_id:
                        target_order = order
                        target_order_id = order_id
                        break
            
            # If not found, try to find by venue_order_id
            if not target_order and venue_order_id:
                for order_id, order in self.active_orders.items():
                    if venue_order_id in order.venue_orders.values():
                        target_order = order
                        target_order_id = order_id
                        break
            
            if not target_order:
                self.logger.warning(f"No matching order found for venue update: {venue_order_id}")
                return
            
            # Update order status
            venue_status = order_data.get('status', '').lower()
            if venue_status == 'filled':
                target_order.status = OrderStatus.FILLED
