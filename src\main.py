#!/usr/bin/env python3
"""
AI Trading System - Main Entry Point

This is the main entry point for the AI Trading System.
It initializes and coordinates all system components.

Author: inkbytefo
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent))

from core.agent import TradingAgent
from config.settings import Settings
from utils.logger import setup_logging


class AITradingSystem:
    """Main AI Trading System class"""
    
    def __init__(self):
        self.settings = Settings()
        self.logger = setup_logging()
        self.trading_agent = None
        self.running = False
        
    async def start(self):
        """Start the AI Trading System"""
        try:
            self.logger.info("Starting AI Trading System...")
            
            # Initialize trading agent
            self.trading_agent = TradingAgent()
            await self.trading_agent.initialize()
            
            # Start the main trading loop
            self.running = True
            await self.run_trading_loop()
            
        except Exception as e:
            self.logger.error(f"Error starting AI Trading System: {e}")
            raise
    
    async def run_trading_loop(self):
        """Main trading loop"""
        self.logger.info("Starting main trading loop...")
        
        while self.running:
            try:
                # Run trading cycle
                await self.trading_agent.run_cycle()
                
                # Wait before next cycle
                await asyncio.sleep(self.settings.TRADING_CYCLE_INTERVAL)
                
            except Exception as e:
                self.logger.error(f"Error in trading loop: {e}")
                await asyncio.sleep(5)  # Wait before retrying
    
    async def stop(self):
        """Stop the AI Trading System"""
        self.logger.info("Stopping AI Trading System...")
        self.running = False
        
        if self.trading_agent:
            await self.trading_agent.shutdown()
    
    def signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        self.logger.info(f"Received signal {signum}, shutting down...")
        asyncio.create_task(self.stop())


async def main():
    """Main function"""
    system = AITradingSystem()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, system.signal_handler)
    signal.signal(signal.SIGTERM, system.signal_handler)
    
    try:
        await system.start()
    except KeyboardInterrupt:
        await system.stop()
    except Exception as e:
        logging.error(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())