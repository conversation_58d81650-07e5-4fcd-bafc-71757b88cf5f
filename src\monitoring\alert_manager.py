"""Alert Management System for AI Trading Agent.

This module provides comprehensive alert management, notification routing,
and escalation capabilities for the AI trading system.

Author: inkbytefo
"""

import asyncio
import logging
import json
import smtplib
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable, Set
from dataclasses import dataclass, field
from collections import defaultdict, deque
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from pathlib import Path
import threading
import uuid

try:
    import requests
except ImportError:
    requests = None

try:
    from twilio.rest import Client as TwilioClient
except ImportError:
    TwilioClient = None


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class AlertCategory(Enum):
    """Alert categories."""
    SYSTEM = "system"
    TRADING = "trading"
    RISK = "risk"
    PERFORMANCE = "performance"
    SECURITY = "security"
    DATA = "data"
    CONNECTIVITY = "connectivity"
    AI_MODEL = "ai_model"


class AlertStatus(Enum):
    """Alert status."""
    ACTIVE = "active"
    ACKNOWLEDGED = "acknowledged"
    RESOLVED = "resolved"
    SUPPRESSED = "suppressed"
    ESCALATED = "escalated"


class NotificationChannel(Enum):
    """Notification channels."""
    EMAIL = "email"
    SMS = "sms"
    SLACK = "slack"
    DISCORD = "discord"
    WEBHOOK = "webhook"
    CONSOLE = "console"
    FILE = "file"
    DATABASE = "database"


class EscalationLevel(Enum):
    """Escalation levels."""
    LEVEL_1 = "level_1"  # Initial notification
    LEVEL_2 = "level_2"  # Supervisor notification
    LEVEL_3 = "level_3"  # Manager notification
    LEVEL_4 = "level_4"  # Executive notification


@dataclass
class AlertRule:
    """Alert rule configuration."""
    rule_id: str
    name: str
    category: AlertCategory
    severity: AlertSeverity
    condition: str  # Condition expression
    threshold: float
    comparison: str  # >, <, >=, <=, ==, !=
    time_window: int  # seconds
    min_occurrences: int = 1
    cooldown_period: int = 300  # 5 minutes
    auto_resolve: bool = True
    enabled: bool = True
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class NotificationConfig:
    """Notification configuration."""
    channel: NotificationChannel
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)
    severity_filter: List[AlertSeverity] = field(default_factory=list)
    category_filter: List[AlertCategory] = field(default_factory=list)
    rate_limit: Optional[int] = None  # Max notifications per hour
    quiet_hours: Optional[Dict[str, str]] = None  # {"start": "22:00", "end": "08:00"}


@dataclass
class EscalationRule:
    """Escalation rule configuration."""
    rule_id: str
    severity: AlertSeverity
    category: AlertCategory
    escalation_delay: int  # seconds before escalation
    escalation_levels: List[EscalationLevel]
    notification_configs: Dict[EscalationLevel, List[NotificationConfig]]
    max_escalations: int = 3
    enabled: bool = True


@dataclass
class Alert:
    """Alert instance."""
    alert_id: str
    rule_id: str
    category: AlertCategory
    severity: AlertSeverity
    title: str
    message: str
    source: str
    timestamp: datetime
    status: AlertStatus = AlertStatus.ACTIVE
    acknowledged_by: Optional[str] = None
    acknowledged_at: Optional[datetime] = None
    resolved_at: Optional[datetime] = None
    escalation_level: EscalationLevel = EscalationLevel.LEVEL_1
    escalation_count: int = 0
    last_escalation: Optional[datetime] = None
    notification_count: int = 0
    last_notification: Optional[datetime] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    related_alerts: List[str] = field(default_factory=list)


@dataclass
class NotificationResult:
    """Notification delivery result."""
    notification_id: str
    alert_id: str
    channel: NotificationChannel
    success: bool
    timestamp: datetime
    error_message: Optional[str] = None
    delivery_time_ms: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


class AlertManager:
    """Comprehensive alert management and notification system."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize alert manager."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Alert management
        self.alert_rules: Dict[str, AlertRule] = {}
        self.active_alerts: Dict[str, Alert] = {}
        self.alert_history: List[Alert] = []
        self.suppressed_alerts: Set[str] = set()
        
        # Notification management
        self.notification_configs: Dict[str, NotificationConfig] = {}
        self.escalation_rules: Dict[str, EscalationRule] = {}
        self.notification_history: List[NotificationResult] = []
        self.notification_rate_limits: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Alert processing
        self.alert_queue: asyncio.Queue = asyncio.Queue()
        self.notification_queue: asyncio.Queue = asyncio.Queue()
        self.processing_tasks: List[asyncio.Task] = []
        
        # Metrics and monitoring
        self.alert_metrics = {
            'total_alerts': 0,
            'alerts_by_severity': defaultdict(int),
            'alerts_by_category': defaultdict(int),
            'notifications_sent': 0,
            'notifications_failed': 0,
            'escalations_triggered': 0,
            'avg_resolution_time': 0.0
        }
        
        # Alert correlation
        self.correlation_rules: Dict[str, Dict[str, Any]] = {}
        self.correlation_window = config.get('correlation_window', 300)  # 5 minutes
        
        # Maintenance and cleanup
        self.retention_days = config.get('retention_days', 30)
        self.cleanup_interval = config.get('cleanup_interval', 3600)  # 1 hour
        
        # State management
        self.is_running = False
        
        # Load configurations
        self._load_configurations()
        
        self.logger.info("AlertManager initialized")
    
    def _load_configurations(self):
        """Load alert rules and notification configurations."""
        try:
            # Load default alert rules
            default_rules = [
                AlertRule(
                    rule_id="high_cpu_usage",
                    name="High CPU Usage",
                    category=AlertCategory.SYSTEM,
                    severity=AlertSeverity.WARNING,
                    condition="cpu_usage",
                    threshold=80.0,
                    comparison=">",
                    time_window=300,
                    min_occurrences=3
                ),
                AlertRule(
                    rule_id="critical_cpu_usage",
                    name="Critical CPU Usage",
                    category=AlertCategory.SYSTEM,
                    severity=AlertSeverity.CRITICAL,
                    condition="cpu_usage",
                    threshold=95.0,
                    comparison=">",
                    time_window=60,
                    min_occurrences=1
                ),
                AlertRule(
                    rule_id="high_memory_usage",
                    name="High Memory Usage",
                    category=AlertCategory.SYSTEM,
                    severity=AlertSeverity.WARNING,
                    condition="memory_usage",
                    threshold=85.0,
                    comparison=">",
                    time_window=300,
                    min_occurrences=2
                ),
                AlertRule(
                    rule_id="trading_error_rate",
                    name="High Trading Error Rate",
                    category=AlertCategory.TRADING,
                    severity=AlertSeverity.CRITICAL,
                    condition="error_rate",
                    threshold=0.05,
                    comparison=">",
                    time_window=300,
                    min_occurrences=1
                ),
                AlertRule(
                    rule_id="risk_limit_breach",
                    name="Risk Limit Breach",
                    category=AlertCategory.RISK,
                    severity=AlertSeverity.EMERGENCY,
                    condition="risk_exposure",
                    threshold=1.0,
                    comparison=">",
                    time_window=0,
                    min_occurrences=1,
                    auto_resolve=False
                )
            ]
            
            for rule in default_rules:
                self.alert_rules[rule.rule_id] = rule
            
            # Load default notification configs
            self._setup_default_notifications()
            
            # Load default escalation rules
            self._setup_default_escalations()
            
        except Exception as e:
            self.logger.error(f"Error loading configurations: {e}")
    
    def _setup_default_notifications(self):
        """Setup default notification configurations."""
        try:
            # Console notifications
            console_config = NotificationConfig(
                channel=NotificationChannel.CONSOLE,
                enabled=True,
                config={},
                severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]
            )
            self.notification_configs["console"] = console_config
            
            # File notifications
            file_config = NotificationConfig(
                channel=NotificationChannel.FILE,
                enabled=True,
                config={
                    "filepath": "logs/alerts.log",
                    "format": "json"
                }
            )
            self.notification_configs["file"] = file_config
            
            # Email notifications (if configured)
            email_settings = self.config.get('email', {})
            if email_settings.get('enabled', False):
                email_config = NotificationConfig(
                    channel=NotificationChannel.EMAIL,
                    enabled=True,
                    config=email_settings,
                    severity_filter=[AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY],
                    rate_limit=10  # Max 10 emails per hour
                )
                self.notification_configs["email"] = email_config
            
            # Slack notifications (if configured)
            slack_settings = self.config.get('slack', {})
            if slack_settings.get('enabled', False):
                slack_config = NotificationConfig(
                    channel=NotificationChannel.SLACK,
                    enabled=True,
                    config=slack_settings,
                    severity_filter=[AlertSeverity.WARNING, AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY],
                    rate_limit=20  # Max 20 messages per hour
                )
                self.notification_configs["slack"] = slack_config
            
        except Exception as e:
            self.logger.error(f"Error setting up default notifications: {e}")
    
    def _setup_default_escalations(self):
        """Setup default escalation rules."""
        try:
            # Critical alert escalation
            critical_escalation = EscalationRule(
                rule_id="critical_escalation",
                severity=AlertSeverity.CRITICAL,
                category=AlertCategory.TRADING,
                escalation_delay=300,  # 5 minutes
                escalation_levels=[EscalationLevel.LEVEL_1, EscalationLevel.LEVEL_2],
                notification_configs={
                    EscalationLevel.LEVEL_1: [self.notification_configs.get("console"), self.notification_configs.get("file")],
                    EscalationLevel.LEVEL_2: [self.notification_configs.get("email"), self.notification_configs.get("slack")]
                },
                max_escalations=2
            )
            
            # Filter out None configs
            for level in critical_escalation.notification_configs:
                critical_escalation.notification_configs[level] = [
                    config for config in critical_escalation.notification_configs[level] if config is not None
                ]
            
            self.escalation_rules["critical_escalation"] = critical_escalation
            
            # Emergency alert escalation
            emergency_escalation = EscalationRule(
                rule_id="emergency_escalation",
                severity=AlertSeverity.EMERGENCY,
                category=AlertCategory.RISK,
                escalation_delay=60,  # 1 minute
                escalation_levels=[EscalationLevel.LEVEL_1, EscalationLevel.LEVEL_2, EscalationLevel.LEVEL_3],
                notification_configs={
                    EscalationLevel.LEVEL_1: [self.notification_configs.get("console"), self.notification_configs.get("file")],
                    EscalationLevel.LEVEL_2: [self.notification_configs.get("email"), self.notification_configs.get("slack")],
                    EscalationLevel.LEVEL_3: [self.notification_configs.get("email")]  # Executive notification
                },
                max_escalations=3
            )
            
            # Filter out None configs
            for level in emergency_escalation.notification_configs:
                emergency_escalation.notification_configs[level] = [
                    config for config in emergency_escalation.notification_configs[level] if config is not None
                ]
            
            self.escalation_rules["emergency_escalation"] = emergency_escalation
            
        except Exception as e:
            self.logger.error(f"Error setting up default escalations: {e}")
    
    async def start(self):
        """Start alert manager."""
        try:
            if self.is_running:
                self.logger.warning("Alert manager already running")
                return
            
            self.is_running = True
            
            # Start processing tasks
            self.processing_tasks = [
                asyncio.create_task(self._process_alerts()),
                asyncio.create_task(self._process_notifications()),
                asyncio.create_task(self._monitor_escalations()),
                asyncio.create_task(self._cleanup_old_data())
            ]
            
            self.logger.info("Alert manager started")
            
        except Exception as e:
            self.logger.error(f"Error starting alert manager: {e}")
            raise
    
    async def stop(self):
        """Stop alert manager."""
        try:
            self.is_running = False
            
            # Cancel processing tasks
            for task in self.processing_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            if self.processing_tasks:
                await asyncio.gather(*self.processing_tasks, return_exceptions=True)
            
            self.processing_tasks.clear()
            self.logger.info("Alert manager stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping alert manager: {e}")
    
    async def create_alert(self, rule_id: str, source: str, value: float, 
                          metadata: Optional[Dict[str, Any]] = None) -> Optional[str]:
        """Create new alert based on rule."""
        try:
            if rule_id not in self.alert_rules:
                self.logger.error(f"Alert rule not found: {rule_id}")
                return None
            
            rule = self.alert_rules[rule_id]
            
            if not rule.enabled:
                return None
            
            # Check if alert should be created based on rule conditions
            if not self._evaluate_alert_condition(rule, value):
                return None
            
            # Check for suppression
            if rule_id in self.suppressed_alerts:
                return None
            
            # Check cooldown period
            if self._is_in_cooldown(rule_id):
                return None
            
            # Create alert
            alert_id = str(uuid.uuid4())
            alert = Alert(
                alert_id=alert_id,
                rule_id=rule_id,
                category=rule.category,
                severity=rule.severity,
                title=rule.name,
                message=f"{rule.name}: {rule.condition} = {value} {rule.comparison} {rule.threshold}",
                source=source,
                timestamp=datetime.now(),
                tags=rule.tags.copy(),
                metadata=metadata or {}
            )
            
            # Add to active alerts
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            
            # Update metrics
            self.alert_metrics['total_alerts'] += 1
            self.alert_metrics['alerts_by_severity'][rule.severity.value] += 1
            self.alert_metrics['alerts_by_category'][rule.category.value] += 1
            
            # Queue for processing
            await self.alert_queue.put(alert)
            
            self.logger.info(f"Alert created: {alert_id} - {rule.name}")
            return alert_id
            
        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")
            return None
    
    def _evaluate_alert_condition(self, rule: AlertRule, value: float) -> bool:
        """Evaluate if alert condition is met."""
        try:
            if rule.comparison == ">":
                return value > rule.threshold
            elif rule.comparison == ">=":
                return value >= rule.threshold
            elif rule.comparison == "<":
                return value < rule.threshold
            elif rule.comparison == "<=":
                return value <= rule.threshold
            elif rule.comparison == "==":
                return abs(value - rule.threshold) < 0.001  # Float comparison
            elif rule.comparison == "!=":
                return abs(value - rule.threshold) >= 0.001
            else:
                self.logger.error(f"Unknown comparison operator: {rule.comparison}")
                return False
                
        except Exception as e:
            self.logger.error(f"Error evaluating alert condition: {e}")
            return False
    
    def _is_in_cooldown(self, rule_id: str) -> bool:
        """Check if rule is in cooldown period."""
        try:
            if rule_id not in self.alert_rules:
                return False
            
            rule = self.alert_rules[rule_id]
            now = datetime.now()
            
            # Find last alert for this rule
            last_alert = None
            for alert in reversed(self.alert_history):
                if alert.rule_id == rule_id:
                    last_alert = alert
                    break
            
            if last_alert:
                time_since_last = (now - last_alert.timestamp).total_seconds()
                return time_since_last < rule.cooldown_period
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error checking cooldown: {e}")
            return False
    
    async def _process_alerts(self):
        """Process alerts from queue."""
        while self.is_running:
            try:
                # Get alert from queue with timeout
                try:
                    alert = await asyncio.wait_for(self.alert_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Process alert
                await self._handle_alert(alert)
                
                # Mark task as done
                self.alert_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"Error processing alert: {e}")
                await asyncio.sleep(1)
    
    async def _handle_alert(self, alert: Alert):
        """Handle individual alert."""
        try:
            # Check for alert correlation
            await self._correlate_alert(alert)
            
            # Send initial notifications
            await self._send_alert_notifications(alert)
            
            # Schedule escalation if needed
            await self._schedule_escalation(alert)
            
        except Exception as e:
            self.logger.error(f"Error handling alert {alert.alert_id}: {e}")
    
    async def _correlate_alert(self, alert: Alert):
        """Correlate alert with existing alerts."""
        try:
            # Simple correlation based on category and time window
            correlation_window_start = alert.timestamp - timedelta(seconds=self.correlation_window)
            
            related_alerts = []
            for existing_alert in self.active_alerts.values():
                if (existing_alert.alert_id != alert.alert_id and
                    existing_alert.category == alert.category and
                    existing_alert.timestamp >= correlation_window_start):
                    related_alerts.append(existing_alert.alert_id)
            
            if related_alerts:
                alert.related_alerts = related_alerts
                # Update related alerts to reference this one
                for related_id in related_alerts:
                    if related_id in self.active_alerts:
                        self.active_alerts[related_id].related_alerts.append(alert.alert_id)
                
                self.logger.info(f"Alert {alert.alert_id} correlated with {len(related_alerts)} alerts")
                
        except Exception as e:
            self.logger.error(f"Error correlating alert: {e}")
    
    async def _send_alert_notifications(self, alert: Alert):
        """Send notifications for alert."""
        try:
            # Find applicable notification configs
            applicable_configs = []
            
            for config_name, config in self.notification_configs.items():
                if (config.enabled and
                    (not config.severity_filter or alert.severity in config.severity_filter) and
                    (not config.category_filter or alert.category in config.category_filter)):
                    applicable_configs.append((config_name, config))
            
            # Send notifications
            for config_name, config in applicable_configs:
                if self._check_rate_limit(config_name, config):
                    await self._queue_notification(alert, config_name, config)
                
        except Exception as e:
            self.logger.error(f"Error sending alert notifications: {e}")
    
    def _check_rate_limit(self, config_name: str, config: NotificationConfig) -> bool:
        """Check if notification is within rate limit."""
        try:
            if not config.rate_limit:
                return True
            
            now = datetime.now()
            hour_ago = now - timedelta(hours=1)
            
            # Count notifications in last hour
            recent_notifications = [
                ts for ts in self.notification_rate_limits[config_name]
                if ts >= hour_ago
            ]
            
            if len(recent_notifications) >= config.rate_limit:
                self.logger.warning(f"Rate limit exceeded for {config_name}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error checking rate limit: {e}")
            return True
    
    async def _queue_notification(self, alert: Alert, config_name: str, config: NotificationConfig):
        """Queue notification for delivery."""
        try:
            notification_data = {
                'alert': alert,
                'config_name': config_name,
                'config': config,
                'timestamp': datetime.now()
            }
            
            await self.notification_queue.put(notification_data)
            
        except Exception as e:
            self.logger.error(f"Error queueing notification: {e}")
    
    async def _process_notifications(self):
        """Process notifications from queue."""
        while self.is_running:
            try:
                # Get notification from queue with timeout
                try:
                    notification_data = await asyncio.wait_for(self.notification_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Send notification
                await self._send_notification(notification_data)
                
                # Mark task as done
                self.notification_queue.task_done()
                
            except Exception as e:
                self.logger.error(f"Error processing notification: {e}")
                await asyncio.sleep(1)
    
    async def _send_notification(self, notification_data: Dict[str, Any]):
        """Send individual notification."""
        try:
            alert = notification_data['alert']
            config_name = notification_data['config_name']
            config = notification_data['config']
            
            start_time = time.time()
            notification_id = str(uuid.uuid4())
            
            success = False
            error_message = None
            
            try:
                if config.channel == NotificationChannel.CONSOLE:
                    success = await self._send_console_notification(alert, config)
                elif config.channel == NotificationChannel.FILE:
                    success = await self._send_file_notification(alert, config)
                elif config.channel == NotificationChannel.EMAIL:
                    success = await self._send_email_notification(alert, config)
                elif config.channel == NotificationChannel.SLACK:
                    success = await self._send_slack_notification(alert, config)
                elif config.channel == NotificationChannel.WEBHOOK:
                    success = await self._send_webhook_notification(alert, config)
                else:
                    self.logger.warning(f"Unsupported notification channel: {config.channel}")
                    success = False
                    error_message = f"Unsupported channel: {config.channel}"
                    
            except Exception as e:
                success = False
                error_message = str(e)
                self.logger.error(f"Error sending {config.channel.value} notification: {e}")
            
            # Record result
            delivery_time = (time.time() - start_time) * 1000  # Convert to milliseconds
            
            result = NotificationResult(
                notification_id=notification_id,
                alert_id=alert.alert_id,
                channel=config.channel,
                success=success,
                timestamp=datetime.now(),
                error_message=error_message,
                delivery_time_ms=delivery_time
            )
            
            self.notification_history.append(result)
            
            # Update metrics
            if success:
                self.alert_metrics['notifications_sent'] += 1
                # Update rate limit tracking
                self.notification_rate_limits[config_name].append(datetime.now())
            else:
                self.alert_metrics['notifications_failed'] += 1
            
            # Update alert notification count
            if alert.alert_id in self.active_alerts:
                self.active_alerts[alert.alert_id].notification_count += 1
                self.active_alerts[alert.alert_id].last_notification = datetime.now()
            
        except Exception as e:
            self.logger.error(f"Error in send notification: {e}")
    
    async def _send_console_notification(self, alert: Alert, config: NotificationConfig) -> bool:
        """Send console notification."""
        try:
            severity_colors = {
                AlertSeverity.INFO: "\033[94m",      # Blue
                AlertSeverity.WARNING: "\033[93m",   # Yellow
                AlertSeverity.CRITICAL: "\033[91m",  # Red
                AlertSeverity.EMERGENCY: "\033[95m"  # Magenta
            }
            
            reset_color = "\033[0m"
            color = severity_colors.get(alert.severity, "")
            
            message = f"{color}[{alert.severity.value.upper()}] {alert.title}: {alert.message}{reset_color}"
            print(message)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending console notification: {e}")
            return False
    
    async def _send_file_notification(self, alert: Alert, config: NotificationConfig) -> bool:
        """Send file notification."""
        try:
            filepath = config.config.get('filepath', 'alerts.log')
            format_type = config.config.get('format', 'text')
            
            # Ensure directory exists
            Path(filepath).parent.mkdir(parents=True, exist_ok=True)
            
            if format_type == 'json':
                alert_data = {
                    'timestamp': alert.timestamp.isoformat(),
                    'alert_id': alert.alert_id,
                    'severity': alert.severity.value,
                    'category': alert.category.value,
                    'title': alert.title,
                    'message': alert.message,
                    'source': alert.source,
                    'tags': alert.tags,
                    'metadata': alert.metadata
                }
                
                with open(filepath, 'a') as f:
                    f.write(json.dumps(alert_data) + '\n')
            else:
                # Text format
                timestamp_str = alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')
                line = f"[{timestamp_str}] [{alert.severity.value.upper()}] {alert.title}: {alert.message}\n"
                
                with open(filepath, 'a') as f:
                    f.write(line)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending file notification: {e}")
            return False
    
    async def _send_email_notification(self, alert: Alert, config: NotificationConfig) -> bool:
        """Send email notification."""
        try:
            smtp_server = config.config.get('smtp_server')
            smtp_port = config.config.get('smtp_port', 587)
            username = config.config.get('username')
            password = config.config.get('password')
            from_email = config.config.get('from_email')
            to_emails = config.config.get('to_emails', [])
            
            if not all([smtp_server, username, password, from_email, to_emails]):
                self.logger.error("Incomplete email configuration")
                return False
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = from_email
            msg['To'] = ', '.join(to_emails)
            msg['Subject'] = f"[{alert.severity.value.upper()}] {alert.title}"
            
            # Email body
            body = f"""
Alert Details:

Severity: {alert.severity.value.upper()}
Category: {alert.category.value}
Source: {alert.source}
Timestamp: {alert.timestamp.strftime('%Y-%m-%d %H:%M:%S')}

Message:
{alert.message}

Alert ID: {alert.alert_id}
"""
            
            if alert.tags:
                body += f"\nTags: {', '.join(alert.tags)}"
            
            if alert.metadata:
                body += f"\nMetadata: {json.dumps(alert.metadata, indent=2)}"
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(username, password)
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending email notification: {e}")
            return False
    
    async def _send_slack_notification(self, alert: Alert, config: NotificationConfig) -> bool:
        """Send Slack notification."""
        try:
            if not requests:
                self.logger.error("requests library not available for Slack notifications")
                return False
            
            webhook_url = config.config.get('webhook_url')
            if not webhook_url:
                self.logger.error("Slack webhook URL not configured")
                return False
            
            # Slack message colors
            color_map = {
                AlertSeverity.INFO: "good",
                AlertSeverity.WARNING: "warning",
                AlertSeverity.CRITICAL: "danger",
                AlertSeverity.EMERGENCY: "danger"
            }
            
            # Create Slack message
            payload = {
                "attachments": [
                    {
                        "color": color_map.get(alert.severity, "warning"),
                        "title": f"[{alert.severity.value.upper()}] {alert.title}",
                        "text": alert.message,
                        "fields": [
                            {
                                "title": "Category",
                                "value": alert.category.value,
                                "short": True
                            },
                            {
                                "title": "Source",
                                "value": alert.source,
                                "short": True
                            },
                            {
                                "title": "Alert ID",
                                "value": alert.alert_id,
                                "short": True
                            },
                            {
                                "title": "Timestamp",
                                "value": alert.timestamp.strftime('%Y-%m-%d %H:%M:%S'),
                                "short": True
                            }
                        ],
                        "footer": "AI Trading Agent",
                        "ts": int(alert.timestamp.timestamp())
                    }
                ]
            }
            
            response = requests.post(webhook_url, json=payload, timeout=10)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending Slack notification: {e}")
            return False
    
    async def _send_webhook_notification(self, alert: Alert, config: NotificationConfig) -> bool:
        """Send webhook notification."""
        try:
            if not requests:
                self.logger.error("requests library not available for webhook notifications")
                return False
            
            webhook_url = config.config.get('url')
            if not webhook_url:
                self.logger.error("Webhook URL not configured")
                return False
            
            # Create webhook payload
            payload = {
                "alert_id": alert.alert_id,
                "rule_id": alert.rule_id,
                "severity": alert.severity.value,
                "category": alert.category.value,
                "title": alert.title,
                "message": alert.message,
                "source": alert.source,
                "timestamp": alert.timestamp.isoformat(),
                "status": alert.status.value,
                "tags": alert.tags,
                "metadata": alert.metadata
            }
            
            headers = config.config.get('headers', {})
            timeout = config.config.get('timeout', 10)
            
            response = requests.post(webhook_url, json=payload, headers=headers, timeout=timeout)
            response.raise_for_status()
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error sending webhook notification: {e}")
            return False
    
    async def _schedule_escalation(self, alert: Alert):
        """Schedule alert escalation."""
        try:
            # Find applicable escalation rule
            escalation_rule = None
            for rule in self.escalation_rules.values():
                if (rule.enabled and
                    rule.severity == alert.severity and
                    rule.category == alert.category):
                    escalation_rule = rule
                    break
            
            if not escalation_rule:
                return
            
            # Schedule escalation task
            asyncio.create_task(self._handle_escalation(alert, escalation_rule))
            
        except Exception as e:
            self.logger.error(f"Error scheduling escalation: {e}")
    
    async def _handle_escalation(self, alert: Alert, escalation_rule: EscalationRule):
        """Handle alert escalation."""
        try:
            # Wait for escalation delay
            await asyncio.sleep(escalation_rule.escalation_delay)
            
            # Check if alert is still active and not acknowledged
            if (alert.alert_id not in self.active_alerts or
                alert.status in [AlertStatus.ACKNOWLEDGED, AlertStatus.RESOLVED]):
                return
            
            # Check if we've reached max escalations
            if alert.escalation_count >= escalation_rule.max_escalations:
                return
            
            # Escalate to next level
            current_level_index = list(escalation_rule.escalation_levels).index(alert.escalation_level)
            if current_level_index < len(escalation_rule.escalation_levels) - 1:
                next_level = escalation_rule.escalation_levels[current_level_index + 1]
                
                # Update alert
                alert.escalation_level = next_level
                alert.escalation_count += 1
                alert.last_escalation = datetime.now()
                
                # Send escalation notifications
                if next_level in escalation_rule.notification_configs:
                    for config in escalation_rule.notification_configs[next_level]:
                        if config and config.enabled:
                            await self._queue_notification(alert, f"escalation_{next_level.value}", config)
                
                self.alert_metrics['escalations_triggered'] += 1
                self.logger.warning(f"Alert {alert.alert_id} escalated to {next_level.value}")
                
                # Schedule next escalation if needed
                if alert.escalation_count < escalation_rule.max_escalations:
                    asyncio.create_task(self._handle_escalation(alert, escalation_rule))
            
        except Exception as e:
            self.logger.error(f"Error handling escalation: {e}")
    
    async def _monitor_escalations(self):
        """Monitor and handle escalations."""
        while self.is_running:
            try:
                # This is handled by individual escalation tasks
                # This method can be used for additional escalation monitoring
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error monitoring escalations: {e}")
                await asyncio.sleep(60)
    
    async def _cleanup_old_data(self):
        """Clean up old alert and notification data."""
        while self.is_running:
            try:
                cutoff_date = datetime.now() - timedelta(days=self.retention_days)
                
                # Clean up alert history
                self.alert_history = [a for a in self.alert_history if a.timestamp >= cutoff_date]
                
                # Clean up notification history
                self.notification_history = [n for n in self.notification_history if n.timestamp >= cutoff_date]
                
                # Clean up resolved alerts from active alerts
                resolved_alerts = [
                    alert_id for alert_id, alert in self.active_alerts.items()
                    if alert.status == AlertStatus.RESOLVED and
                    alert.resolved_at and alert.resolved_at < cutoff_date
                ]
                
                for alert_id in resolved_alerts:
                    del self.active_alerts[alert_id]
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                self.logger.error(f"Error cleaning up old data: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    # Public interface methods
    async def acknowledge_alert(self, alert_id: str, acknowledged_by: str) -> bool:
        """Acknowledge alert."""
        try:
            if alert_id not in self.active_alerts:
                return False
            
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.ACKNOWLEDGED
            alert.acknowledged_by = acknowledged_by
            alert.acknowledged_at = datetime.now()
            
            self.logger.info(f"Alert acknowledged: {alert_id} by {acknowledged_by}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str, resolved_by: Optional[str] = None) -> bool:
        """Resolve alert."""
        try:
            if alert_id not in self.active_alerts:
                return False
            
            alert = self.active_alerts[alert_id]
            alert.status = AlertStatus.RESOLVED
            alert.resolved_at = datetime.now()
            
            # Calculate resolution time for metrics
            resolution_time = (alert.resolved_at - alert.timestamp).total_seconds()
            
            # Update average resolution time
            if self.alert_metrics['avg_resolution_time'] == 0:
                self.alert_metrics['avg_resolution_time'] = resolution_time
            else:
                # Simple moving average
                self.alert_metrics['avg_resolution_time'] = (
                    self.alert_metrics['avg_resolution_time'] * 0.9 + resolution_time * 0.1
                )
            
            self.logger.info(f"Alert resolved: {alert_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False
    
    async def suppress_alerts(self, rule_ids: List[str], duration_minutes: int) -> bool:
        """Suppress alerts for specified rules."""
        try:
            for rule_id in rule_ids:
                self.suppressed_alerts.add(rule_id)
            
            # Schedule unsuppression
            asyncio.create_task(self._unsuppress_alerts_after_delay(rule_ids, duration_minutes * 60))
            
            self.logger.info(f"Alerts suppressed for {duration_minutes} minutes: {rule_ids}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error suppressing alerts: {e}")
            return False
    
    async def _unsuppress_alerts_after_delay(self, rule_ids: List[str], delay_seconds: int):
        """Unsuppress alerts after delay."""
        try:
            await asyncio.sleep(delay_seconds)
            
            for rule_id in rule_ids:
                self.suppressed_alerts.discard(rule_id)
            
            self.logger.info(f"Alert suppression lifted: {rule_ids}")
            
        except Exception as e:
            self.logger.error(f"Error unsuppressing alerts: {e}")
    
    async def get_alert_status(self) -> Dict[str, Any]:
        """Get current alert status."""
        try:
            active_by_severity = defaultdict(int)
            active_by_category = defaultdict(int)
            
            for alert in self.active_alerts.values():
                active_by_severity[alert.severity.value] += 1
                active_by_category[alert.category.value] += 1
            
            return {
                'timestamp': datetime.now().isoformat(),
                'active_alerts': len(self.active_alerts),
                'active_by_severity': dict(active_by_severity),
                'active_by_category': dict(active_by_category),
                'suppressed_rules': list(self.suppressed_alerts),
                'total_metrics': self.alert_metrics,
                'notification_channels': len(self.notification_configs),
                'escalation_rules': len(self.escalation_rules)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting alert status: {e}")
            return {}
    
    async def get_active_alerts(self, severity: Optional[AlertSeverity] = None,
                              category: Optional[AlertCategory] = None) -> List[Dict[str, Any]]:
        """Get active alerts with optional filtering."""
        try:
            alerts = list(self.active_alerts.values())
            
            if severity:
                alerts = [a for a in alerts if a.severity == severity]
            
            if category:
                alerts = [a for a in alerts if a.category == category]
            
            return [
                {
                    'alert_id': alert.alert_id,
                    'rule_id': alert.rule_id,
                    'severity': alert.severity.value,
                    'category': alert.category.value,
                    'title': alert.title,
                    'message': alert.message,
                    'source': alert.source,
                    'timestamp': alert.timestamp.isoformat(),
                    'status': alert.status.value,
                    'escalation_level': alert.escalation_level.value,
                    'escalation_count': alert.escalation_count,
                    'notification_count': alert.notification_count,
                    'acknowledged_by': alert.acknowledged_by,
                    'tags': alert.tags,
                    'related_alerts': alert.related_alerts
                }
                for alert in sorted(alerts, key=lambda x: x.timestamp, reverse=True)
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting active alerts: {e}")
            return []
    
    async def add_alert_rule(self, rule: AlertRule) -> bool:
        """Add new alert rule."""
        try:
            self.alert_rules[rule.rule_id] = rule
            self.logger.info(f"Alert rule added: {rule.rule_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error adding alert rule: {e}")
            return False
    
    async def update_alert_rule(self, rule_id: str, updates: Dict[str, Any]) -> bool:
        """Update existing alert rule."""
        try:
            if rule_id not in self.alert_rules:
                return False
            
            rule = self.alert_rules[rule_id]
            
            for key, value in updates.items():
                if hasattr(rule, key):
                    setattr(rule, key, value)
            
            self.logger.info(f"Alert rule updated: {rule_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating alert rule: {e}")
            return False
    
    async def export_alert_data(self, filepath: str, hours: int = 24) -> bool:
        """Export alert data to file."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'period_hours': hours,
                'alert_rules': {
                    rule_id: {
                        'name': rule.name,
                        'category': rule.category.value,
                        'severity': rule.severity.value,
                        'condition': rule.condition,
                        'threshold': rule.threshold,
                        'enabled': rule.enabled
                    }
                    for rule_id, rule in self.alert_rules.items()
                },
                'recent_alerts': [
                    {
                        'alert_id': alert.alert_id,
                        'rule_id': alert.rule_id,
                        'severity': alert.severity.value,
                        'category': alert.category.value,
                        'title': alert.title,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'status': alert.status.value,
                        'escalation_count': alert.escalation_count,
                        'notification_count': alert.notification_count
                    }
                    for alert in self.alert_history if alert.timestamp >= cutoff_time
                ],
                'notification_summary': {
                    'total_sent': len([n for n in self.notification_history if n.timestamp >= cutoff_time and n.success]),
                    'total_failed': len([n for n in self.notification_history if n.timestamp >= cutoff_time and not n.success]),
                    'by_channel': {}
                },
                'metrics': self.alert_metrics
            }
            
            # Calculate notification summary by channel
            for notification in self.notification_history:
                if notification.timestamp >= cutoff_time:
                    channel = notification.channel.value
                    if channel not in export_data['notification_summary']['by_channel']:
                        export_data['notification_summary']['by_channel'][channel] = {'sent': 0, 'failed': 0}
                    
                    if notification.success:
                        export_data['notification_summary']['by_channel'][channel]['sent'] += 1
                    else:
                        export_data['notification_summary']['by_channel'][channel]['failed'] += 1
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.logger.info(f"Alert data exported to: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting alert data: {e}")
            return False