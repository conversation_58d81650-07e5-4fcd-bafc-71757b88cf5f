"""Performance Monitoring System for AI Trading Agent.

This module provides comprehensive performance tracking, analysis,
and reporting capabilities for the AI trading system.

Author: inkbytefo
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
from pathlib import Path
import numpy as np


class MetricType(Enum):
    """Types of performance metrics."""
    RETURN = "return"
    RISK = "risk"
    EFFICIENCY = "efficiency"
    EXECUTION = "execution"
    SYSTEM = "system"
    AI_MODEL = "ai_model"


class TimeFrame(Enum):
    """Time frames for performance analysis."""
    MINUTE = "1m"
    HOUR = "1h"
    DAY = "1d"
    WEEK = "1w"
    MONTH = "1M"
    QUARTER = "3M"
    YEAR = "1Y"


class PerformanceLevel(Enum):
    """Performance level classifications."""
    EXCELLENT = "excellent"
    GOOD = "good"
    AVERAGE = "average"
    POOR = "poor"
    CRITICAL = "critical"


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    metric_type: MetricType
    timestamp: datetime
    timeframe: TimeFrame
    metadata: Dict[str, Any] = field(default_factory=dict)
    benchmark: Optional[float] = None
    target: Optional[float] = None


@dataclass
class PerformanceSnapshot:
    """Performance snapshot at a point in time."""
    timestamp: datetime
    portfolio_value: float
    total_return: float
    daily_return: float
    sharpe_ratio: float
    max_drawdown: float
    win_rate: float
    profit_factor: float
    total_trades: int
    active_positions: int
    cash_balance: float
    metrics: Dict[str, float] = field(default_factory=dict)


@dataclass
class TradePerformance:
    """Individual trade performance analysis."""
    trade_id: str
    symbol: str
    side: str
    entry_price: float
    exit_price: float
    quantity: float
    entry_time: datetime
    exit_time: datetime
    duration: timedelta
    pnl: float
    pnl_pct: float
    fees: float
    slippage: float
    execution_quality: float
    ai_confidence: Optional[float] = None
    market_conditions: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceReport:
    """Comprehensive performance report."""
    period_start: datetime
    period_end: datetime
    timeframe: TimeFrame
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    calmar_ratio: float
    win_rate: float
    profit_factor: float
    average_win: float
    average_loss: float
    largest_win: float
    largest_loss: float
    total_trades: int
    winning_trades: int
    losing_trades: int
    total_fees: float
    total_slippage: float
    benchmark_return: Optional[float] = None
    alpha: Optional[float] = None
    beta: Optional[float] = None
    information_ratio: Optional[float] = None
    metrics: Dict[str, Any] = field(default_factory=dict)


class PerformanceMonitor:
    """Comprehensive performance monitoring system."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize performance monitor."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Performance tracking
        self.snapshots: List[PerformanceSnapshot] = []
        self.trade_history: List[TradePerformance] = []
        self.metrics_history: Dict[str, List[PerformanceMetric]] = defaultdict(list)
        
        # Real-time metrics
        self.current_metrics: Dict[str, float] = {}
        self.rolling_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        
        # Benchmarks and targets
        self.benchmarks: Dict[str, float] = config.get('benchmarks', {})
        self.targets: Dict[str, float] = config.get('targets', {})
        
        # Performance thresholds
        self.performance_thresholds = {
            'sharpe_ratio': {'excellent': 2.0, 'good': 1.5, 'average': 1.0, 'poor': 0.5},
            'max_drawdown': {'excellent': 0.05, 'good': 0.10, 'average': 0.15, 'poor': 0.25},
            'win_rate': {'excellent': 0.70, 'good': 0.60, 'average': 0.50, 'poor': 0.40},
            'profit_factor': {'excellent': 2.0, 'good': 1.5, 'average': 1.2, 'poor': 1.0}
        }
        
        # Analysis settings
        self.analysis_window = config.get('analysis_window', 252)  # Trading days
        self.risk_free_rate = config.get('risk_free_rate', 0.02)  # 2% annual
        self.benchmark_symbol = config.get('benchmark_symbol', 'SPY')
        
        # Reporting settings
        self.report_frequency = config.get('report_frequency', 'daily')
        self.auto_reports = config.get('auto_reports', True)
        self.report_directory = Path(config.get('report_directory', 'reports'))
        self.report_directory.mkdir(exist_ok=True)
        
        # Performance alerts
        self.alert_thresholds = config.get('alert_thresholds', {
            'drawdown_limit': 0.20,
            'daily_loss_limit': 0.05,
            'sharpe_decline': 0.30,
            'win_rate_decline': 0.20
        })
        
        # AI model performance tracking
        self.ai_performance = {
            'prediction_accuracy': deque(maxlen=1000),
            'signal_quality': deque(maxlen=1000),
            'model_confidence': deque(maxlen=1000),
            'feature_importance': {},
            'model_drift': deque(maxlen=100)
        }
        
        self.logger.info("PerformanceMonitor initialized")
    
    async def start_monitoring(self):
        """Start performance monitoring."""
        try:
            # Start monitoring tasks
            asyncio.create_task(self._collect_metrics())
            asyncio.create_task(self._generate_reports())
            asyncio.create_task(self._monitor_alerts())
            
            self.logger.info("Performance monitoring started")
            
        except Exception as e:
            self.logger.error(f"Error starting performance monitoring: {e}")
            raise
    
    async def record_trade(self, trade_data: Dict[str, Any]):
        """Record a completed trade for performance analysis."""
        try:
            trade_performance = TradePerformance(
                trade_id=trade_data['trade_id'],
                symbol=trade_data['symbol'],
                side=trade_data['side'],
                entry_price=trade_data['entry_price'],
                exit_price=trade_data['exit_price'],
                quantity=trade_data['quantity'],
                entry_time=trade_data['entry_time'],
                exit_time=trade_data['exit_time'],
                duration=trade_data['exit_time'] - trade_data['entry_time'],
                pnl=trade_data['pnl'],
                pnl_pct=trade_data['pnl_pct'],
                fees=trade_data.get('fees', 0.0),
                slippage=trade_data.get('slippage', 0.0),
                execution_quality=trade_data.get('execution_quality', 1.0),
                ai_confidence=trade_data.get('ai_confidence'),
                market_conditions=trade_data.get('market_conditions', {})
            )
            
            self.trade_history.append(trade_performance)
            
            # Update rolling metrics
            self.rolling_metrics['pnl'].append(trade_performance.pnl)
            self.rolling_metrics['pnl_pct'].append(trade_performance.pnl_pct)
            self.rolling_metrics['duration'].append(trade_performance.duration.total_seconds())
            
            # Update current metrics
            await self._update_trade_metrics()
            
            self.logger.debug(f"Trade recorded: {trade_performance.trade_id}")
            
        except Exception as e:
            self.logger.error(f"Error recording trade: {e}")
    
    async def record_portfolio_snapshot(self, portfolio_data: Dict[str, Any]):
        """Record a portfolio snapshot."""
        try:
            # Calculate performance metrics
            total_return = self._calculate_total_return(portfolio_data['portfolio_value'])
            daily_return = self._calculate_daily_return(portfolio_data['portfolio_value'])
            sharpe_ratio = self._calculate_sharpe_ratio()
            max_drawdown = self._calculate_max_drawdown()
            win_rate = self._calculate_win_rate()
            profit_factor = self._calculate_profit_factor()
            
            snapshot = PerformanceSnapshot(
                timestamp=datetime.now(),
                portfolio_value=portfolio_data['portfolio_value'],
                total_return=total_return,
                daily_return=daily_return,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                win_rate=win_rate,
                profit_factor=profit_factor,
                total_trades=len(self.trade_history),
                active_positions=portfolio_data.get('active_positions', 0),
                cash_balance=portfolio_data.get('cash_balance', 0.0),
                metrics=portfolio_data.get('metrics', {})
            )
            
            self.snapshots.append(snapshot)
            
            # Update current metrics
            self.current_metrics.update({
                'portfolio_value': snapshot.portfolio_value,
                'total_return': snapshot.total_return,
                'daily_return': snapshot.daily_return,
                'sharpe_ratio': snapshot.sharpe_ratio,
                'max_drawdown': snapshot.max_drawdown,
                'win_rate': snapshot.win_rate,
                'profit_factor': snapshot.profit_factor
            })
            
            # Keep only recent snapshots
            if len(self.snapshots) > 10000:
                self.snapshots = self.snapshots[-5000:]
            
            self.logger.debug(f"Portfolio snapshot recorded: {snapshot.portfolio_value}")
            
        except Exception as e:
            self.logger.error(f"Error recording portfolio snapshot: {e}")
    
    async def record_ai_performance(self, ai_data: Dict[str, Any]):
        """Record AI model performance metrics."""
        try:
            # Record prediction accuracy
            if 'prediction_accuracy' in ai_data:
                self.ai_performance['prediction_accuracy'].append(ai_data['prediction_accuracy'])
            
            # Record signal quality
            if 'signal_quality' in ai_data:
                self.ai_performance['signal_quality'].append(ai_data['signal_quality'])
            
            # Record model confidence
            if 'model_confidence' in ai_data:
                self.ai_performance['model_confidence'].append(ai_data['model_confidence'])
            
            # Update feature importance
            if 'feature_importance' in ai_data:
                self.ai_performance['feature_importance'].update(ai_data['feature_importance'])
            
            # Record model drift
            if 'model_drift' in ai_data:
                self.ai_performance['model_drift'].append(ai_data['model_drift'])
            
            self.logger.debug("AI performance metrics recorded")
            
        except Exception as e:
            self.logger.error(f"Error recording AI performance: {e}")
    
    async def record_metric(self, name: str, value: float, metric_type: MetricType, 
                          timeframe: TimeFrame = TimeFrame.MINUTE, metadata: Dict[str, Any] = None):
        """Record a custom performance metric."""
        try:
            metric = PerformanceMetric(
                name=name,
                value=value,
                metric_type=metric_type,
                timestamp=datetime.now(),
                timeframe=timeframe,
                metadata=metadata or {},
                benchmark=self.benchmarks.get(name),
                target=self.targets.get(name)
            )
            
            self.metrics_history[name].append(metric)
            self.current_metrics[name] = value
            
            # Keep only recent metrics
            if len(self.metrics_history[name]) > 10000:
                self.metrics_history[name] = self.metrics_history[name][-5000:]
            
            self.logger.debug(f"Metric recorded: {name}={value}")
            
        except Exception as e:
            self.logger.error(f"Error recording metric: {e}")
    
    def _calculate_total_return(self, current_value: float) -> float:
        """Calculate total return since inception."""
        try:
            if not self.snapshots:
                return 0.0
            
            initial_value = self.snapshots[0].portfolio_value
            if initial_value == 0:
                return 0.0
            
            return (current_value - initial_value) / initial_value
            
        except Exception as e:
            self.logger.error(f"Error calculating total return: {e}")
            return 0.0
    
    def _calculate_daily_return(self, current_value: float) -> float:
        """Calculate daily return."""
        try:
            if len(self.snapshots) < 2:
                return 0.0
            
            # Find snapshot from 24 hours ago
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_snapshot = None
            
            for snapshot in reversed(self.snapshots):
                if snapshot.timestamp <= yesterday:
                    yesterday_snapshot = snapshot
                    break
            
            if not yesterday_snapshot:
                return 0.0
            
            if yesterday_snapshot.portfolio_value == 0:
                return 0.0
            
            return (current_value - yesterday_snapshot.portfolio_value) / yesterday_snapshot.portfolio_value
            
        except Exception as e:
            self.logger.error(f"Error calculating daily return: {e}")
            return 0.0
    
    def _calculate_sharpe_ratio(self, window: int = 252) -> float:
        """Calculate Sharpe ratio."""
        try:
            if len(self.snapshots) < 30:
                return 0.0
            
            # Get recent returns
            recent_snapshots = self.snapshots[-window:]
            returns = []
            
            for i in range(1, len(recent_snapshots)):
                prev_value = recent_snapshots[i-1].portfolio_value
                curr_value = recent_snapshots[i].portfolio_value
                
                if prev_value > 0:
                    daily_return = (curr_value - prev_value) / prev_value
                    returns.append(daily_return)
            
            if len(returns) < 10:
                return 0.0
            
            # Calculate Sharpe ratio
            mean_return = statistics.mean(returns)
            std_return = statistics.stdev(returns) if len(returns) > 1 else 0.0
            
            if std_return == 0:
                return 0.0
            
            # Annualize
            annual_return = mean_return * 252
            annual_std = std_return * (252 ** 0.5)
            
            return (annual_return - self.risk_free_rate) / annual_std
            
        except Exception as e:
            self.logger.error(f"Error calculating Sharpe ratio: {e}")
            return 0.0
    
    def _calculate_max_drawdown(self) -> float:
        """Calculate maximum drawdown."""
        try:
            if len(self.snapshots) < 2:
                return 0.0
            
            values = [s.portfolio_value for s in self.snapshots]
            peak = values[0]
            max_dd = 0.0
            
            for value in values:
                if value > peak:
                    peak = value
                
                drawdown = (peak - value) / peak if peak > 0 else 0.0
                max_dd = max(max_dd, drawdown)
            
            return max_dd
            
        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    def _calculate_win_rate(self) -> float:
        """Calculate win rate from recent trades."""
        try:
            if not self.trade_history:
                return 0.0
            
            winning_trades = sum(1 for trade in self.trade_history if trade.pnl > 0)
            return winning_trades / len(self.trade_history)
            
        except Exception as e:
            self.logger.error(f"Error calculating win rate: {e}")
            return 0.0
    
    def _calculate_profit_factor(self) -> float:
        """Calculate profit factor."""
        try:
            if not self.trade_history:
                return 0.0
            
            gross_profit = sum(trade.pnl for trade in self.trade_history if trade.pnl > 0)
            gross_loss = abs(sum(trade.pnl for trade in self.trade_history if trade.pnl < 0))
            
            if gross_loss == 0:
                return float('inf') if gross_profit > 0 else 0.0
            
            return gross_profit / gross_loss
            
        except Exception as e:
            self.logger.error(f"Error calculating profit factor: {e}")
            return 0.0
    
    async def _update_trade_metrics(self):
        """Update trade-based metrics."""
        try:
            if not self.trade_history:
                return
            
            recent_trades = self.trade_history[-100:]  # Last 100 trades
            
            # Calculate metrics
            total_pnl = sum(trade.pnl for trade in recent_trades)
            winning_trades = [trade for trade in recent_trades if trade.pnl > 0]
            losing_trades = [trade for trade in recent_trades if trade.pnl < 0]
            
            metrics = {
                'recent_total_pnl': total_pnl,
                'recent_win_rate': len(winning_trades) / len(recent_trades),
                'recent_avg_win': statistics.mean([t.pnl for t in winning_trades]) if winning_trades else 0.0,
                'recent_avg_loss': statistics.mean([t.pnl for t in losing_trades]) if losing_trades else 0.0,
                'recent_avg_duration': statistics.mean([t.duration.total_seconds() for t in recent_trades]),
                'recent_total_fees': sum(trade.fees for trade in recent_trades),
                'recent_avg_slippage': statistics.mean([trade.slippage for trade in recent_trades])
            }
            
            self.current_metrics.update(metrics)
            
        except Exception as e:
            self.logger.error(f"Error updating trade metrics: {e}")
    
    async def _collect_metrics(self):
        """Periodically collect and update metrics."""
        while True:
            try:
                # Update AI performance metrics
                if self.ai_performance['prediction_accuracy']:
                    avg_accuracy = statistics.mean(list(self.ai_performance['prediction_accuracy']))
                    await self.record_metric('ai_prediction_accuracy', avg_accuracy, MetricType.AI_MODEL)
                
                if self.ai_performance['signal_quality']:
                    avg_quality = statistics.mean(list(self.ai_performance['signal_quality']))
                    await self.record_metric('ai_signal_quality', avg_quality, MetricType.AI_MODEL)
                
                # Update system metrics
                await self._collect_system_metrics()
                
                await asyncio.sleep(60)  # Collect every minute
                
            except Exception as e:
                self.logger.error(f"Error collecting metrics: {e}")
                await asyncio.sleep(60)
    
    async def _collect_system_metrics(self):
        """Collect system performance metrics."""
        try:
            import psutil
            
            # CPU and memory usage
            cpu_percent = psutil.cpu_percent()
            memory_percent = psutil.virtual_memory().percent
            
            await self.record_metric('system_cpu_usage', cpu_percent, MetricType.SYSTEM)
            await self.record_metric('system_memory_usage', memory_percent, MetricType.SYSTEM)
            
        except ImportError:
            # psutil not available
            pass
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
    
    async def _generate_reports(self):
        """Periodically generate performance reports."""
        while True:
            try:
                if self.auto_reports:
                    # Generate daily report
                    if self.report_frequency == 'daily':
                        await self.generate_performance_report(TimeFrame.DAY)
                        await asyncio.sleep(24 * 3600)  # Daily
                    elif self.report_frequency == 'hourly':
                        await self.generate_performance_report(TimeFrame.HOUR)
                        await asyncio.sleep(3600)  # Hourly
                    else:
                        await asyncio.sleep(3600)  # Default hourly check
                else:
                    await asyncio.sleep(3600)
                    
            except Exception as e:
                self.logger.error(f"Error generating reports: {e}")
                await asyncio.sleep(3600)
    
    async def _monitor_alerts(self):
        """Monitor for performance alerts."""
        while True:
            try:
                # Check drawdown alert
                current_dd = self.current_metrics.get('max_drawdown', 0.0)
                if current_dd > self.alert_thresholds['drawdown_limit']:
                    await self._trigger_alert('drawdown_limit_exceeded', {
                        'current_drawdown': current_dd,
                        'limit': self.alert_thresholds['drawdown_limit']
                    })
                
                # Check daily loss alert
                daily_return = self.current_metrics.get('daily_return', 0.0)
                if daily_return < -self.alert_thresholds['daily_loss_limit']:
                    await self._trigger_alert('daily_loss_limit_exceeded', {
                        'daily_return': daily_return,
                        'limit': self.alert_thresholds['daily_loss_limit']
                    })
                
                # Check Sharpe ratio decline
                current_sharpe = self.current_metrics.get('sharpe_ratio', 0.0)
                if len(self.snapshots) > 100:
                    avg_sharpe_30d = statistics.mean([s.sharpe_ratio for s in self.snapshots[-30:]])
                    avg_sharpe_90d = statistics.mean([s.sharpe_ratio for s in self.snapshots[-90:]])
                    
                    if avg_sharpe_90d > 0 and (avg_sharpe_30d / avg_sharpe_90d) < (1 - self.alert_thresholds['sharpe_decline']):
                        await self._trigger_alert('sharpe_ratio_decline', {
                            'current_30d': avg_sharpe_30d,
                            'previous_90d': avg_sharpe_90d,
                            'decline_pct': 1 - (avg_sharpe_30d / avg_sharpe_90d)
                        })
                
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error monitoring alerts: {e}")
                await asyncio.sleep(300)
    
    async def _trigger_alert(self, alert_type: str, data: Dict[str, Any]):
        """Trigger a performance alert."""
        try:
            alert_message = {
                'type': alert_type,
                'timestamp': datetime.now().isoformat(),
                'data': data,
                'severity': 'high' if 'limit_exceeded' in alert_type else 'medium'
            }
            
            self.logger.warning(f"Performance alert: {alert_type} - {data}")
            
            # Here you could integrate with external alerting systems
            # like email, Slack, Discord, etc.
            
        except Exception as e:
            self.logger.error(f"Error triggering alert: {e}")
    
    async def generate_performance_report(self, timeframe: TimeFrame) -> PerformanceReport:
        """Generate a comprehensive performance report."""
        try:
            # Determine time period
            end_time = datetime.now()
            
            if timeframe == TimeFrame.DAY:
                start_time = end_time - timedelta(days=1)
            elif timeframe == TimeFrame.WEEK:
                start_time = end_time - timedelta(weeks=1)
            elif timeframe == TimeFrame.MONTH:
                start_time = end_time - timedelta(days=30)
            elif timeframe == TimeFrame.QUARTER:
                start_time = end_time - timedelta(days=90)
            elif timeframe == TimeFrame.YEAR:
                start_time = end_time - timedelta(days=365)
            else:
                start_time = end_time - timedelta(hours=1)
            
            # Filter data for the period
            period_snapshots = [s for s in self.snapshots if start_time <= s.timestamp <= end_time]
            period_trades = [t for t in self.trade_history if start_time <= t.exit_time <= end_time]
            
            if not period_snapshots or not period_trades:
                self.logger.warning(f"Insufficient data for {timeframe.value} report")
                return None
            
            # Calculate performance metrics
            start_value = period_snapshots[0].portfolio_value
            end_value = period_snapshots[-1].portfolio_value
            
            total_return = (end_value - start_value) / start_value if start_value > 0 else 0.0
            
            # Annualize return
            days = (end_time - start_time).days
            annualized_return = ((1 + total_return) ** (365 / days)) - 1 if days > 0 else 0.0
            
            # Calculate volatility
            returns = []
            for i in range(1, len(period_snapshots)):
                prev_val = period_snapshots[i-1].portfolio_value
                curr_val = period_snapshots[i].portfolio_value
                if prev_val > 0:
                    returns.append((curr_val - prev_val) / prev_val)
            
            volatility = statistics.stdev(returns) * (252 ** 0.5) if len(returns) > 1 else 0.0
            
            # Calculate ratios
            sharpe_ratio = (annualized_return - self.risk_free_rate) / volatility if volatility > 0 else 0.0
            
            # Sortino ratio (downside deviation)
            downside_returns = [r for r in returns if r < 0]
            downside_std = statistics.stdev(downside_returns) * (252 ** 0.5) if len(downside_returns) > 1 else 0.0
            sortino_ratio = (annualized_return - self.risk_free_rate) / downside_std if downside_std > 0 else 0.0
            
            # Max drawdown for period
            period_values = [s.portfolio_value for s in period_snapshots]
            peak = period_values[0]
            max_drawdown = 0.0
            
            for value in period_values:
                if value > peak:
                    peak = value
                drawdown = (peak - value) / peak if peak > 0 else 0.0
                max_drawdown = max(max_drawdown, drawdown)
            
            # Calmar ratio
            calmar_ratio = annualized_return / max_drawdown if max_drawdown > 0 else 0.0
            
            # Trade statistics
            winning_trades = [t for t in period_trades if t.pnl > 0]
            losing_trades = [t for t in period_trades if t.pnl < 0]
            
            win_rate = len(winning_trades) / len(period_trades) if period_trades else 0.0
            
            gross_profit = sum(t.pnl for t in winning_trades)
            gross_loss = abs(sum(t.pnl for t in losing_trades))
            profit_factor = gross_profit / gross_loss if gross_loss > 0 else 0.0
            
            average_win = statistics.mean([t.pnl for t in winning_trades]) if winning_trades else 0.0
            average_loss = statistics.mean([t.pnl for t in losing_trades]) if losing_trades else 0.0
            
            largest_win = max([t.pnl for t in winning_trades]) if winning_trades else 0.0
            largest_loss = min([t.pnl for t in losing_trades]) if losing_trades else 0.0
            
            total_fees = sum(t.fees for t in period_trades)
            total_slippage = sum(t.slippage for t in period_trades)
            
            # Create report
            report = PerformanceReport(
                period_start=start_time,
                period_end=end_time,
                timeframe=timeframe,
                total_return=total_return,
                annualized_return=annualized_return,
                volatility=volatility,
                sharpe_ratio=sharpe_ratio,
                sortino_ratio=sortino_ratio,
                max_drawdown=max_drawdown,
                calmar_ratio=calmar_ratio,
                win_rate=win_rate,
                profit_factor=profit_factor,
                average_win=average_win,
                average_loss=average_loss,
                largest_win=largest_win,
                largest_loss=largest_loss,
                total_trades=len(period_trades),
                winning_trades=len(winning_trades),
                losing_trades=len(losing_trades),
                total_fees=total_fees,
                total_slippage=total_slippage
            )
            
            # Save report
            if self.auto_reports:
                await self._save_report(report)
            
            self.logger.info(f"Performance report generated for {timeframe.value}")
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating performance report: {e}")
            return None
    
    async def _save_report(self, report: PerformanceReport):
        """Save performance report to file."""
        try:
            timestamp = report.period_end.strftime('%Y%m%d_%H%M%S')
            filename = f"performance_report_{report.timeframe.value}_{timestamp}.json"
            filepath = self.report_directory / filename
            
            report_data = {
                'period_start': report.period_start.isoformat(),
                'period_end': report.period_end.isoformat(),
                'timeframe': report.timeframe.value,
                'total_return': report.total_return,
                'annualized_return': report.annualized_return,
                'volatility': report.volatility,
                'sharpe_ratio': report.sharpe_ratio,
                'sortino_ratio': report.sortino_ratio,
                'max_drawdown': report.max_drawdown,
                'calmar_ratio': report.calmar_ratio,
                'win_rate': report.win_rate,
                'profit_factor': report.profit_factor,
                'average_win': report.average_win,
                'average_loss': report.average_loss,
                'largest_win': report.largest_win,
                'largest_loss': report.largest_loss,
                'total_trades': report.total_trades,
                'winning_trades': report.winning_trades,
                'losing_trades': report.losing_trades,
                'total_fees': report.total_fees,
                'total_slippage': report.total_slippage,
                'metrics': report.metrics
            }
            
            with open(filepath, 'w') as f:
                json.dump(report_data, f, indent=2)
            
            self.logger.info(f"Report saved: {filepath}")
            
        except Exception as e:
            self.logger.error(f"Error saving report: {e}")
    
    def get_performance_level(self, metric_name: str, value: float) -> PerformanceLevel:
        """Classify performance level for a metric."""
        try:
            if metric_name not in self.performance_thresholds:
                return PerformanceLevel.AVERAGE
            
            thresholds = self.performance_thresholds[metric_name]
            
            # For metrics where higher is better
            if metric_name in ['sharpe_ratio', 'win_rate', 'profit_factor']:
                if value >= thresholds['excellent']:
                    return PerformanceLevel.EXCELLENT
                elif value >= thresholds['good']:
                    return PerformanceLevel.GOOD
                elif value >= thresholds['average']:
                    return PerformanceLevel.AVERAGE
                elif value >= thresholds['poor']:
                    return PerformanceLevel.POOR
                else:
                    return PerformanceLevel.CRITICAL
            
            # For metrics where lower is better (like max_drawdown)
            else:
                if value <= thresholds['excellent']:
                    return PerformanceLevel.EXCELLENT
                elif value <= thresholds['good']:
                    return PerformanceLevel.GOOD
                elif value <= thresholds['average']:
                    return PerformanceLevel.AVERAGE
                elif value <= thresholds['poor']:
                    return PerformanceLevel.POOR
                else:
                    return PerformanceLevel.CRITICAL
                    
        except Exception as e:
            self.logger.error(f"Error classifying performance level: {e}")
            return PerformanceLevel.AVERAGE
    
    async def get_current_performance(self) -> Dict[str, Any]:
        """Get current performance summary."""
        try:
            if not self.snapshots:
                return {}
            
            latest_snapshot = self.snapshots[-1]
            
            # Get performance levels
            sharpe_level = self.get_performance_level('sharpe_ratio', latest_snapshot.sharpe_ratio)
            drawdown_level = self.get_performance_level('max_drawdown', latest_snapshot.max_drawdown)
            win_rate_level = self.get_performance_level('win_rate', latest_snapshot.win_rate)
            profit_factor_level = self.get_performance_level('profit_factor', latest_snapshot.profit_factor)
            
            return {
                'timestamp': latest_snapshot.timestamp.isoformat(),
                'portfolio_value': latest_snapshot.portfolio_value,
                'total_return': latest_snapshot.total_return,
                'daily_return': latest_snapshot.daily_return,
                'sharpe_ratio': latest_snapshot.sharpe_ratio,
                'sharpe_level': sharpe_level.value,
                'max_drawdown': latest_snapshot.max_drawdown,
                'drawdown_level': drawdown_level.value,
                'win_rate': latest_snapshot.win_rate,
                'win_rate_level': win_rate_level.value,
                'profit_factor': latest_snapshot.profit_factor,
                'profit_factor_level': profit_factor_level.value,
                'total_trades': latest_snapshot.total_trades,
                'active_positions': latest_snapshot.active_positions,
                'ai_metrics': {
                    'prediction_accuracy': statistics.mean(list(self.ai_performance['prediction_accuracy'])) if self.ai_performance['prediction_accuracy'] else 0.0,
                    'signal_quality': statistics.mean(list(self.ai_performance['signal_quality'])) if self.ai_performance['signal_quality'] else 0.0,
                    'model_confidence': statistics.mean(list(self.ai_performance['model_confidence'])) if self.ai_performance['model_confidence'] else 0.0
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting current performance: {e}")
            return {}
    
    async def get_trade_analysis(self, symbol: Optional[str] = None, days: int = 30) -> Dict[str, Any]:
        """Get detailed trade analysis."""
        try:
            # Filter trades
            cutoff_date = datetime.now() - timedelta(days=days)
            recent_trades = [t for t in self.trade_history if t.exit_time >= cutoff_date]
            
            if symbol:
                recent_trades = [t for t in recent_trades if t.symbol == symbol]
            
            if not recent_trades:
                return {}
            
            # Analyze trades
            winning_trades = [t for t in recent_trades if t.pnl > 0]
            losing_trades = [t for t in recent_trades if t.pnl < 0]
            
            # Performance by symbol
            symbol_performance = defaultdict(lambda: {'trades': 0, 'pnl': 0.0, 'win_rate': 0.0})
            for trade in recent_trades:
                symbol_performance[trade.symbol]['trades'] += 1
                symbol_performance[trade.symbol]['pnl'] += trade.pnl
            
            for sym in symbol_performance:
                sym_trades = [t for t in recent_trades if t.symbol == sym]
                sym_wins = [t for t in sym_trades if t.pnl > 0]
                symbol_performance[sym]['win_rate'] = len(sym_wins) / len(sym_trades)
            
            # Performance by time of day
            hourly_performance = defaultdict(lambda: {'trades': 0, 'pnl': 0.0})
            for trade in recent_trades:
                hour = trade.exit_time.hour
                hourly_performance[hour]['trades'] += 1
                hourly_performance[hour]['pnl'] += trade.pnl
            
            return {
                'period_days': days,
                'total_trades': len(recent_trades),
                'winning_trades': len(winning_trades),
                'losing_trades': len(losing_trades),
                'win_rate': len(winning_trades) / len(recent_trades),
                'total_pnl': sum(t.pnl for t in recent_trades),
                'average_win': statistics.mean([t.pnl for t in winning_trades]) if winning_trades else 0.0,
                'average_loss': statistics.mean([t.pnl for t in losing_trades]) if losing_trades else 0.0,
                'largest_win': max([t.pnl for t in winning_trades]) if winning_trades else 0.0,
                'largest_loss': min([t.pnl for t in losing_trades]) if losing_trades else 0.0,
                'average_duration': statistics.mean([t.duration.total_seconds() for t in recent_trades]),
                'total_fees': sum(t.fees for t in recent_trades),
                'average_slippage': statistics.mean([t.slippage for t in recent_trades]),
                'symbol_performance': dict(symbol_performance),
                'hourly_performance': dict(hourly_performance)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting trade analysis: {e}")
            return {}
    
    async def export_performance_data(self, filepath: str, days: int = 30) -> bool:
        """Export performance data to file."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'period_days': days,
                'snapshots': [
                    {
                        'timestamp': s.timestamp.isoformat(),
                        'portfolio_value': s.portfolio_value,
                        'total_return': s.total_return,
                        'daily_return': s.daily_return,
                        'sharpe_ratio': s.sharpe_ratio,
                        'max_drawdown': s.max_drawdown,
                        'win_rate': s.win_rate,
                        'profit_factor': s.profit_factor,
                        'total_trades': s.total_trades
                    }
                    for s in self.snapshots if s.timestamp >= cutoff_date
                ],
                'trades': [
                    {
                        'trade_id': t.trade_id,
                        'symbol': t.symbol,
                        'side': t.side,
                        'entry_price': t.entry_price,
                        'exit_price': t.exit_price,
                        'quantity': t.quantity,
                        'entry_time': t.entry_time.isoformat(),
                        'exit_time': t.exit_time.isoformat(),
                        'duration_seconds': t.duration.total_seconds(),
                        'pnl': t.pnl,
                        'pnl_pct': t.pnl_pct,
                        'fees': t.fees,
                        'slippage': t.slippage,
                        'ai_confidence': t.ai_confidence
                    }
                    for t in self.trade_history if t.exit_time >= cutoff_date
                ],
                'current_metrics': self.current_metrics,
                'ai_performance': {
                    'prediction_accuracy': list(self.ai_performance['prediction_accuracy']),
                    'signal_quality': list(self.ai_performance['signal_quality']),
                    'model_confidence': list(self.ai_performance['model_confidence']),
                    'feature_importance': self.ai_performance['feature_importance']
                }
            }
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.logger.info(f"Performance data exported to: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting performance data: {e}")
            return False