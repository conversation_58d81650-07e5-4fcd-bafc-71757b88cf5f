"""Risk Monitoring System for AI Trading Agent.

This module provides comprehensive risk monitoring, assessment,
and alerting capabilities for the AI trading system.

Author: inkbytefo
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
from pathlib import Path
import numpy as np


class RiskLevel(Enum):
    """Risk level classifications."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"
    EXTREME = "extreme"


class RiskType(Enum):
    """Types of risk being monitored."""
    MARKET = "market"
    LIQUIDITY = "liquidity"
    CREDIT = "credit"
    OPERATIONAL = "operational"
    MODEL = "model"
    CONCENTRATION = "concentration"
    VOLATILITY = "volatility"
    DRAWDOWN = "drawdown"
    LEVERAGE = "leverage"
    CORRELATION = "correlation"


class AlertSeverity(Enum):
    """Alert severity levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


@dataclass
class RiskMetric:
    """Individual risk metric."""
    name: str
    value: float
    risk_type: RiskType
    risk_level: RiskLevel
    threshold: float
    timestamp: datetime
    metadata: Dict[str, Any] = field(default_factory=dict)
    description: str = ""


@dataclass
class RiskAlert:
    """Risk alert notification."""
    alert_id: str
    risk_type: RiskType
    severity: AlertSeverity
    message: str
    current_value: float
    threshold: float
    timestamp: datetime
    acknowledged: bool = False
    resolved: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionRisk:
    """Risk assessment for individual position."""
    symbol: str
    position_size: float
    market_value: float
    unrealized_pnl: float
    var_1d: float  # 1-day Value at Risk
    var_5d: float  # 5-day Value at Risk
    max_loss: float
    concentration_risk: float
    liquidity_risk: float
    volatility_risk: float
    correlation_risk: float
    overall_risk_score: float
    risk_level: RiskLevel
    timestamp: datetime


@dataclass
class PortfolioRisk:
    """Portfolio-level risk assessment."""
    total_value: float
    total_var: float
    max_drawdown: float
    current_drawdown: float
    leverage_ratio: float
    concentration_ratio: float
    correlation_risk: float
    liquidity_risk: float
    model_risk: float
    overall_risk_score: float
    risk_level: RiskLevel
    position_risks: Dict[str, PositionRisk]
    timestamp: datetime


class RiskMonitor:
    """Comprehensive risk monitoring system."""
    
    def __init__(self, config):
        """Initialize risk monitor."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Handle both dict and RiskSettings object
        if hasattr(config, '__dict__'):
            config_dict = config.__dict__
        else:
            config_dict = config
        
        # Risk thresholds
        self.risk_thresholds = {
            'max_portfolio_var': getattr(config, 'max_portfolio_var', 0.05) if hasattr(config, 'max_portfolio_var') else config_dict.get('max_portfolio_var', 0.05),
            'max_position_concentration': getattr(config, 'max_position_concentration', 0.20) if hasattr(config, 'max_position_concentration') else config_dict.get('max_position_concentration', 0.20),
            'max_drawdown': getattr(config, 'max_drawdown', 0.15) if hasattr(config, 'max_drawdown') else config_dict.get('max_drawdown', 0.15),
            'max_leverage': getattr(config, 'max_leverage', 2.0) if hasattr(config, 'max_leverage') else config_dict.get('max_leverage', 2.0),
            'max_correlation': getattr(config, 'max_correlation', 0.80) if hasattr(config, 'max_correlation') else config_dict.get('max_correlation', 0.80),
            'min_liquidity_ratio': getattr(config, 'min_liquidity_ratio', 0.10) if hasattr(config, 'min_liquidity_ratio') else config_dict.get('min_liquidity_ratio', 0.10),
            'max_model_uncertainty': getattr(config, 'max_model_uncertainty', 0.30) if hasattr(config, 'max_model_uncertainty') else config_dict.get('max_model_uncertainty', 0.30),
            'max_daily_loss': getattr(config, 'max_daily_loss', 0.03) if hasattr(config, 'max_daily_loss') else config_dict.get('max_daily_loss', 0.03),
            'max_weekly_loss': getattr(config, 'max_weekly_loss', 0.10) if hasattr(config, 'max_weekly_loss') else config_dict.get('max_weekly_loss', 0.10),
            'volatility_threshold': getattr(config, 'volatility_threshold', 0.25) if hasattr(config, 'volatility_threshold') else config_dict.get('volatility_threshold', 0.25)
        }
        
        # Risk monitoring data
        self.risk_metrics: Dict[str, List[RiskMetric]] = defaultdict(list)
        self.active_alerts: Dict[str, RiskAlert] = {}
        self.alert_history: List[RiskAlert] = []
        self.position_risks: Dict[str, PositionRisk] = {}
        self.portfolio_risk_history: List[PortfolioRisk] = []
        
        # Real-time risk tracking
        self.current_portfolio_risk: Optional[PortfolioRisk] = None
        self.risk_scores: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.volatility_estimates: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))
        
        # Market data for risk calculations
        self.price_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=252))  # 1 year
        self.return_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=252))
        self.correlation_matrix: Dict[Tuple[str, str], float] = {}
        
        # Risk model parameters
        self.confidence_level = getattr(config, 'confidence_level', 0.95) if hasattr(config, 'confidence_level') else config_dict.get('confidence_level', 0.95)
        self.lookback_period = getattr(config, 'lookback_period', 252) if hasattr(config, 'lookback_period') else config_dict.get('lookback_period', 252)
        self.decay_factor = getattr(config, 'decay_factor', 0.94) if hasattr(config, 'decay_factor') else config_dict.get('decay_factor', 0.94)
        
        # Alert settings
        self.alert_cooldown = getattr(config, 'alert_cooldown', 300) if hasattr(config, 'alert_cooldown') else config_dict.get('alert_cooldown', 300)
        self.last_alert_times: Dict[str, datetime] = {}
        
        # Monitoring settings
        self.monitoring_frequency = getattr(config, 'monitoring_frequency', 60) if hasattr(config, 'monitoring_frequency') else config_dict.get('monitoring_frequency', 60)
        self.risk_report_frequency = getattr(config, 'risk_report_frequency', 3600) if hasattr(config, 'risk_report_frequency') else config_dict.get('risk_report_frequency', 3600)
        
        self.logger.info("RiskMonitor initialized")
    
    async def start_monitoring(self):
        """Start risk monitoring."""
        try:
            # Start monitoring tasks
            asyncio.create_task(self._monitor_risks())
            asyncio.create_task(self._generate_risk_reports())
            asyncio.create_task(self._update_correlations())
            asyncio.create_task(self._cleanup_old_data())
            
            self.logger.info("Risk monitoring started")
            
        except Exception as e:
            self.logger.error(f"Error starting risk monitoring: {e}")
            raise
    
    async def update_market_data(self, symbol: str, price: float, timestamp: datetime = None):
        """Update market data for risk calculations."""
        try:
            if timestamp is None:
                timestamp = datetime.now()
            
            # Store price
            self.price_history[symbol].append((timestamp, price))
            
            # Calculate return if we have previous price
            if len(self.price_history[symbol]) > 1:
                prev_price = self.price_history[symbol][-2][1]
                if prev_price > 0:
                    return_pct = (price - prev_price) / prev_price
                    self.return_history[symbol].append((timestamp, return_pct))
                    
                    # Update volatility estimate
                    await self._update_volatility_estimate(symbol)
            
            self.logger.debug(f"Market data updated for {symbol}: {price}")
            
        except Exception as e:
            self.logger.error(f"Error updating market data: {e}")
    
    async def update_position(self, symbol: str, position_data: Dict[str, Any]):
        """Update position data and calculate position risk."""
        try:
            # Calculate position risk metrics
            position_risk = await self._calculate_position_risk(symbol, position_data)
            
            if position_risk:
                self.position_risks[symbol] = position_risk
                
                # Check for position-level alerts
                await self._check_position_alerts(position_risk)
            
            self.logger.debug(f"Position risk updated for {symbol}")
            
        except Exception as e:
            self.logger.error(f"Error updating position risk: {e}")
    
    async def update_portfolio(self, portfolio_data: Dict[str, Any]):
        """Update portfolio data and calculate portfolio risk."""
        try:
            # Calculate portfolio risk metrics
            portfolio_risk = await self._calculate_portfolio_risk(portfolio_data)
            
            if portfolio_risk:
                self.current_portfolio_risk = portfolio_risk
                self.portfolio_risk_history.append(portfolio_risk)
                
                # Keep only recent history
                if len(self.portfolio_risk_history) > 10000:
                    self.portfolio_risk_history = self.portfolio_risk_history[-5000:]
                
                # Check for portfolio-level alerts
                await self._check_portfolio_alerts(portfolio_risk)
            
            self.logger.debug("Portfolio risk updated")
            
        except Exception as e:
            self.logger.error(f"Error updating portfolio risk: {e}")
    
    async def _calculate_position_risk(self, symbol: str, position_data: Dict[str, Any]) -> Optional[PositionRisk]:
        """Calculate risk metrics for individual position."""
        try:
            position_size = position_data.get('size', 0.0)
            market_value = position_data.get('market_value', 0.0)
            unrealized_pnl = position_data.get('unrealized_pnl', 0.0)
            
            if position_size == 0:
                return None
            
            # Get volatility estimate
            volatility = await self._get_volatility_estimate(symbol)
            
            # Calculate Value at Risk (VaR)
            var_1d = self._calculate_var(market_value, volatility, 1)
            var_5d = self._calculate_var(market_value, volatility, 5)
            
            # Calculate maximum potential loss (worst case scenario)
            max_loss = abs(market_value) * 0.5  # Assume 50% max loss
            
            # Calculate concentration risk
            portfolio_value = position_data.get('portfolio_value', market_value)
            concentration_risk = abs(market_value) / portfolio_value if portfolio_value > 0 else 0.0
            
            # Calculate liquidity risk (simplified)
            daily_volume = position_data.get('daily_volume', 0.0)
            liquidity_risk = abs(position_size) / daily_volume if daily_volume > 0 else 1.0
            liquidity_risk = min(liquidity_risk, 1.0)  # Cap at 100%
            
            # Calculate volatility risk
            volatility_risk = min(volatility / self.risk_thresholds['volatility_threshold'], 1.0)
            
            # Calculate correlation risk (simplified)
            correlation_risk = await self._get_correlation_risk(symbol)
            
            # Calculate overall risk score
            risk_components = {
                'concentration': concentration_risk * 0.25,
                'liquidity': liquidity_risk * 0.20,
                'volatility': volatility_risk * 0.25,
                'correlation': correlation_risk * 0.15,
                'var': (var_1d / abs(market_value)) * 0.15 if market_value != 0 else 0.0
            }
            
            overall_risk_score = sum(risk_components.values())
            
            # Determine risk level
            if overall_risk_score >= 0.8:
                risk_level = RiskLevel.EXTREME
            elif overall_risk_score >= 0.6:
                risk_level = RiskLevel.CRITICAL
            elif overall_risk_score >= 0.4:
                risk_level = RiskLevel.HIGH
            elif overall_risk_score >= 0.2:
                risk_level = RiskLevel.MEDIUM
            else:
                risk_level = RiskLevel.LOW
            
            return PositionRisk(
                symbol=symbol,
                position_size=position_size,
                market_value=market_value,
                unrealized_pnl=unrealized_pnl,
                var_1d=var_1d,
                var_5d=var_5d,
                max_loss=max_loss,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                volatility_risk=volatility_risk,
                correlation_risk=correlation_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating position risk for {symbol}: {e}")
            return None
    
    async def _calculate_portfolio_risk(self, portfolio_data: Dict[str, Any]) -> Optional[PortfolioRisk]:
        """Calculate portfolio-level risk metrics."""
        try:
            total_value = portfolio_data.get('total_value', 0.0)
            cash_balance = portfolio_data.get('cash_balance', 0.0)
            
            if total_value <= 0:
                return None
            
            # Calculate portfolio VaR (sum of individual VaRs with correlation adjustment)
            total_var = 0.0
            for position_risk in self.position_risks.values():
                total_var += position_risk.var_1d ** 2
            
            # Apply correlation adjustment (simplified)
            avg_correlation = await self._get_average_correlation()
            correlation_adjustment = 1 + (avg_correlation - 1) * 0.5
            total_var = (total_var ** 0.5) * correlation_adjustment
            
            # Calculate drawdown
            max_drawdown = await self._calculate_max_drawdown(portfolio_data)
            current_drawdown = await self._calculate_current_drawdown(portfolio_data)
            
            # Calculate leverage ratio
            gross_exposure = sum(abs(pos.market_value) for pos in self.position_risks.values())
            leverage_ratio = gross_exposure / total_value if total_value > 0 else 0.0
            
            # Calculate concentration ratio (largest position as % of portfolio)
            if self.position_risks:
                largest_position = max(abs(pos.market_value) for pos in self.position_risks.values())
                concentration_ratio = largest_position / total_value
            else:
                concentration_ratio = 0.0
            
            # Calculate correlation risk
            correlation_risk = avg_correlation
            
            # Calculate liquidity risk
            liquidity_risk = 1 - (cash_balance / total_value) if total_value > 0 else 1.0
            liquidity_risk = max(0.0, min(1.0, liquidity_risk))
            
            # Calculate model risk (based on AI uncertainty)
            model_risk = portfolio_data.get('model_uncertainty', 0.0)
            
            # Calculate overall portfolio risk score
            risk_components = {
                'var': min(total_var / total_value, 1.0) if total_value > 0 else 0.0,
                'drawdown': min(current_drawdown / self.risk_thresholds['max_drawdown'], 1.0),
                'leverage': min(leverage_ratio / self.risk_thresholds['max_leverage'], 1.0),
                'concentration': min(concentration_ratio / self.risk_thresholds['max_position_concentration'], 1.0),
                'correlation': correlation_risk,
                'liquidity': liquidity_risk,
                'model': model_risk
            }
            
            # Weight the components
            weights = {
                'var': 0.25,
                'drawdown': 0.20,
                'leverage': 0.15,
                'concentration': 0.15,
                'correlation': 0.10,
                'liquidity': 0.10,
                'model': 0.05
            }
            
            overall_risk_score = sum(risk_components[k] * weights[k] for k in risk_components)
            
            # Determine risk level
            if overall_risk_score >= 0.8:
                risk_level = RiskLevel.EXTREME
            elif overall_risk_score >= 0.6:
                risk_level = RiskLevel.CRITICAL
            elif overall_risk_score >= 0.4:
                risk_level = RiskLevel.HIGH
            elif overall_risk_score >= 0.2:
                risk_level = RiskLevel.MEDIUM
            else:
                risk_level = RiskLevel.LOW
            
            return PortfolioRisk(
                total_value=total_value,
                total_var=total_var,
                max_drawdown=max_drawdown,
                current_drawdown=current_drawdown,
                leverage_ratio=leverage_ratio,
                concentration_ratio=concentration_ratio,
                correlation_risk=correlation_risk,
                liquidity_risk=liquidity_risk,
                model_risk=model_risk,
                overall_risk_score=overall_risk_score,
                risk_level=risk_level,
                position_risks=self.position_risks.copy(),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio risk: {e}")
            return None
    
    def _calculate_var(self, market_value: float, volatility: float, days: int) -> float:
        """Calculate Value at Risk."""
        try:
            # Use normal distribution approximation
            # For 95% confidence level, z-score is approximately 1.645
            z_score = 1.645 if self.confidence_level == 0.95 else 2.33  # 99% confidence
            
            # Scale volatility for time horizon
            scaled_volatility = volatility * (days ** 0.5)
            
            # Calculate VaR
            var = abs(market_value) * z_score * scaled_volatility
            
            return var
            
        except Exception as e:
            self.logger.error(f"Error calculating VaR: {e}")
            return 0.0
    
    async def _get_volatility_estimate(self, symbol: str) -> float:
        """Get volatility estimate for symbol."""
        try:
            if symbol not in self.return_history or len(self.return_history[symbol]) < 10:
                return 0.20  # Default 20% volatility
            
            # Get recent returns
            returns = [r[1] for r in list(self.return_history[symbol])[-30:]]  # Last 30 returns
            
            if len(returns) < 2:
                return 0.20
            
            # Calculate EWMA volatility
            ewma_var = 0.0
            for i, ret in enumerate(reversed(returns)):
                weight = (self.decay_factor ** i) * (1 - self.decay_factor)
                ewma_var += weight * (ret ** 2)
            
            # Annualize volatility (assuming daily returns)
            volatility = (ewma_var ** 0.5) * (252 ** 0.5)
            
            return volatility
            
        except Exception as e:
            self.logger.error(f"Error calculating volatility for {symbol}: {e}")
            return 0.20
    
    async def _update_volatility_estimate(self, symbol: str):
        """Update volatility estimate for symbol."""
        try:
            volatility = await self._get_volatility_estimate(symbol)
            self.volatility_estimates[symbol].append((datetime.now(), volatility))
            
        except Exception as e:
            self.logger.error(f"Error updating volatility estimate: {e}")
    
    async def _get_correlation_risk(self, symbol: str) -> float:
        """Calculate correlation risk for a symbol."""
        try:
            if len(self.position_risks) <= 1:
                return 0.0  # No correlation risk with single position
            
            # Calculate average correlation with other positions
            correlations = []
            for other_symbol in self.position_risks:
                if other_symbol != symbol:
                    corr = self.correlation_matrix.get((symbol, other_symbol), 0.0)
                    correlations.append(abs(corr))
            
            if not correlations:
                return 0.0
            
            avg_correlation = statistics.mean(correlations)
            return min(avg_correlation, 1.0)
            
        except Exception as e:
            self.logger.error(f"Error calculating correlation risk: {e}")
            return 0.0
    
    async def _get_average_correlation(self) -> float:
        """Get average correlation across all positions."""
        try:
            if len(self.correlation_matrix) == 0:
                return 0.0
            
            correlations = [abs(corr) for corr in self.correlation_matrix.values()]
            return statistics.mean(correlations) if correlations else 0.0
            
        except Exception as e:
            self.logger.error(f"Error calculating average correlation: {e}")
            return 0.0
    
    async def _calculate_max_drawdown(self, portfolio_data: Dict[str, Any]) -> float:
        """Calculate maximum drawdown."""
        try:
            if len(self.portfolio_risk_history) < 2:
                return 0.0
            
            values = [p.total_value for p in self.portfolio_risk_history]
            peak = values[0]
            max_dd = 0.0
            
            for value in values:
                if value > peak:
                    peak = value
                
                drawdown = (peak - value) / peak if peak > 0 else 0.0
                max_dd = max(max_dd, drawdown)
            
            return max_dd
            
        except Exception as e:
            self.logger.error(f"Error calculating max drawdown: {e}")
            return 0.0
    
    async def _calculate_current_drawdown(self, portfolio_data: Dict[str, Any]) -> float:
        """Calculate current drawdown from peak."""
        try:
            if len(self.portfolio_risk_history) < 2:
                return 0.0
            
            current_value = portfolio_data.get('total_value', 0.0)
            values = [p.total_value for p in self.portfolio_risk_history]
            peak = max(values)
            
            if peak <= 0:
                return 0.0
            
            return (peak - current_value) / peak
            
        except Exception as e:
            self.logger.error(f"Error calculating current drawdown: {e}")
            return 0.0
    
    async def _check_position_alerts(self, position_risk: PositionRisk):
        """Check for position-level risk alerts."""
        try:
            alerts = []
            
            # Concentration risk alert
            if position_risk.concentration_risk > self.risk_thresholds['max_position_concentration']:
                alerts.append({
                    'type': 'position_concentration',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Position {position_risk.symbol} concentration risk: {position_risk.concentration_risk:.2%}",
                    'current_value': position_risk.concentration_risk,
                    'threshold': self.risk_thresholds['max_position_concentration']
                })
            
            # Liquidity risk alert
            if position_risk.liquidity_risk > 0.5:  # 50% liquidity risk threshold
                alerts.append({
                    'type': 'position_liquidity',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Position {position_risk.symbol} liquidity risk: {position_risk.liquidity_risk:.2%}",
                    'current_value': position_risk.liquidity_risk,
                    'threshold': 0.5
                })
            
            # High volatility alert
            if position_risk.volatility_risk > 0.8:  # 80% volatility risk threshold
                alerts.append({
                    'type': 'position_volatility',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Position {position_risk.symbol} high volatility risk: {position_risk.volatility_risk:.2%}",
                    'current_value': position_risk.volatility_risk,
                    'threshold': 0.8
                })
            
            # Overall risk level alert
            if position_risk.risk_level in [RiskLevel.CRITICAL, RiskLevel.EXTREME]:
                severity = AlertSeverity.CRITICAL if position_risk.risk_level == RiskLevel.EXTREME else AlertSeverity.WARNING
                alerts.append({
                    'type': 'position_risk_level',
                    'severity': severity,
                    'message': f"Position {position_risk.symbol} risk level: {position_risk.risk_level.value}",
                    'current_value': position_risk.overall_risk_score,
                    'threshold': 0.6
                })
            
            # Create and store alerts
            for alert_data in alerts:
                await self._create_alert(
                    risk_type=RiskType.CONCENTRATION if 'concentration' in alert_data['type'] else RiskType.MARKET,
                    **alert_data
                )
                
        except Exception as e:
            self.logger.error(f"Error checking position alerts: {e}")
    
    async def _check_portfolio_alerts(self, portfolio_risk: PortfolioRisk):
        """Check for portfolio-level risk alerts."""
        try:
            alerts = []
            
            # Portfolio VaR alert
            var_ratio = portfolio_risk.total_var / portfolio_risk.total_value if portfolio_risk.total_value > 0 else 0.0
            if var_ratio > self.risk_thresholds['max_portfolio_var']:
                alerts.append({
                    'type': 'portfolio_var',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Portfolio VaR exceeded: {var_ratio:.2%}",
                    'current_value': var_ratio,
                    'threshold': self.risk_thresholds['max_portfolio_var']
                })
            
            # Drawdown alert
            if portfolio_risk.current_drawdown > self.risk_thresholds['max_drawdown']:
                severity = AlertSeverity.CRITICAL if portfolio_risk.current_drawdown > self.risk_thresholds['max_drawdown'] * 1.5 else AlertSeverity.WARNING
                alerts.append({
                    'type': 'portfolio_drawdown',
                    'severity': severity,
                    'message': f"Portfolio drawdown: {portfolio_risk.current_drawdown:.2%}",
                    'current_value': portfolio_risk.current_drawdown,
                    'threshold': self.risk_thresholds['max_drawdown']
                })
            
            # Leverage alert
            if portfolio_risk.leverage_ratio > self.risk_thresholds['max_leverage']:
                alerts.append({
                    'type': 'portfolio_leverage',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Portfolio leverage: {portfolio_risk.leverage_ratio:.2f}x",
                    'current_value': portfolio_risk.leverage_ratio,
                    'threshold': self.risk_thresholds['max_leverage']
                })
            
            # Concentration alert
            if portfolio_risk.concentration_ratio > self.risk_thresholds['max_position_concentration']:
                alerts.append({
                    'type': 'portfolio_concentration',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Portfolio concentration: {portfolio_risk.concentration_ratio:.2%}",
                    'current_value': portfolio_risk.concentration_ratio,
                    'threshold': self.risk_thresholds['max_position_concentration']
                })
            
            # Liquidity alert
            if portfolio_risk.liquidity_risk > (1 - self.risk_thresholds['min_liquidity_ratio']):
                alerts.append({
                    'type': 'portfolio_liquidity',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Portfolio liquidity risk: {portfolio_risk.liquidity_risk:.2%}",
                    'current_value': portfolio_risk.liquidity_risk,
                    'threshold': 1 - self.risk_thresholds['min_liquidity_ratio']
                })
            
            # Model risk alert
            if portfolio_risk.model_risk > self.risk_thresholds['max_model_uncertainty']:
                alerts.append({
                    'type': 'portfolio_model_risk',
                    'severity': AlertSeverity.WARNING,
                    'message': f"Model uncertainty: {portfolio_risk.model_risk:.2%}",
                    'current_value': portfolio_risk.model_risk,
                    'threshold': self.risk_thresholds['max_model_uncertainty']
                })
            
            # Overall risk level alert
            if portfolio_risk.risk_level in [RiskLevel.CRITICAL, RiskLevel.EXTREME]:
                severity = AlertSeverity.EMERGENCY if portfolio_risk.risk_level == RiskLevel.EXTREME else AlertSeverity.CRITICAL
                alerts.append({
                    'type': 'portfolio_risk_level',
                    'severity': severity,
                    'message': f"Portfolio risk level: {portfolio_risk.risk_level.value}",
                    'current_value': portfolio_risk.overall_risk_score,
                    'threshold': 0.6
                })
            
            # Create and store alerts
            for alert_data in alerts:
                await self._create_alert(
                    risk_type=RiskType.MARKET,
                    **alert_data
                )
                
        except Exception as e:
            self.logger.error(f"Error checking portfolio alerts: {e}")
    
    async def _create_alert(self, risk_type: RiskType, type: str, severity: AlertSeverity, 
                          message: str, current_value: float, threshold: float):
        """Create and store a risk alert."""
        try:
            # Check cooldown period
            alert_key = f"{type}_{risk_type.value}"
            now = datetime.now()
            
            if alert_key in self.last_alert_times:
                time_since_last = (now - self.last_alert_times[alert_key]).total_seconds()
                if time_since_last < self.alert_cooldown:
                    return  # Skip alert due to cooldown
            
            # Create alert
            alert_id = f"{alert_key}_{int(now.timestamp())}"
            alert = RiskAlert(
                alert_id=alert_id,
                risk_type=risk_type,
                severity=severity,
                message=message,
                current_value=current_value,
                threshold=threshold,
                timestamp=now
            )
            
            # Store alert
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            self.last_alert_times[alert_key] = now
            
            # Log alert
            log_level = logging.CRITICAL if severity == AlertSeverity.EMERGENCY else logging.WARNING
            self.logger.log(log_level, f"Risk alert: {message}")
            
            # Keep alert history manageable
            if len(self.alert_history) > 10000:
                self.alert_history = self.alert_history[-5000:]
                
        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")
    
    async def _monitor_risks(self):
        """Main risk monitoring loop."""
        while True:
            try:
                # Update risk scores
                await self._update_risk_scores()
                
                # Check for new alerts
                if self.current_portfolio_risk:
                    await self._check_portfolio_alerts(self.current_portfolio_risk)
                
                for position_risk in self.position_risks.values():
                    await self._check_position_alerts(position_risk)
                
                # Clean up resolved alerts
                await self._cleanup_alerts()
                
                await asyncio.sleep(self.monitoring_frequency)
                
            except Exception as e:
                self.logger.error(f"Error in risk monitoring loop: {e}")
                await asyncio.sleep(self.monitoring_frequency)
    
    async def _update_risk_scores(self):
        """Update rolling risk scores."""
        try:
            if self.current_portfolio_risk:
                self.risk_scores['portfolio'].append((
                    datetime.now(),
                    self.current_portfolio_risk.overall_risk_score
                ))
            
            for symbol, position_risk in self.position_risks.items():
                self.risk_scores[f'position_{symbol}'].append((
                    datetime.now(),
                    position_risk.overall_risk_score
                ))
                
        except Exception as e:
            self.logger.error(f"Error updating risk scores: {e}")
    
    async def _cleanup_alerts(self):
        """Clean up resolved or expired alerts."""
        try:
            now = datetime.now()
            expired_alerts = []
            
            for alert_id, alert in self.active_alerts.items():
                # Auto-resolve alerts older than 1 hour if conditions improved
                if (now - alert.timestamp).total_seconds() > 3600:
                    # Check if condition still exists
                    if await self._is_alert_condition_resolved(alert):
                        alert.resolved = True
                        expired_alerts.append(alert_id)
            
            # Remove resolved alerts
            for alert_id in expired_alerts:
                del self.active_alerts[alert_id]
                
        except Exception as e:
            self.logger.error(f"Error cleaning up alerts: {e}")
    
    async def _is_alert_condition_resolved(self, alert: RiskAlert) -> bool:
        """Check if alert condition has been resolved."""
        try:
            # Simple check: if current value is below threshold
            if 'portfolio' in alert.message.lower():
                if self.current_portfolio_risk:
                    if 'var' in alert.message.lower():
                        current_var_ratio = self.current_portfolio_risk.total_var / self.current_portfolio_risk.total_value
                        return current_var_ratio <= alert.threshold
                    elif 'drawdown' in alert.message.lower():
                        return self.current_portfolio_risk.current_drawdown <= alert.threshold
                    elif 'leverage' in alert.message.lower():
                        return self.current_portfolio_risk.leverage_ratio <= alert.threshold
            
            return False  # Conservative approach
            
        except Exception as e:
            self.logger.error(f"Error checking alert resolution: {e}")
            return False
    
    async def _update_correlations(self):
        """Update correlation matrix periodically."""
        while True:
            try:
                await self._calculate_correlations()
                await asyncio.sleep(3600)  # Update every hour
                
            except Exception as e:
                self.logger.error(f"Error updating correlations: {e}")
                await asyncio.sleep(3600)
    
    async def _calculate_correlations(self):
        """Calculate correlation matrix for all symbols using optimized vectorized operations."""
        try:
            symbols = list(self.return_history.keys())
            
            if len(symbols) < 2:
                return
            
            # Prepare returns data for all symbols
            returns_data = {}
            for symbol in symbols:
                returns = [r[1] for r in list(self.return_history[symbol])[-100:]]
                if len(returns) >= 10:
                    returns_data[symbol] = returns
            
            # Calculate correlations using vectorized operations
            valid_symbols = list(returns_data.keys())
            if len(valid_symbols) < 2:
                return
                
            # Use numpy for efficient correlation calculation
            import numpy as np
            
            # Align all returns to same length
            min_length = min(len(returns_data[symbol]) for symbol in valid_symbols)
            aligned_returns = np.array([
                returns_data[symbol][-min_length:] for symbol in valid_symbols
            ])
            
            # Calculate correlation matrix in one operation
            corr_matrix = np.corrcoef(aligned_returns)
            
            # Update correlation matrix
            for i, symbol1 in enumerate(valid_symbols):
                for j, symbol2 in enumerate(valid_symbols):
                    if i != j:
                        correlation = corr_matrix[i, j] if not np.isnan(corr_matrix[i, j]) else 0.0
                        self.correlation_matrix[(symbol1, symbol2)] = correlation
                    
        except Exception as e:
            self.logger.error(f"Error calculating correlations: {e}")
    
    async def _calculate_correlation(self, symbol1: str, symbol2: str) -> float:
        """Calculate correlation between two symbols."""
        try:
            if symbol1 not in self.return_history or symbol2 not in self.return_history:
                return 0.0
            
            returns1 = [r[1] for r in list(self.return_history[symbol1])[-100:]]  # Last 100 returns
            returns2 = [r[1] for r in list(self.return_history[symbol2])[-100:]]
            
            if len(returns1) < 10 or len(returns2) < 10:
                return 0.0
            
            # Align returns by timestamp (simplified)
            min_length = min(len(returns1), len(returns2))
            returns1 = returns1[-min_length:]
            returns2 = returns2[-min_length:]
            
            if min_length < 10:
                return 0.0
            
            # Calculate correlation
            if np is not None:
                correlation = np.corrcoef(returns1, returns2)[0, 1]
                return correlation if not np.isnan(correlation) else 0.0
            else:
                # Manual correlation calculation
                mean1 = statistics.mean(returns1)
                mean2 = statistics.mean(returns2)
                
                numerator = sum((r1 - mean1) * (r2 - mean2) for r1, r2 in zip(returns1, returns2))
                
                sum_sq1 = sum((r1 - mean1) ** 2 for r1 in returns1)
                sum_sq2 = sum((r2 - mean2) ** 2 for r2 in returns2)
                
                denominator = (sum_sq1 * sum_sq2) ** 0.5
                
                if denominator == 0:
                    return 0.0
                
                return numerator / denominator
                
        except Exception as e:
            self.logger.error(f"Error calculating correlation between {symbol1} and {symbol2}: {e}")
            return 0.0
    
    async def _generate_risk_reports(self):
        """Generate periodic risk reports."""
        while True:
            try:
                await self.generate_risk_report()
                await asyncio.sleep(self.risk_report_frequency)
                
            except Exception as e:
                self.logger.error(f"Error generating risk reports: {e}")
                await asyncio.sleep(self.risk_report_frequency)
    
    async def _cleanup_old_data(self):
        """Clean up old data periodically."""
        while True:
            try:
                cutoff_date = datetime.now() - timedelta(days=30)
                
                # Clean up old metrics
                for metric_list in self.risk_metrics.values():
                    metric_list[:] = [m for m in metric_list if m.timestamp > cutoff_date]
                
                # Clean up old alerts
                self.alert_history[:] = [a for a in self.alert_history if a.timestamp > cutoff_date]
                
                await asyncio.sleep(24 * 3600)  # Daily cleanup
                
            except Exception as e:
                self.logger.error(f"Error cleaning up old data: {e}")
                await asyncio.sleep(24 * 3600)
    
    # Public interface methods
    async def get_current_risk_status(self) -> Dict[str, Any]:
        """Get current risk status summary."""
        try:
            if not self.current_portfolio_risk:
                return {}
            
            portfolio_risk = self.current_portfolio_risk
            
            # Count active alerts by severity
            alert_counts = defaultdict(int)
            for alert in self.active_alerts.values():
                alert_counts[alert.severity.value] += 1
            
            # Get top risk positions
            top_risk_positions = sorted(
                self.position_risks.items(),
                key=lambda x: x[1].overall_risk_score,
                reverse=True
            )[:5]
            
            return {
                'timestamp': portfolio_risk.timestamp.isoformat(),
                'overall_risk_level': portfolio_risk.risk_level.value,
                'overall_risk_score': portfolio_risk.overall_risk_score,
                'portfolio_metrics': {
                    'total_value': portfolio_risk.total_value,
                    'total_var': portfolio_risk.total_var,
                    'var_ratio': portfolio_risk.total_var / portfolio_risk.total_value if portfolio_risk.total_value > 0 else 0.0,
                    'current_drawdown': portfolio_risk.current_drawdown,
                    'max_drawdown': portfolio_risk.max_drawdown,
                    'leverage_ratio': portfolio_risk.leverage_ratio,
                    'concentration_ratio': portfolio_risk.concentration_ratio,
                    'liquidity_risk': portfolio_risk.liquidity_risk,
                    'correlation_risk': portfolio_risk.correlation_risk,
                    'model_risk': portfolio_risk.model_risk
                },
                'active_alerts': {
                    'total': len(self.active_alerts),
                    'by_severity': dict(alert_counts)
                },
                'top_risk_positions': [
                    {
                        'symbol': symbol,
                        'risk_score': pos.overall_risk_score,
                        'risk_level': pos.risk_level.value,
                        'market_value': pos.market_value,
                        'concentration_risk': pos.concentration_risk
                    }
                    for symbol, pos in top_risk_positions
                ]
            }
            
        except Exception as e:
            self.logger.error(f"Error getting current risk status: {e}")
            return {}
    
    async def get_position_risk(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get risk metrics for specific position."""
        try:
            if symbol not in self.position_risks:
                return None
            
            position_risk = self.position_risks[symbol]
            
            return {
                'symbol': position_risk.symbol,
                'risk_level': position_risk.risk_level.value,
                'overall_risk_score': position_risk.overall_risk_score,
                'position_size': position_risk.position_size,
                'market_value': position_risk.market_value,
                'unrealized_pnl': position_risk.unrealized_pnl,
                'var_1d': position_risk.var_1d,
                'var_5d': position_risk.var_5d,
                'max_loss': position_risk.max_loss,
                'concentration_risk': position_risk.concentration_risk,
                'liquidity_risk': position_risk.liquidity_risk,
                'volatility_risk': position_risk.volatility_risk,
                'correlation_risk': position_risk.correlation_risk,
                'timestamp': position_risk.timestamp.isoformat()
            }
            
        except Exception as e:
            self.logger.error(f"Error getting position risk for {symbol}: {e}")
            return None
    
    async def get_active_alerts(self, severity: Optional[AlertSeverity] = None) -> List[Dict[str, Any]]:
        """Get active risk alerts."""
        try:
            alerts = list(self.active_alerts.values())
            
            if severity:
                alerts = [a for a in alerts if a.severity == severity]
            
            return [
                {
                    'alert_id': alert.alert_id,
                    'risk_type': alert.risk_type.value,
                    'severity': alert.severity.value,
                    'message': alert.message,
                    'current_value': alert.current_value,
                    'threshold': alert.threshold,
                    'timestamp': alert.timestamp.isoformat(),
                    'acknowledged': alert.acknowledged
                }
                for alert in sorted(alerts, key=lambda x: x.timestamp, reverse=True)
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting active alerts: {e}")
            return []
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge a risk alert."""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].acknowledged = True
                self.logger.info(f"Alert acknowledged: {alert_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """Manually resolve a risk alert."""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].resolved = True
                del self.active_alerts[alert_id]
                self.logger.info(f"Alert resolved: {alert_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False
    
    async def generate_risk_report(self) -> Dict[str, Any]:
        """Generate comprehensive risk report."""
        try:
            if not self.current_portfolio_risk:
                return {}
            
            portfolio_risk = self.current_portfolio_risk
            
            # Calculate risk trends
            recent_scores = list(self.risk_scores['portfolio'])[-30:]  # Last 30 measurements
            risk_trend = 'stable'
            
            if len(recent_scores) >= 10:
                recent_avg = statistics.mean([s[1] for s in recent_scores[-10:]])
                older_avg = statistics.mean([s[1] for s in recent_scores[-20:-10]]) if len(recent_scores) >= 20 else recent_avg
                
                if recent_avg > older_avg * 1.1:
                    risk_trend = 'increasing'
                elif recent_avg < older_avg * 0.9:
                    risk_trend = 'decreasing'
            
            # Risk breakdown by type
            risk_breakdown = {
                'market_risk': portfolio_risk.total_var / portfolio_risk.total_value if portfolio_risk.total_value > 0 else 0.0,
                'concentration_risk': portfolio_risk.concentration_ratio,
                'liquidity_risk': portfolio_risk.liquidity_risk,
                'leverage_risk': min(portfolio_risk.leverage_ratio / self.risk_thresholds['max_leverage'], 1.0),
                'correlation_risk': portfolio_risk.correlation_risk,
                'model_risk': portfolio_risk.model_risk,
                'drawdown_risk': min(portfolio_risk.current_drawdown / self.risk_thresholds['max_drawdown'], 1.0)
            }
            
            # Position risk summary
            position_summary = {
                'total_positions': len(self.position_risks),
                'high_risk_positions': len([p for p in self.position_risks.values() if p.risk_level in [RiskLevel.HIGH, RiskLevel.CRITICAL, RiskLevel.EXTREME]]),
                'avg_position_risk': statistics.mean([p.overall_risk_score for p in self.position_risks.values()]) if self.position_risks else 0.0,
                'max_position_risk': max([p.overall_risk_score for p in self.position_risks.values()]) if self.position_risks else 0.0
            }
            
            # Alert summary
            alert_summary = {
                'total_active': len(self.active_alerts),
                'critical_alerts': len([a for a in self.active_alerts.values() if a.severity in [AlertSeverity.CRITICAL, AlertSeverity.EMERGENCY]]),
                'unacknowledged': len([a for a in self.active_alerts.values() if not a.acknowledged]),
                'recent_alerts': len([a for a in self.alert_history if (datetime.now() - a.timestamp).total_seconds() < 3600])  # Last hour
            }
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'portfolio_risk': {
                    'overall_level': portfolio_risk.risk_level.value,
                    'overall_score': portfolio_risk.overall_risk_score,
                    'trend': risk_trend,
                    'total_value': portfolio_risk.total_value,
                    'total_var': portfolio_risk.total_var,
                    'current_drawdown': portfolio_risk.current_drawdown,
                    'max_drawdown': portfolio_risk.max_drawdown,
                    'leverage_ratio': portfolio_risk.leverage_ratio
                },
                'risk_breakdown': risk_breakdown,
                'position_summary': position_summary,
                'alert_summary': alert_summary,
                'risk_thresholds': self.risk_thresholds,
                'recommendations': await self._generate_risk_recommendations()
            }
            
            self.logger.info("Risk report generated")
            return report
            
        except Exception as e:
            self.logger.error(f"Error generating risk report: {e}")
            return {}
    
    async def _generate_risk_recommendations(self) -> List[str]:
        """Generate risk management recommendations."""
        try:
            recommendations = []
            
            if not self.current_portfolio_risk:
                return recommendations
            
            portfolio_risk = self.current_portfolio_risk
            
            # High overall risk
            if portfolio_risk.risk_level in [RiskLevel.CRITICAL, RiskLevel.EXTREME]:
                recommendations.append("Consider reducing overall portfolio risk through position sizing or hedging")
            
            # High concentration
            if portfolio_risk.concentration_ratio > self.risk_thresholds['max_position_concentration']:
                recommendations.append("Reduce position concentration by diversifying holdings")
            
            # High leverage
            if portfolio_risk.leverage_ratio > self.risk_thresholds['max_leverage']:
                recommendations.append("Reduce leverage by closing positions or adding capital")
            
            # High drawdown
            if portfolio_risk.current_drawdown > self.risk_thresholds['max_drawdown']:
                recommendations.append("Consider implementing stop-loss mechanisms to limit further drawdown")
            
            # Low liquidity
            if portfolio_risk.liquidity_risk > 0.8:
                recommendations.append("Increase cash reserves or reduce illiquid positions")
            
            # High correlation
            if portfolio_risk.correlation_risk > self.risk_thresholds['max_correlation']:
                recommendations.append("Diversify into uncorrelated assets to reduce correlation risk")
            
            # Model uncertainty
            if portfolio_risk.model_risk > self.risk_thresholds['max_model_uncertainty']:
                recommendations.append("Review AI model performance and consider manual oversight")
            
            # High VaR
            var_ratio = portfolio_risk.total_var / portfolio_risk.total_value if portfolio_risk.total_value > 0 else 0.0
            if var_ratio > self.risk_thresholds['max_portfolio_var']:
                recommendations.append("Reduce portfolio Value at Risk through hedging or position reduction")
            
            return recommendations
            
        except Exception as e:
            self.logger.error(f"Error generating risk recommendations: {e}")
            return []
    
    async def export_risk_data(self, filepath: str, days: int = 7) -> bool:
        """Export risk data to file."""
        try:
            cutoff_date = datetime.now() - timedelta(days=days)
            
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'period_days': days,
                'current_portfolio_risk': {
                    'overall_risk_level': self.current_portfolio_risk.risk_level.value,
                    'overall_risk_score': self.current_portfolio_risk.overall_risk_score,
                    'total_value': self.current_portfolio_risk.total_value,
                    'total_var': self.current_portfolio_risk.total_var,
                    'current_drawdown': self.current_portfolio_risk.current_drawdown,
                    'max_drawdown': self.current_portfolio_risk.max_drawdown,
                    'leverage_ratio': self.current_portfolio_risk.leverage_ratio,
                    'concentration_ratio': self.current_portfolio_risk.concentration_ratio,
                    'liquidity_risk': self.current_portfolio_risk.liquidity_risk,
                    'correlation_risk': self.current_portfolio_risk.correlation_risk,
                    'model_risk': self.current_portfolio_risk.model_risk
                } if self.current_portfolio_risk else {},
                'position_risks': {
                    symbol: {
                        'risk_level': pos.risk_level.value,
                        'overall_risk_score': pos.overall_risk_score,
                        'market_value': pos.market_value,
                        'var_1d': pos.var_1d,
                        'concentration_risk': pos.concentration_risk,
                        'liquidity_risk': pos.liquidity_risk,
                        'volatility_risk': pos.volatility_risk
                    }
                    for symbol, pos in self.position_risks.items()
                },
                'recent_alerts': [
                    {
                        'alert_id': alert.alert_id,
                        'risk_type': alert.risk_type.value,
                        'severity': alert.severity.value,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'resolved': alert.resolved
                    }
                    for alert in self.alert_history if alert.timestamp >= cutoff_date
                ],
                'risk_thresholds': self.risk_thresholds,
                'correlation_matrix': {
                    f"{pair[0]}_{pair[1]}": corr
                    for pair, corr in self.correlation_matrix.items()
                }
            }
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.logger.info(f"Risk data exported to: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting risk data: {e}")
            return False
    
    async def check_position_risk(self, symbol: str) -> Optional[PositionRisk]:
        """Check and return position risk for a specific symbol."""
        try:
            return self.position_risks.get(symbol)
        except Exception as e:
            self.logger.error(f"Error checking position risk for {symbol}: {e}")
            return None
    
    async def check_portfolio_risk(self) -> Optional[PortfolioRisk]:
        """Check and return current portfolio risk."""
        try:
            return self.current_portfolio_risk
        except Exception as e:
            self.logger.error(f"Error checking portfolio risk: {e}")
            return None
    
    async def get_risk_metrics(self) -> Dict[str, Any]:
        """Get current risk metrics."""
        try:
            metrics = {
                'portfolio_risk': self.current_portfolio_risk.__dict__ if self.current_portfolio_risk else None,
                'position_risks': {symbol: risk.__dict__ for symbol, risk in self.position_risks.items()},
                'risk_alerts': [alert.__dict__ for alert in self.active_alerts.values()],
                'risk_thresholds': self.risk_thresholds
            }
            return metrics
        except Exception as e:
            self.logger.error(f"Error getting risk metrics: {e}")
            return {}