"""System Monitoring for AI Trading Agent.

This module provides comprehensive system monitoring, performance tracking,
and health assessment capabilities for the AI trading system.

Author: inkbytefo
"""

import asyncio
import logging
import json
import psutil
import time
from datetime import datetime, timedelta
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import statistics
from pathlib import Path
import threading
import gc
import sys
import platform
from ..config.settings import MonitoringSettings
from .performance_monitor import PerformanceMonitor

try:
    import GPUtil
except ImportError:
    GPUtil = None

try:
    import nvidia_ml_py3 as nvml
except ImportError:
    nvml = None


class SystemStatus(Enum):
    """System status levels."""
    HEALTHY = "healthy"
    WARNING = "warning"
    CRITICAL = "critical"
    DEGRADED = "degraded"
    OFFLINE = "offline"


class ComponentType(Enum):
    """System component types."""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"
    NETWORK = "network"
    GPU = "gpu"
    DATABASE = "database"
    API = "api"
    TRADING_ENGINE = "trading_engine"
    AI_MODEL = "ai_model"
    DATA_FEED = "data_feed"
    EXECUTION = "execution"
    RISK_MANAGER = "risk_manager"


class AlertType(Enum):
    """System alert types."""
    PERFORMANCE = "performance"
    RESOURCE = "resource"
    CONNECTIVITY = "connectivity"
    ERROR = "error"
    LATENCY = "latency"
    CAPACITY = "capacity"
    SECURITY = "security"


@dataclass
class SystemMetric:
    """Individual system metric."""
    name: str
    value: float
    unit: str
    component: ComponentType
    timestamp: datetime
    threshold_warning: Optional[float] = None
    threshold_critical: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ComponentHealth:
    """Health status of system component."""
    component: ComponentType
    status: SystemStatus
    score: float  # 0-100 health score
    metrics: Dict[str, float]
    last_check: datetime
    uptime: float  # seconds
    error_count: int
    warning_count: int
    message: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SystemAlert:
    """System monitoring alert."""
    alert_id: str
    alert_type: AlertType
    component: ComponentType
    severity: str  # info, warning, critical
    message: str
    metric_name: str
    current_value: float
    threshold: float
    timestamp: datetime
    acknowledged: bool = False
    resolved: bool = False
    auto_resolve: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceSnapshot:
    """System performance snapshot."""
    timestamp: datetime
    cpu_usage: float
    memory_usage: float
    disk_usage: float
    network_io: Dict[str, float]
    gpu_usage: Optional[float]
    active_connections: int
    response_time: float
    throughput: float
    error_rate: float
    component_health: Dict[ComponentType, float]
    overall_health: float


class SystemMonitor:
    """Comprehensive system monitoring and health assessment."""

    def __init__(self, settings):
        """Initialize system monitor."""
        self.settings = settings
        self.logger = logging.getLogger(__name__)

        # Monitoring settings - handle both dict and MonitoringSettings
        if isinstance(settings, dict):
            self.monitoring_interval = settings.get('monitoring_interval_seconds', 60)
            self.health_check_interval = settings.get('health_check_interval_seconds', 300)
            self.cleanup_interval = settings.get('cleanup_interval_hours', 24) * 3600
            self.retention_days = settings.get('retention_days', 30)
        else:
            self.monitoring_interval = settings.monitoring_interval_seconds
            self.health_check_interval = settings.health_check_interval_seconds
            self.cleanup_interval = settings.cleanup_interval_hours * 3600
            self.retention_days = settings.retention_days

        # Thresholds
        if isinstance(settings, dict):
            self.thresholds = {
                'cpu_warning': settings.get('cpu_warning_threshold', 70),
                'cpu_critical': settings.get('cpu_critical_threshold', 90),
                'memory_warning': settings.get('memory_warning_threshold', 80),
                'memory_critical': settings.get('memory_critical_threshold', 95),
                'disk_warning': settings.get('disk_warning_threshold', 85),
                'disk_critical': settings.get('disk_critical_threshold', 95),
                'response_time_warning': settings.get('response_time_warning_ms', 1000),
                'response_time_critical': settings.get('response_time_critical_ms', 5000),
                'error_rate_warning': settings.get('error_rate_warning_threshold', 0.05),
                'error_rate_critical': settings.get('error_rate_critical_threshold', 0.10),
                'gpu_warning': settings.get('gpu_warning_threshold', 80),
                'gpu_critical': settings.get('gpu_critical_threshold', 95)
            }
        else:
            self.thresholds = {
                'cpu_warning': settings.cpu_warning_threshold,
                'cpu_critical': settings.cpu_critical_threshold,
                'memory_warning': settings.memory_warning_threshold,
                'memory_critical': settings.memory_critical_threshold,
                'disk_warning': settings.disk_warning_threshold,
                'disk_critical': settings.disk_critical_threshold,
                'response_time_warning': settings.response_time_warning_ms,
                'response_time_critical': settings.response_time_critical_ms,
                'error_rate_warning': settings.error_rate_warning_threshold,
                'error_rate_critical': settings.error_rate_critical_threshold,
                'gpu_warning': settings.gpu_warning_threshold,
                'gpu_critical': settings.gpu_critical_threshold
            }
        
        # Monitoring data
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.component_health: Dict[ComponentType, ComponentHealth] = {}
        self.performance_snapshots: deque = deque(maxlen=1000)
        self.active_alerts: Dict[str, SystemAlert] = {}
        self.alert_history: List[SystemAlert] = []
        
        # System info
        self.system_info = self._get_system_info()
        self.start_time = datetime.now()
        
        # Performance tracking
        self.request_times: deque = deque(maxlen=1000)
        self.error_counts: Dict[str, int] = defaultdict(int)
        self.throughput_counter = 0
        self.last_throughput_time = time.time()
        
        # Component checkers
        self.component_checkers: Dict[ComponentType, Callable] = {
            ComponentType.CPU: self._check_cpu_health,
            ComponentType.MEMORY: self._check_memory_health,
            ComponentType.DISK: self._check_disk_health,
            ComponentType.NETWORK: self._check_network_health,
            ComponentType.GPU: self._check_gpu_health,
        }
        
        # Custom component checkers (can be added by other modules)
        self.custom_checkers: Dict[ComponentType, Callable] = {}
        
        # Alert cooldowns
        self.alert_cooldowns: Dict[str, datetime] = {}
        if isinstance(settings, dict):
            self.cooldown_period = settings.get('alert_cooldown_seconds', 300)
        else:
            self.cooldown_period = settings.alert_cooldown_seconds
        
        # Monitoring state
        self.is_monitoring = False
        self.monitoring_tasks: List[asyncio.Task] = []
        
        self.logger.info("SystemMonitor initialized")
    
    def _get_system_info(self) -> Dict[str, Any]:
        """Get system information."""
        try:
            return {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'architecture': platform.architecture(),
                'python_version': sys.version,
                'cpu_count': psutil.cpu_count(),
                'memory_total': psutil.virtual_memory().total,
                'disk_total': sum(psutil.disk_usage(p.mountpoint).total for p in psutil.disk_partitions()),
                'boot_time': datetime.fromtimestamp(psutil.boot_time()),
                'hostname': platform.node()
            }
        except Exception as e:
            self.logger.error(f"Error getting system info: {e}")
            return {}
    
    async def start_monitoring(self):
        """Start system monitoring."""
        try:
            if self.is_monitoring:
                self.logger.warning("System monitoring already running")
                return
            
            self.is_monitoring = True
            
            # Start monitoring tasks
            self.monitoring_tasks = [
                asyncio.create_task(self._monitor_system_metrics()),
                asyncio.create_task(self._monitor_component_health()),
                asyncio.create_task(self._cleanup_old_data()),
                asyncio.create_task(self._generate_performance_snapshots())
            ]
            
            self.logger.info("System monitoring started")
            
        except Exception as e:
            self.logger.error(f"Error starting system monitoring: {e}")
            raise
    
    async def stop_monitoring(self):
        """Stop system monitoring."""
        try:
            self.is_monitoring = False
            
            # Cancel monitoring tasks
            for task in self.monitoring_tasks:
                if not task.done():
                    task.cancel()
            
            # Wait for tasks to complete
            if self.monitoring_tasks:
                await asyncio.gather(*self.monitoring_tasks, return_exceptions=True)
            
            self.monitoring_tasks.clear()
            self.logger.info("System monitoring stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping system monitoring: {e}")
    
    def register_component_checker(self, component: ComponentType, checker: Callable):
        """Register custom component health checker."""
        self.custom_checkers[component] = checker
        self.logger.info(f"Registered custom checker for {component.value}")
    
    def record_request_time(self, response_time_ms: float):
        """Record API request response time."""
        self.request_times.append((datetime.now(), response_time_ms))
    
    def record_error(self, error_type: str):
        """Record system error."""
        self.error_counts[error_type] += 1
    
    def record_throughput(self, count: int = 1):
        """Record throughput events."""
        self.throughput_counter += count
    
    async def _monitor_system_metrics(self):
        """Monitor basic system metrics."""
        while self.is_monitoring:
            try:
                await self._collect_system_metrics()
                await asyncio.sleep(self.monitoring_interval)
                
            except Exception as e:
                self.logger.error(f"Error in system metrics monitoring: {e}")
                await asyncio.sleep(self.monitoring_interval)
    
    async def _collect_system_metrics(self):
        """Collect system metrics."""
        try:
            timestamp = datetime.now()
            
            # CPU metrics
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_freq = psutil.cpu_freq()
            cpu_count = psutil.cpu_count()
            
            self._record_metric('cpu_usage', cpu_percent, '%', ComponentType.CPU, timestamp)
            if cpu_freq:
                self._record_metric('cpu_frequency', cpu_freq.current, 'MHz', ComponentType.CPU, timestamp)
            
            # Memory metrics
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            self._record_metric('memory_usage', memory.percent, '%', ComponentType.MEMORY, timestamp)
            self._record_metric('memory_available', memory.available / (1024**3), 'GB', ComponentType.MEMORY, timestamp)
            self._record_metric('swap_usage', swap.percent, '%', ComponentType.MEMORY, timestamp)
            
            # Disk metrics
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            self._record_metric('disk_usage', (disk_usage.used / disk_usage.total) * 100, '%', ComponentType.DISK, timestamp)
            self._record_metric('disk_free', disk_usage.free / (1024**3), 'GB', ComponentType.DISK, timestamp)
            
            if disk_io:
                self._record_metric('disk_read_bytes', disk_io.read_bytes, 'bytes', ComponentType.DISK, timestamp)
                self._record_metric('disk_write_bytes', disk_io.write_bytes, 'bytes', ComponentType.DISK, timestamp)
            
            # Network metrics
            network_io = psutil.net_io_counters()
            if network_io:
                self._record_metric('network_bytes_sent', network_io.bytes_sent, 'bytes', ComponentType.NETWORK, timestamp)
                self._record_metric('network_bytes_recv', network_io.bytes_recv, 'bytes', ComponentType.NETWORK, timestamp)
                self._record_metric('network_packets_sent', network_io.packets_sent, 'packets', ComponentType.NETWORK, timestamp)
                self._record_metric('network_packets_recv', network_io.packets_recv, 'packets', ComponentType.NETWORK, timestamp)
            
            # GPU metrics (if available)
            if GPUtil:
                try:
                    gpus = GPUtil.getGPUs()
                    for i, gpu in enumerate(gpus):
                        self._record_metric(f'gpu_{i}_usage', gpu.load * 100, '%', ComponentType.GPU, timestamp)
                        self._record_metric(f'gpu_{i}_memory', gpu.memoryUtil * 100, '%', ComponentType.GPU, timestamp)
                        self._record_metric(f'gpu_{i}_temperature', gpu.temperature, '°C', ComponentType.GPU, timestamp)
                except Exception as e:
                    self.logger.debug(f"Error collecting GPU metrics: {e}")
            
            # Process metrics
            process = psutil.Process()
            self._record_metric('process_cpu', process.cpu_percent(), '%', ComponentType.CPU, timestamp)
            self._record_metric('process_memory', process.memory_percent(), '%', ComponentType.MEMORY, timestamp)
            self._record_metric('process_threads', process.num_threads(), 'count', ComponentType.CPU, timestamp)
            
            # Python-specific metrics
            self._record_metric('gc_objects', len(gc.get_objects()), 'count', ComponentType.MEMORY, timestamp)
            
            # Performance metrics
            if self.request_times:
                recent_times = [rt[1] for rt in self.request_times if (timestamp - rt[0]).total_seconds() < 300]  # Last 5 minutes
                if recent_times:
                    avg_response_time = statistics.mean(recent_times)
                    self._record_metric('avg_response_time', avg_response_time, 'ms', ComponentType.API, timestamp)
            
            # Throughput metrics
            current_time = time.time()
            time_diff = current_time - self.last_throughput_time
            if time_diff >= 60:  # Calculate per minute
                throughput = self.throughput_counter / (time_diff / 60)
                self._record_metric('throughput', throughput, 'ops/min', ComponentType.API, timestamp)
                self.throughput_counter = 0
                self.last_throughput_time = current_time
            
            # Error rate
            total_errors = sum(self.error_counts.values())
            total_requests = len(self.request_times)
            if total_requests > 0:
                error_rate = total_errors / total_requests
                self._record_metric('error_rate', error_rate * 100, '%', ComponentType.API, timestamp)
            
        except Exception as e:
            self.logger.error(f"Error collecting system metrics: {e}")
    
    def _record_metric(self, name: str, value: float, unit: str, component: ComponentType, timestamp: datetime):
        """Record a system metric."""
        try:
            metric = SystemMetric(
                name=name,
                value=value,
                unit=unit,
                component=component,
                timestamp=timestamp,
                threshold_warning=self.thresholds.get(f"{name}_warning"),
                threshold_critical=self.thresholds.get(f"{name}_critical")
            )
            
            self.metrics_history[name].append(metric)
            
            # Check for threshold violations
            self._check_metric_thresholds(metric)
            
        except Exception as e:
            self.logger.error(f"Error recording metric {name}: {e}")
    
    def _check_metric_thresholds(self, metric: SystemMetric):
        """Check if metric violates thresholds."""
        try:
            alert_type = None
            severity = None
            threshold = None
            
            if metric.threshold_critical and metric.value >= metric.threshold_critical:
                alert_type = AlertType.PERFORMANCE
                severity = "critical"
                threshold = metric.threshold_critical
            elif metric.threshold_warning and metric.value >= metric.threshold_warning:
                alert_type = AlertType.PERFORMANCE
                severity = "warning"
                threshold = metric.threshold_warning
            
            if alert_type:
                asyncio.create_task(self._create_alert(
                    alert_type=alert_type,
                    component=metric.component,
                    severity=severity,
                    message=f"{metric.name} threshold exceeded: {metric.value:.2f}{metric.unit}",
                    metric_name=metric.name,
                    current_value=metric.value,
                    threshold=threshold
                ))
                
        except Exception as e:
            self.logger.error(f"Error checking metric thresholds: {e}")
    
    async def _monitor_component_health(self):
        """Monitor component health."""
        while self.is_monitoring:
            try:
                await self._check_all_components()
                await asyncio.sleep(self.health_check_interval)
                
            except Exception as e:
                self.logger.error(f"Error in component health monitoring: {e}")
                await asyncio.sleep(self.health_check_interval)
    
    async def _check_all_components(self):
        """Check health of all components."""
        try:
            # Check built-in components
            for component_type, checker in self.component_checkers.items():
                try:
                    health = await checker()
                    if health:
                        self.component_health[component_type] = health
                except Exception as e:
                    self.logger.error(f"Error checking {component_type.value} health: {e}")
            
            # Check custom components
            for component_type, checker in self.custom_checkers.items():
                try:
                    health = await checker()
                    if health:
                        self.component_health[component_type] = health
                except Exception as e:
                    self.logger.error(f"Error checking custom {component_type.value} health: {e}")
                    
        except Exception as e:
            self.logger.error(f"Error checking all components: {e}")
    
    async def _check_cpu_health(self) -> ComponentHealth:
        """Check CPU health."""
        try:
            # Get recent CPU metrics
            cpu_metrics = [m for m in self.metrics_history['cpu_usage'] if (datetime.now() - m.timestamp).total_seconds() < 300]
            
            if not cpu_metrics:
                return ComponentHealth(
                    component=ComponentType.CPU,
                    status=SystemStatus.WARNING,
                    score=50.0,
                    metrics={},
                    last_check=datetime.now(),
                    uptime=(datetime.now() - self.start_time).total_seconds(),
                    error_count=0,
                    warning_count=0,
                    message="No recent CPU metrics available"
                )
            
            avg_cpu = statistics.mean([m.value for m in cpu_metrics])
            max_cpu = max([m.value for m in cpu_metrics])
            
            # Calculate health score (0-100)
            if avg_cpu < self.thresholds['cpu_warning']:
                score = 100 - (avg_cpu / self.thresholds['cpu_warning']) * 30
                status = SystemStatus.HEALTHY
            elif avg_cpu < self.thresholds['cpu_critical']:
                score = 70 - ((avg_cpu - self.thresholds['cpu_warning']) / (self.thresholds['cpu_critical'] - self.thresholds['cpu_warning'])) * 40
                status = SystemStatus.WARNING
            else:
                score = 30 - min((avg_cpu - self.thresholds['cpu_critical']) / 10, 30)
                status = SystemStatus.CRITICAL
            
            return ComponentHealth(
                component=ComponentType.CPU,
                status=status,
                score=max(0, score),
                metrics={
                    'avg_usage': avg_cpu,
                    'max_usage': max_cpu,
                    'current_usage': cpu_metrics[-1].value
                },
                last_check=datetime.now(),
                uptime=(datetime.now() - self.start_time).total_seconds(),
                error_count=0,
                warning_count=len([m for m in cpu_metrics if m.value >= self.thresholds['cpu_warning']]),
                message=f"Average CPU usage: {avg_cpu:.1f}%"
            )
            
        except Exception as e:
            self.logger.error(f"Error checking CPU health: {e}")
            return ComponentHealth(
                component=ComponentType.CPU,
                status=SystemStatus.DEGRADED,
                score=0.0,
                metrics={},
                last_check=datetime.now(),
                uptime=0,
                error_count=1,
                warning_count=0,
                message=f"Error checking CPU health: {e}"
            )
    
    async def _check_memory_health(self) -> ComponentHealth:
        """Check memory health."""
        try:
            # Get recent memory metrics
            memory_metrics = [m for m in self.metrics_history['memory_usage'] if (datetime.now() - m.timestamp).total_seconds() < 300]
            
            if not memory_metrics:
                return ComponentHealth(
                    component=ComponentType.MEMORY,
                    status=SystemStatus.WARNING,
                    score=50.0,
                    metrics={},
                    last_check=datetime.now(),
                    uptime=(datetime.now() - self.start_time).total_seconds(),
                    error_count=0,
                    warning_count=0,
                    message="No recent memory metrics available"
                )
            
            avg_memory = statistics.mean([m.value for m in memory_metrics])
            max_memory = max([m.value for m in memory_metrics])
            
            # Calculate health score
            if avg_memory < self.thresholds['memory_warning']:
                score = 100 - (avg_memory / self.thresholds['memory_warning']) * 30
                status = SystemStatus.HEALTHY
            elif avg_memory < self.thresholds['memory_critical']:
                score = 70 - ((avg_memory - self.thresholds['memory_warning']) / (self.thresholds['memory_critical'] - self.thresholds['memory_warning'])) * 40
                status = SystemStatus.WARNING
            else:
                score = 30 - min((avg_memory - self.thresholds['memory_critical']) / 5, 30)
                status = SystemStatus.CRITICAL
            
            return ComponentHealth(
                component=ComponentType.MEMORY,
                status=status,
                score=max(0, score),
                metrics={
                    'avg_usage': avg_memory,
                    'max_usage': max_memory,
                    'current_usage': memory_metrics[-1].value
                },
                last_check=datetime.now(),
                uptime=(datetime.now() - self.start_time).total_seconds(),
                error_count=0,
                warning_count=len([m for m in memory_metrics if m.value >= self.thresholds['memory_warning']]),
                message=f"Average memory usage: {avg_memory:.1f}%"
            )
            
        except Exception as e:
            self.logger.error(f"Error checking memory health: {e}")
            return ComponentHealth(
                component=ComponentType.MEMORY,
                status=SystemStatus.DEGRADED,
                score=0.0,
                metrics={},
                last_check=datetime.now(),
                uptime=0,
                error_count=1,
                warning_count=0,
                message=f"Error checking memory health: {e}"
            )
    
    async def _check_disk_health(self) -> ComponentHealth:
        """Check disk health."""
        try:
            # Get recent disk metrics
            disk_metrics = [m for m in self.metrics_history['disk_usage'] if (datetime.now() - m.timestamp).total_seconds() < 300]
            
            if not disk_metrics:
                return ComponentHealth(
                    component=ComponentType.DISK,
                    status=SystemStatus.WARNING,
                    score=50.0,
                    metrics={},
                    last_check=datetime.now(),
                    uptime=(datetime.now() - self.start_time).total_seconds(),
                    error_count=0,
                    warning_count=0,
                    message="No recent disk metrics available"
                )
            
            avg_disk = statistics.mean([m.value for m in disk_metrics])
            max_disk = max([m.value for m in disk_metrics])
            
            # Calculate health score
            if avg_disk < self.thresholds['disk_warning']:
                score = 100 - (avg_disk / self.thresholds['disk_warning']) * 30
                status = SystemStatus.HEALTHY
            elif avg_disk < self.thresholds['disk_critical']:
                score = 70 - ((avg_disk - self.thresholds['disk_warning']) / (self.thresholds['disk_critical'] - self.thresholds['disk_warning'])) * 40
                status = SystemStatus.WARNING
            else:
                score = 30 - min((avg_disk - self.thresholds['disk_critical']) / 5, 30)
                status = SystemStatus.CRITICAL
            
            return ComponentHealth(
                component=ComponentType.DISK,
                status=status,
                score=max(0, score),
                metrics={
                    'avg_usage': avg_disk,
                    'max_usage': max_disk,
                    'current_usage': disk_metrics[-1].value
                },
                last_check=datetime.now(),
                uptime=(datetime.now() - self.start_time).total_seconds(),
                error_count=0,
                warning_count=len([m for m in disk_metrics if m.value >= self.thresholds['disk_warning']]),
                message=f"Average disk usage: {avg_disk:.1f}%"
            )
            
        except Exception as e:
            self.logger.error(f"Error checking disk health: {e}")
            return ComponentHealth(
                component=ComponentType.DISK,
                status=SystemStatus.DEGRADED,
                score=0.0,
                metrics={},
                last_check=datetime.now(),
                uptime=0,
                error_count=1,
                warning_count=0,
                message=f"Error checking disk health: {e}"
            )
    
    async def _check_network_health(self) -> ComponentHealth:
        """Check network health."""
        try:
            # Simple network connectivity check
            import socket
            
            try:
                # Try to connect to a reliable external service
                socket.create_connection(("8.8.8.8", 53), timeout=5)
                connectivity = True
            except:
                connectivity = False
            
            # Get network I/O metrics
            network_sent = [m for m in self.metrics_history['network_bytes_sent'] if (datetime.now() - m.timestamp).total_seconds() < 300]
            network_recv = [m for m in self.metrics_history['network_bytes_recv'] if (datetime.now() - m.timestamp).total_seconds() < 300]
            
            if connectivity:
                status = SystemStatus.HEALTHY
                score = 100.0
                message = "Network connectivity OK"
            else:
                status = SystemStatus.CRITICAL
                score = 0.0
                message = "Network connectivity failed"
            
            metrics = {
                'connectivity': 1.0 if connectivity else 0.0
            }
            
            if network_sent and len(network_sent) > 1:
                bytes_sent_rate = (network_sent[-1].value - network_sent[0].value) / len(network_sent)
                metrics['bytes_sent_rate'] = bytes_sent_rate
            
            if network_recv and len(network_recv) > 1:
                bytes_recv_rate = (network_recv[-1].value - network_recv[0].value) / len(network_recv)
                metrics['bytes_recv_rate'] = bytes_recv_rate
            
            return ComponentHealth(
                component=ComponentType.NETWORK,
                status=status,
                score=score,
                metrics=metrics,
                last_check=datetime.now(),
                uptime=(datetime.now() - self.start_time).total_seconds(),
                error_count=0 if connectivity else 1,
                warning_count=0,
                message=message
            )
            
        except Exception as e:
            self.logger.error(f"Error checking network health: {e}")
            return ComponentHealth(
                component=ComponentType.NETWORK,
                status=SystemStatus.DEGRADED,
                score=0.0,
                metrics={},
                last_check=datetime.now(),
                uptime=0,
                error_count=1,
                warning_count=0,
                message=f"Error checking network health: {e}"
            )
    
    async def _check_gpu_health(self) -> Optional[ComponentHealth]:
        """Check GPU health."""
        try:
            if not GPUtil:
                return None
            
            gpus = GPUtil.getGPUs()
            if not gpus:
                return None
            
            # Check first GPU (can be extended for multiple GPUs)
            gpu = gpus[0]
            
            gpu_usage = gpu.load * 100
            gpu_memory = gpu.memoryUtil * 100
            gpu_temp = gpu.temperature
            
            # Calculate health score based on usage and temperature
            if gpu_usage < self.thresholds['gpu_warning'] and gpu_temp < 80:
                score = 100 - (gpu_usage / self.thresholds['gpu_warning']) * 30
                status = SystemStatus.HEALTHY
            elif gpu_usage < self.thresholds['gpu_critical'] and gpu_temp < 90:
                score = 70 - ((gpu_usage - self.thresholds['gpu_warning']) / (self.thresholds['gpu_critical'] - self.thresholds['gpu_warning'])) * 40
                status = SystemStatus.WARNING
            else:
                score = 30 - min((gpu_usage - self.thresholds['gpu_critical']) / 10, 30)
                status = SystemStatus.CRITICAL
            
            return ComponentHealth(
                component=ComponentType.GPU,
                status=status,
                score=max(0, score),
                metrics={
                    'usage': gpu_usage,
                    'memory_usage': gpu_memory,
                    'temperature': gpu_temp,
                    'memory_total': gpu.memoryTotal,
                    'memory_used': gpu.memoryUsed
                },
                last_check=datetime.now(),
                uptime=(datetime.now() - self.start_time).total_seconds(),
                error_count=0,
                warning_count=1 if gpu_usage >= self.thresholds['gpu_warning'] else 0,
                message=f"GPU usage: {gpu_usage:.1f}%, Memory: {gpu_memory:.1f}%, Temp: {gpu_temp}°C"
            )
            
        except Exception as e:
            self.logger.error(f"Error checking GPU health: {e}")
            return ComponentHealth(
                component=ComponentType.GPU,
                status=SystemStatus.DEGRADED,
                score=0.0,
                metrics={},
                last_check=datetime.now(),
                uptime=0,
                error_count=1,
                warning_count=0,
                message=f"Error checking GPU health: {e}"
            )
    
    async def _create_alert(self, alert_type: AlertType, component: ComponentType, severity: str,
                          message: str, metric_name: str, current_value: float, threshold: float):
        """Create system alert."""
        try:
            # Check cooldown
            alert_key = f"{component.value}_{metric_name}_{severity}"
            now = datetime.now()
            
            if alert_key in self.alert_cooldowns:
                time_since_last = (now - self.alert_cooldowns[alert_key]).total_seconds()
                if time_since_last < self.cooldown_period:
                    return
            
            # Create alert
            alert_id = f"{alert_key}_{int(now.timestamp())}"
            alert = SystemAlert(
                alert_id=alert_id,
                alert_type=alert_type,
                component=component,
                severity=severity,
                message=message,
                metric_name=metric_name,
                current_value=current_value,
                threshold=threshold,
                timestamp=now
            )
            
            # Store alert
            self.active_alerts[alert_id] = alert
            self.alert_history.append(alert)
            self.alert_cooldowns[alert_key] = now
            
            # Log alert
            log_level = logging.CRITICAL if severity == "critical" else logging.WARNING
            self.logger.log(log_level, f"System alert: {message}")
            
            # Keep history manageable
            if len(self.alert_history) > 10000:
                self.alert_history = self.alert_history[-5000:]
                
        except Exception as e:
            self.logger.error(f"Error creating alert: {e}")
    
    async def _generate_performance_snapshots(self):
        """Generate periodic performance snapshots."""
        while self.is_monitoring:
            try:
                snapshot = await self._create_performance_snapshot()
                if snapshot:
                    self.performance_snapshots.append(snapshot)
                
                await asyncio.sleep(300)  # Every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error generating performance snapshot: {e}")
                await asyncio.sleep(300)
    
    async def _create_performance_snapshot(self) -> Optional[PerformanceSnapshot]:
        """Create performance snapshot."""
        try:
            timestamp = datetime.now()
            
            # Get latest metrics
            cpu_usage = self._get_latest_metric_value('cpu_usage', 0.0)
            memory_usage = self._get_latest_metric_value('memory_usage', 0.0)
            disk_usage = self._get_latest_metric_value('disk_usage', 0.0)
            
            # Network I/O
            network_io = {
                'bytes_sent': self._get_latest_metric_value('network_bytes_sent', 0.0),
                'bytes_recv': self._get_latest_metric_value('network_bytes_recv', 0.0)
            }
            
            # GPU usage (if available)
            gpu_usage = self._get_latest_metric_value('gpu_0_usage')
            
            # Performance metrics
            response_time = self._get_latest_metric_value('avg_response_time', 0.0)
            throughput = self._get_latest_metric_value('throughput', 0.0)
            error_rate = self._get_latest_metric_value('error_rate', 0.0)
            
            # Component health scores
            component_health = {
                comp_type: health.score
                for comp_type, health in self.component_health.items()
            }
            
            # Overall health score
            if component_health:
                overall_health = statistics.mean(component_health.values())
            else:
                overall_health = 50.0  # Default neutral score
            
            # Active connections (simplified)
            active_connections = len(psutil.net_connections())
            
            return PerformanceSnapshot(
                timestamp=timestamp,
                cpu_usage=cpu_usage,
                memory_usage=memory_usage,
                disk_usage=disk_usage,
                network_io=network_io,
                gpu_usage=gpu_usage,
                active_connections=active_connections,
                response_time=response_time,
                throughput=throughput,
                error_rate=error_rate,
                component_health=component_health,
                overall_health=overall_health
            )
            
        except Exception as e:
            self.logger.error(f"Error creating performance snapshot: {e}")
            return None
    
    def _get_latest_metric_value(self, metric_name: str, default: Optional[float] = None) -> Optional[float]:
        """Get latest value for a metric."""
        try:
            if metric_name in self.metrics_history and self.metrics_history[metric_name]:
                return self.metrics_history[metric_name][-1].value
            return default
        except Exception:
            return default
    
    async def _cleanup_old_data(self):
        """Clean up old monitoring data."""
        while self.is_monitoring:
            try:
                cutoff_date = datetime.now() - timedelta(days=self.retention_days)
                
                # Clean up metrics history
                for metric_name, metrics in self.metrics_history.items():
                    # Keep only recent metrics
                    while metrics and metrics[0].timestamp < cutoff_date:
                        metrics.popleft()
                
                # Clean up alert history
                self.alert_history = [a for a in self.alert_history if a.timestamp >= cutoff_date]
                
                # Clean up resolved alerts
                resolved_alerts = [aid for aid, alert in self.active_alerts.items() if alert.resolved]
                for alert_id in resolved_alerts:
                    del self.active_alerts[alert_id]
                
                await asyncio.sleep(self.cleanup_interval)
                
            except Exception as e:
                self.logger.error(f"Error cleaning up old data: {e}")
                await asyncio.sleep(self.cleanup_interval)
    
    # Public interface methods
    async def get_system_status(self) -> Dict[str, Any]:
        """Get current system status."""
        try:
            # Overall system health
            if self.component_health:
                health_scores = [h.score for h in self.component_health.values()]
                overall_health = statistics.mean(health_scores)
                
                if overall_health >= 80:
                    overall_status = SystemStatus.HEALTHY
                elif overall_health >= 60:
                    overall_status = SystemStatus.WARNING
                elif overall_health >= 30:
                    overall_status = SystemStatus.DEGRADED
                else:
                    overall_status = SystemStatus.CRITICAL
            else:
                overall_health = 0.0
                overall_status = SystemStatus.OFFLINE
            
            # Active alerts summary
            alert_counts = defaultdict(int)
            for alert in self.active_alerts.values():
                alert_counts[alert.severity] += 1
            
            # Component status summary
            component_status = {
                comp_type.value: {
                    'status': health.status.value,
                    'score': health.score,
                    'last_check': health.last_check.isoformat(),
                    'message': health.message
                }
                for comp_type, health in self.component_health.items()
            }
            
            # System uptime
            uptime = (datetime.now() - self.start_time).total_seconds()
            
            return {
                'timestamp': datetime.now().isoformat(),
                'overall_status': overall_status.value,
                'overall_health': overall_health,
                'uptime_seconds': uptime,
                'component_status': component_status,
                'active_alerts': {
                    'total': len(self.active_alerts),
                    'by_severity': dict(alert_counts)
                },
                'system_info': self.system_info,
                'monitoring_active': self.is_monitoring
            }
            
        except Exception as e:
            self.logger.error(f"Error getting system status: {e}")
            return {}
    
    async def get_component_health(self, component: ComponentType) -> Optional[Dict[str, Any]]:
        """Get health status for specific component."""
        try:
            if component not in self.component_health:
                return None
            
            health = self.component_health[component]
            
            return {
                'component': health.component.value,
                'status': health.status.value,
                'score': health.score,
                'metrics': health.metrics,
                'last_check': health.last_check.isoformat(),
                'uptime': health.uptime,
                'error_count': health.error_count,
                'warning_count': health.warning_count,
                'message': health.message,
                'metadata': health.metadata
            }
            
        except Exception as e:
            self.logger.error(f"Error getting component health: {e}")
            return None
    
    async def get_performance_metrics(self, hours: int = 1) -> Dict[str, Any]:
        """Get performance metrics for specified time period."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Get recent snapshots
            recent_snapshots = [s for s in self.performance_snapshots if s.timestamp >= cutoff_time]
            
            if not recent_snapshots:
                return {}
            
            # Calculate averages
            avg_cpu = statistics.mean([s.cpu_usage for s in recent_snapshots])
            avg_memory = statistics.mean([s.memory_usage for s in recent_snapshots])
            avg_response_time = statistics.mean([s.response_time for s in recent_snapshots if s.response_time > 0])
            avg_throughput = statistics.mean([s.throughput for s in recent_snapshots if s.throughput > 0])
            avg_error_rate = statistics.mean([s.error_rate for s in recent_snapshots])
            
            # Get trends
            if len(recent_snapshots) >= 2:
                first_half = recent_snapshots[:len(recent_snapshots)//2]
                second_half = recent_snapshots[len(recent_snapshots)//2:]
                
                cpu_trend = statistics.mean([s.cpu_usage for s in second_half]) - statistics.mean([s.cpu_usage for s in first_half])
                memory_trend = statistics.mean([s.memory_usage for s in second_half]) - statistics.mean([s.memory_usage for s in first_half])
            else:
                cpu_trend = 0.0
                memory_trend = 0.0
            
            return {
                'period_hours': hours,
                'snapshot_count': len(recent_snapshots),
                'averages': {
                    'cpu_usage': avg_cpu,
                    'memory_usage': avg_memory,
                    'response_time': avg_response_time,
                    'throughput': avg_throughput,
                    'error_rate': avg_error_rate
                },
                'trends': {
                    'cpu_usage': cpu_trend,
                    'memory_usage': memory_trend
                },
                'latest_snapshot': {
                    'timestamp': recent_snapshots[-1].timestamp.isoformat(),
                    'cpu_usage': recent_snapshots[-1].cpu_usage,
                    'memory_usage': recent_snapshots[-1].memory_usage,
                    'overall_health': recent_snapshots[-1].overall_health
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error getting performance metrics: {e}")
            return {}
    
    async def get_active_alerts(self, component: Optional[ComponentType] = None) -> List[Dict[str, Any]]:
        """Get active system alerts."""
        try:
            alerts = list(self.active_alerts.values())
            
            if component:
                alerts = [a for a in alerts if a.component == component]
            
            return [
                {
                    'alert_id': alert.alert_id,
                    'alert_type': alert.alert_type.value,
                    'component': alert.component.value,
                    'severity': alert.severity,
                    'message': alert.message,
                    'metric_name': alert.metric_name,
                    'current_value': alert.current_value,
                    'threshold': alert.threshold,
                    'timestamp': alert.timestamp.isoformat(),
                    'acknowledged': alert.acknowledged
                }
                for alert in sorted(alerts, key=lambda x: x.timestamp, reverse=True)
            ]
            
        except Exception as e:
            self.logger.error(f"Error getting active alerts: {e}")
            return []
    
    async def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge system alert."""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].acknowledged = True
                self.logger.info(f"System alert acknowledged: {alert_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error acknowledging alert: {e}")
            return False
    
    async def resolve_alert(self, alert_id: str) -> bool:
        """Manually resolve system alert."""
        try:
            if alert_id in self.active_alerts:
                self.active_alerts[alert_id].resolved = True
                del self.active_alerts[alert_id]
                self.logger.info(f"System alert resolved: {alert_id}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Error resolving alert: {e}")
            return False
    
    async def export_system_data(self, filepath: str, hours: int = 24) -> bool:
        """Export system monitoring data."""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            
            # Collect data
            export_data = {
                'export_timestamp': datetime.now().isoformat(),
                'period_hours': hours,
                'system_info': self.system_info,
                'current_status': await self.get_system_status(),
                'component_health': {
                    comp_type.value: await self.get_component_health(comp_type)
                    for comp_type in self.component_health.keys()
                },
                'performance_snapshots': [
                    {
                        'timestamp': s.timestamp.isoformat(),
                        'cpu_usage': s.cpu_usage,
                        'memory_usage': s.memory_usage,
                        'disk_usage': s.disk_usage,
                        'response_time': s.response_time,
                        'throughput': s.throughput,
                        'error_rate': s.error_rate,
                        'overall_health': s.overall_health
                    }
                    for s in self.performance_snapshots if s.timestamp >= cutoff_time
                ],
                'recent_alerts': [
                    {
                        'alert_id': alert.alert_id,
                        'alert_type': alert.alert_type.value,
                        'component': alert.component.value,
                        'severity': alert.severity,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat(),
                        'resolved': alert.resolved
                    }
                    for alert in self.alert_history if alert.timestamp >= cutoff_time
                ],
                'thresholds': self.thresholds
            }
            
            with open(filepath, 'w') as f:
                json.dump(export_data, f, indent=2)
            
            self.logger.info(f"System data exported to: {filepath}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error exporting system data: {e}")
            return False


class MonitoringDashboard:
    """Simple monitoring dashboard for the trading agent."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize monitoring dashboard."""
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.system_monitor = SystemMonitor(config)
        self.performance_monitor = PerformanceMonitor(config)
        
    async def start(self):
        """Start monitoring dashboard."""
        await self.system_monitor.start_monitoring()
        
    async def stop(self):
        """Stop monitoring dashboard."""
        await self.system_monitor.stop_monitoring()
        
    async def get_status(self) -> Dict[str, Any]:
        """Get current system status."""
        return await self.system_monitor.get_system_status()