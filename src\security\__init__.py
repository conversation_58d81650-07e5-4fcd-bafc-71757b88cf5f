"""Security Module for AI Trading Agent.

This module provides comprehensive security features including:
- Authentication and authorization
- API key management
- Data encryption and decryption
- Security monitoring and audit logging
- Access control and permissions
- Security validation and compliance

Author: inkbytefo
"""

from .auth_manager import (
    AuthManager,
    User,
    Role,
    Permission,
    AuthenticationMethod,
    SessionInfo,
    LoginAttempt,
    SecurityEvent
)

from .encryption_manager import (
    EncryptionManager,
    EncryptionMethod,
    KeyInfo,
    EncryptedData,
    CertificateInfo
)

from .api_security import (
    APISecurityManager,
    APIKey,
    APIKeyScope,
    APIKeyStatus,
    RateLimitConfig,
    SecurityPolicy
)

from .security_monitor import (
    SecurityMonitor,
    SecurityThreat,
    ThreatLevel,
    SecurityIncident,
    AuditLog,
    ComplianceCheck
)

__all__ = [
    # Authentication
    'AuthManager',
    'User',
    'Role',
    'Permission',
    'AuthenticationMethod',
    'SessionInfo',
    'LoginAttempt',
    'SecurityEvent',
    
    # Encryption
    'EncryptionManager',
    'EncryptionMethod',
    'KeyInfo',
    'EncryptedData',
    'CertificateInfo',
    
    # API Security
    'APISecurityManager',
    'APIKey',
    'APIKeyScope',
    'APIKeyStatus',
    'RateLimitConfig',
    'SecurityPolicy',
    
    # Security Monitoring
    'SecurityMonitor',
    'SecurityThreat',
    'ThreatLevel',
    'SecurityIncident',
    'AuditLog',
    'ComplianceCheck'
]