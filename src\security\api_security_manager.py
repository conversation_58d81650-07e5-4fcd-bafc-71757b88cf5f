"""API Security Manager for AI Trading Agent.

This module provides comprehensive API security including rate limiting,
API key management, request validation, CORS handling, and security monitoring.

Author: inkbytefo
"""

import time
import hmac
import hashlib
import secrets
import json
import re
from typing import Dict, List, Optional, Set, Any, Tuple, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from collections import defaultdict, deque
from urllib.parse import urlparse, parse_qs
import ipaddress


class SecurityLevel(Enum):
    """API security levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class RateLimitType(Enum):
    """Rate limiting types."""
    REQUESTS_PER_SECOND = "requests_per_second"
    REQUESTS_PER_MINUTE = "requests_per_minute"
    REQUESTS_PER_HOUR = "requests_per_hour"
    REQUESTS_PER_DAY = "requests_per_day"
    BANDWIDTH_PER_SECOND = "bandwidth_per_second"
    BANDWIDTH_PER_MINUTE = "bandwidth_per_minute"


class APIKeyStatus(Enum):
    """API key status."""
    ACTIVE = "active"
    INACTIVE = "inactive"
    SUSPENDED = "suspended"
    EXPIRED = "expired"
    REVOKED = "revoked"


class RequestMethod(Enum):
    """HTTP request methods."""
    GET = "GET"
    POST = "POST"
    PUT = "PUT"
    DELETE = "DELETE"
    PATCH = "PATCH"
    HEAD = "HEAD"
    OPTIONS = "OPTIONS"


class SecurityThreatType(Enum):
    """Security threat types."""
    RATE_LIMIT_EXCEEDED = "rate_limit_exceeded"
    INVALID_API_KEY = "invalid_api_key"
    SUSPICIOUS_PATTERN = "suspicious_pattern"
    SQL_INJECTION = "sql_injection"
    XSS_ATTEMPT = "xss_attempt"
    BRUTE_FORCE = "brute_force"
    DDoS_ATTACK = "ddos_attack"
    UNAUTHORIZED_ACCESS = "unauthorized_access"
    MALFORMED_REQUEST = "malformed_request"
    CORS_VIOLATION = "cors_violation"


@dataclass
class RateLimit:
    """Rate limiting configuration."""
    limit_type: RateLimitType
    limit: int
    window_seconds: int
    burst_limit: Optional[int] = None
    description: str = ""
    
    def __post_init__(self):
        if self.burst_limit is None:
            self.burst_limit = self.limit * 2


@dataclass
class APIKey:
    """API key information."""
    key_id: str
    key_hash: str
    name: str
    status: APIKeyStatus
    created_at: datetime
    expires_at: Optional[datetime] = None
    last_used_at: Optional[datetime] = None
    usage_count: int = 0
    rate_limits: List[RateLimit] = field(default_factory=list)
    allowed_ips: List[str] = field(default_factory=list)
    allowed_endpoints: List[str] = field(default_factory=list)
    permissions: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_valid(self) -> bool:
        """Check if API key is valid."""
        if self.status != APIKeyStatus.ACTIVE:
            return False
        
        if self.expires_at and datetime.utcnow() > self.expires_at:
            return False
        
        return True
    
    def can_access_endpoint(self, endpoint: str) -> bool:
        """Check if API key can access endpoint."""
        if not self.allowed_endpoints:
            return True
        
        for pattern in self.allowed_endpoints:
            if re.match(pattern, endpoint):
                return True
        
        return False
    
    def can_access_from_ip(self, ip: str) -> bool:
        """Check if API key can be used from IP."""
        if not self.allowed_ips:
            return True
        
        try:
            client_ip = ipaddress.ip_address(ip)
            for allowed_ip in self.allowed_ips:
                if '/' in allowed_ip:  # CIDR notation
                    if client_ip in ipaddress.ip_network(allowed_ip):
                        return True
                else:  # Single IP
                    if client_ip == ipaddress.ip_address(allowed_ip):
                        return True
        except ValueError:
            return False
        
        return False


@dataclass
class RequestInfo:
    """HTTP request information."""
    method: RequestMethod
    endpoint: str
    headers: Dict[str, str]
    query_params: Dict[str, List[str]]
    body: Optional[str] = None
    client_ip: str = ""
    user_agent: str = ""
    timestamp: datetime = field(default_factory=datetime.utcnow)
    size_bytes: int = 0
    api_key_id: Optional[str] = None
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'RequestInfo':
        """Create RequestInfo from dictionary."""
        return cls(
            method=RequestMethod(data.get('method', 'GET')),
            endpoint=data.get('endpoint', ''),
            headers=data.get('headers', {}),
            query_params=data.get('query_params', {}),
            body=data.get('body'),
            client_ip=data.get('client_ip', ''),
            user_agent=data.get('user_agent', ''),
            size_bytes=data.get('size_bytes', 0),
            api_key_id=data.get('api_key_id')
        )


@dataclass
class SecurityThreat:
    """Security threat information."""
    threat_id: str
    threat_type: SecurityThreatType
    severity: SecurityLevel
    description: str
    client_ip: str
    endpoint: str
    timestamp: datetime = field(default_factory=datetime.utcnow)
    request_info: Optional[RequestInfo] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_blocked: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            'threat_id': self.threat_id,
            'threat_type': self.threat_type.value,
            'severity': self.severity.value,
            'description': self.description,
            'client_ip': self.client_ip,
            'endpoint': self.endpoint,
            'timestamp': self.timestamp.isoformat(),
            'metadata': self.metadata,
            'is_blocked': self.is_blocked
        }


@dataclass
class RateLimitStatus:
    """Rate limit status for a client."""
    client_id: str
    limit_type: RateLimitType
    current_count: int
    limit: int
    window_start: datetime
    window_seconds: int
    is_exceeded: bool = False
    
    def reset_if_expired(self):
        """Reset counter if window has expired."""
        if datetime.utcnow() > self.window_start + timedelta(seconds=self.window_seconds):
            self.current_count = 0
            self.window_start = datetime.utcnow()
            self.is_exceeded = False
    
    def increment(self) -> bool:
        """Increment counter and check if limit exceeded."""
        self.reset_if_expired()
        self.current_count += 1
        self.is_exceeded = self.current_count > self.limit
        return not self.is_exceeded


@dataclass
class CORSConfig:
    """CORS configuration."""
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    allowed_methods: List[str] = field(default_factory=lambda: ["GET", "POST", "PUT", "DELETE"])
    allowed_headers: List[str] = field(default_factory=lambda: ["Content-Type", "Authorization"])
    exposed_headers: List[str] = field(default_factory=list)
    allow_credentials: bool = False
    max_age: int = 86400  # 24 hours
    
    def is_origin_allowed(self, origin: str) -> bool:
        """Check if origin is allowed."""
        if "*" in self.allowed_origins:
            return True
        return origin in self.allowed_origins
    
    def is_method_allowed(self, method: str) -> bool:
        """Check if method is allowed."""
        return method.upper() in [m.upper() for m in self.allowed_methods]


class APISecurityManager:
    """API security manager for comprehensive API protection."""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """Initialize API security manager."""
        self.config = config or {}
        self.logger = logging.getLogger(__name__)
        
        # API keys storage
        self.api_keys: Dict[str, APIKey] = {}
        self.key_hashes: Dict[str, str] = {}  # hash -> key_id mapping
        
        # Rate limiting
        self.rate_limits: Dict[str, List[RateLimitStatus]] = defaultdict(list)
        self.global_rate_limits: List[RateLimit] = []
        
        # Security monitoring
        self.threats: List[SecurityThreat] = []
        self.blocked_ips: Set[str] = set()
        self.suspicious_ips: Dict[str, int] = defaultdict(int)
        
        # Request tracking
        self.request_history: deque = deque(maxlen=10000)
        self.failed_attempts: Dict[str, List[datetime]] = defaultdict(list)
        
        # CORS configuration
        self.cors_config = CORSConfig()
        
        # Security patterns
        self.sql_injection_patterns = [
            r"('|(\-\-)|(;)|(\||\|)|(\*|\*))",
            r"(union|select|insert|delete|update|drop|create|alter|exec|execute)",
            r"(script|javascript|vbscript|onload|onerror|onclick)"
        ]
        
        self.xss_patterns = [
            r"<script[^>]*>.*?</script>",
            r"javascript:",
            r"on\w+\s*=",
            r"<iframe[^>]*>.*?</iframe>"
        ]
        
        # Load configuration
        self._load_config()
        
        self.logger.info("API Security Manager initialized")
    
    def _load_config(self):
        """Load security configuration."""
        try:
            # Load global rate limits
            if 'rate_limits' in self.config:
                for limit_config in self.config['rate_limits']:
                    rate_limit = RateLimit(
                        limit_type=RateLimitType(limit_config['type']),
                        limit=limit_config['limit'],
                        window_seconds=limit_config['window_seconds'],
                        burst_limit=limit_config.get('burst_limit'),
                        description=limit_config.get('description', '')
                    )
                    self.global_rate_limits.append(rate_limit)
            
            # Load CORS configuration
            if 'cors' in self.config:
                cors_config = self.config['cors']
                self.cors_config = CORSConfig(
                    allowed_origins=cors_config.get('allowed_origins', ["*"]),
                    allowed_methods=cors_config.get('allowed_methods', ["GET", "POST", "PUT", "DELETE"]),
                    allowed_headers=cors_config.get('allowed_headers', ["Content-Type", "Authorization"]),
                    exposed_headers=cors_config.get('exposed_headers', []),
                    allow_credentials=cors_config.get('allow_credentials', False),
                    max_age=cors_config.get('max_age', 86400)
                )
            
            # Load blocked IPs
            if 'blocked_ips' in self.config:
                self.blocked_ips.update(self.config['blocked_ips'])
            
        except Exception as e:
            self.logger.error(f"Error loading security config: {e}")
    
    def generate_api_key(self, name: str, permissions: List[str] = None,
                        expires_in_days: Optional[int] = None,
                        rate_limits: List[RateLimit] = None,
                        allowed_ips: List[str] = None,
                        allowed_endpoints: List[str] = None) -> Tuple[str, str]:
        """Generate a new API key."""
        try:
            # Generate key ID and secret
            key_id = f"ak_{secrets.token_urlsafe(16)}"
            key_secret = secrets.token_urlsafe(32)
            
            # Hash the secret
            key_hash = hashlib.sha256(key_secret.encode()).hexdigest()
            
            # Calculate expiration
            expires_at = None
            if expires_in_days:
                expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
            
            # Create API key
            api_key = APIKey(
                key_id=key_id,
                key_hash=key_hash,
                name=name,
                status=APIKeyStatus.ACTIVE,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                rate_limits=rate_limits or [],
                allowed_ips=allowed_ips or [],
                allowed_endpoints=allowed_endpoints or [],
                permissions=permissions or []
            )
            
            # Store API key
            self.api_keys[key_id] = api_key
            self.key_hashes[key_hash] = key_id
            
            self.logger.info(f"Generated API key '{name}' with ID: {key_id}")
            return key_id, key_secret
            
        except Exception as e:
            self.logger.error(f"Error generating API key: {e}")
            return "", ""
    
    def validate_api_key(self, key_secret: str, client_ip: str = "",
                        endpoint: str = "") -> Tuple[bool, Optional[str], str]:
        """Validate API key and return validation result."""
        try:
            # Hash the provided secret
            key_hash = hashlib.sha256(key_secret.encode()).hexdigest()
            
            # Find API key
            key_id = self.key_hashes.get(key_hash)
            if not key_id:
                self._record_threat(
                    SecurityThreatType.INVALID_API_KEY,
                    "Invalid API key provided",
                    client_ip,
                    endpoint,
                    SecurityLevel.MEDIUM
                )
                return False, None, "Invalid API key"
            
            api_key = self.api_keys[key_id]
            
            # Check if key is valid
            if not api_key.is_valid():
                reason = f"API key status: {api_key.status.value}"
                if api_key.expires_at and datetime.utcnow() > api_key.expires_at:
                    reason = "API key expired"
                
                self._record_threat(
                    SecurityThreatType.INVALID_API_KEY,
                    reason,
                    client_ip,
                    endpoint,
                    SecurityLevel.MEDIUM
                )
                return False, None, reason
            
            # Check IP restrictions
            if client_ip and not api_key.can_access_from_ip(client_ip):
                self._record_threat(
                    SecurityThreatType.UNAUTHORIZED_ACCESS,
                    f"API key access denied from IP: {client_ip}",
                    client_ip,
                    endpoint,
                    SecurityLevel.HIGH
                )
                return False, None, "Access denied from this IP"
            
            # Check endpoint restrictions
            if endpoint and not api_key.can_access_endpoint(endpoint):
                self._record_threat(
                    SecurityThreatType.UNAUTHORIZED_ACCESS,
                    f"API key access denied to endpoint: {endpoint}",
                    client_ip,
                    endpoint,
                    SecurityLevel.HIGH
                )
                return False, None, "Access denied to this endpoint"
            
            # Update usage
            api_key.last_used_at = datetime.utcnow()
            api_key.usage_count += 1
            
            return True, key_id, "Valid"
            
        except Exception as e:
            self.logger.error(f"Error validating API key: {e}")
            return False, None, "Validation error"
    
    def check_rate_limits(self, client_id: str, request_size: int = 0) -> Tuple[bool, str]:
        """Check rate limits for client."""
        try:
            # Check global rate limits
            for rate_limit in self.global_rate_limits:
                if not self._check_single_rate_limit(client_id, rate_limit, request_size):
                    self._record_threat(
                        SecurityThreatType.RATE_LIMIT_EXCEEDED,
                        f"Rate limit exceeded: {rate_limit.limit_type.value}",
                        client_id,
                        "",
                        SecurityLevel.MEDIUM
                    )
                    return False, f"Rate limit exceeded: {rate_limit.limit_type.value}"
            
            # Check API key specific rate limits
            if client_id in self.api_keys:
                api_key = self.api_keys[client_id]
                for rate_limit in api_key.rate_limits:
                    if not self._check_single_rate_limit(client_id, rate_limit, request_size):
                        self._record_threat(
                            SecurityThreatType.RATE_LIMIT_EXCEEDED,
                            f"API key rate limit exceeded: {rate_limit.limit_type.value}",
                            client_id,
                            "",
                            SecurityLevel.MEDIUM
                        )
                        return False, f"API key rate limit exceeded: {rate_limit.limit_type.value}"
            
            return True, "OK"
            
        except Exception as e:
            self.logger.error(f"Error checking rate limits: {e}")
            return False, "Rate limit check error"
    
    def _check_single_rate_limit(self, client_id: str, rate_limit: RateLimit, request_size: int) -> bool:
        """Check a single rate limit."""
        try:
            # Find existing rate limit status
            rate_limit_status = None
            for status in self.rate_limits[client_id]:
                if status.limit_type == rate_limit.limit_type:
                    rate_limit_status = status
                    break
            
            # Create new status if not found
            if not rate_limit_status:
                rate_limit_status = RateLimitStatus(
                    client_id=client_id,
                    limit_type=rate_limit.limit_type,
                    current_count=0,
                    limit=rate_limit.limit,
                    window_start=datetime.utcnow(),
                    window_seconds=rate_limit.window_seconds
                )
                self.rate_limits[client_id].append(rate_limit_status)
            
            # Check bandwidth limits
            if rate_limit.limit_type in [RateLimitType.BANDWIDTH_PER_SECOND, RateLimitType.BANDWIDTH_PER_MINUTE]:
                return rate_limit_status.increment() and (rate_limit_status.current_count + request_size) <= rate_limit.limit
            
            # Check request limits
            return rate_limit_status.increment()
            
        except Exception as e:
            self.logger.error(f"Error checking single rate limit: {e}")
            return False
    
    def validate_request(self, request_info: RequestInfo) -> Tuple[bool, str]:
        """Validate HTTP request for security threats."""
        try:
            # Check if IP is blocked
            if request_info.client_ip in self.blocked_ips:
                self._record_threat(
                    SecurityThreatType.UNAUTHORIZED_ACCESS,
                    "Request from blocked IP",
                    request_info.client_ip,
                    request_info.endpoint,
                    SecurityLevel.HIGH
                )
                return False, "IP blocked"
            
            # Check for SQL injection
            if self._detect_sql_injection(request_info):
                self._record_threat(
                    SecurityThreatType.SQL_INJECTION,
                    "SQL injection attempt detected",
                    request_info.client_ip,
                    request_info.endpoint,
                    SecurityLevel.HIGH
                )
                return False, "SQL injection detected"
            
            # Check for XSS
            if self._detect_xss(request_info):
                self._record_threat(
                    SecurityThreatType.XSS_ATTEMPT,
                    "XSS attempt detected",
                    request_info.client_ip,
                    request_info.endpoint,
                    SecurityLevel.HIGH
                )
                return False, "XSS attempt detected"
            
            # Check for suspicious patterns
            if self._detect_suspicious_patterns(request_info):
                self._record_threat(
                    SecurityThreatType.SUSPICIOUS_PATTERN,
                    "Suspicious request pattern detected",
                    request_info.client_ip,
                    request_info.endpoint,
                    SecurityLevel.MEDIUM
                )
                return False, "Suspicious pattern detected"
            
            # Check for brute force
            if self._detect_brute_force(request_info):
                self._record_threat(
                    SecurityThreatType.BRUTE_FORCE,
                    "Brute force attack detected",
                    request_info.client_ip,
                    request_info.endpoint,
                    SecurityLevel.HIGH
                )
                return False, "Brute force detected"
            
            # Record request
            self.request_history.append(request_info)
            
            return True, "Valid request"
            
        except Exception as e:
            self.logger.error(f"Error validating request: {e}")
            return False, "Request validation error"
    
    def _detect_sql_injection(self, request_info: RequestInfo) -> bool:
        """Detect SQL injection attempts."""
        try:
            # Check query parameters
            for param_values in request_info.query_params.values():
                for value in param_values:
                    for pattern in self.sql_injection_patterns:
                        if re.search(pattern, value, re.IGNORECASE):
                            return True
            
            # Check request body
            if request_info.body:
                for pattern in self.sql_injection_patterns:
                    if re.search(pattern, request_info.body, re.IGNORECASE):
                        return True
            
            return False
            
        except Exception:
            return False
    
    def _detect_xss(self, request_info: RequestInfo) -> bool:
        """Detect XSS attempts."""
        try:
            # Check query parameters
            for param_values in request_info.query_params.values():
                for value in param_values:
                    for pattern in self.xss_patterns:
                        if re.search(pattern, value, re.IGNORECASE):
                            return True
            
            # Check request body
            if request_info.body:
                for pattern in self.xss_patterns:
                    if re.search(pattern, request_info.body, re.IGNORECASE):
                        return True
            
            return False
            
        except Exception:
            return False
    
    def _detect_suspicious_patterns(self, request_info: RequestInfo) -> bool:
        """Detect suspicious request patterns."""
        try:
            # Check for unusual user agents
            suspicious_agents = ['bot', 'crawler', 'spider', 'scraper']
            user_agent = request_info.user_agent.lower()
            if any(agent in user_agent for agent in suspicious_agents):
                return True
            
            # Check for unusual request sizes
            if request_info.size_bytes > 10 * 1024 * 1024:  # 10MB
                return True
            
            # Check for unusual endpoints
            suspicious_endpoints = ['/admin', '/config', '/debug', '/.env', '/backup']
            if any(endpoint in request_info.endpoint for endpoint in suspicious_endpoints):
                return True
            
            return False
            
        except Exception:
            return False
    
    def _detect_brute_force(self, request_info: RequestInfo) -> bool:
        """Detect brute force attacks."""
        try:
            # Track failed attempts from IP
            if request_info.client_ip:
                now = datetime.utcnow()
                
                # Clean old attempts (older than 1 hour)
                cutoff = now - timedelta(hours=1)
                self.failed_attempts[request_info.client_ip] = [
                    attempt for attempt in self.failed_attempts[request_info.client_ip]
                    if attempt > cutoff
                ]
                
                # Check if too many attempts
                if len(self.failed_attempts[request_info.client_ip]) > 50:  # 50 attempts per hour
                    return True
            
            return False
            
        except Exception:
            return False
    
    def _record_threat(self, threat_type: SecurityThreatType, description: str,
                      client_ip: str, endpoint: str, severity: SecurityLevel,
                      request_info: Optional[RequestInfo] = None):
        """Record a security threat."""
        try:
            threat = SecurityThreat(
                threat_id=f"threat_{secrets.token_urlsafe(8)}",
                threat_type=threat_type,
                severity=severity,
                description=description,
                client_ip=client_ip,
                endpoint=endpoint,
                request_info=request_info
            )
            
            self.threats.append(threat)
            
            # Auto-block for high severity threats
            if severity in [SecurityLevel.HIGH, SecurityLevel.CRITICAL]:
                self.block_ip(client_ip, f"Auto-blocked due to {threat_type.value}")
            
            # Increment suspicious IP counter
            if client_ip:
                self.suspicious_ips[client_ip] += 1
                
                # Block if too many threats from same IP
                if self.suspicious_ips[client_ip] > 10:
                    self.block_ip(client_ip, "Too many security threats")
            
            self.logger.warning(f"Security threat detected: {threat_type.value} from {client_ip}")
            
        except Exception as e:
            self.logger.error(f"Error recording threat: {e}")
    
    def validate_cors(self, origin: str, method: str, headers: List[str] = None) -> Tuple[bool, Dict[str, str]]:
        """Validate CORS request and return headers."""
        try:
            cors_headers = {}
            
            # Check origin
            if not self.cors_config.is_origin_allowed(origin):
                self._record_threat(
                    SecurityThreatType.CORS_VIOLATION,
                    f"CORS violation: origin {origin} not allowed",
                    "",
                    "",
                    SecurityLevel.MEDIUM
                )
                return False, {}
            
            # Check method
            if not self.cors_config.is_method_allowed(method):
                self._record_threat(
                    SecurityThreatType.CORS_VIOLATION,
                    f"CORS violation: method {method} not allowed",
                    "",
                    "",
                    SecurityLevel.MEDIUM
                )
                return False, {}
            
            # Set CORS headers
            if origin != "*":
                cors_headers['Access-Control-Allow-Origin'] = origin
            else:
                cors_headers['Access-Control-Allow-Origin'] = "*"
            
            cors_headers['Access-Control-Allow-Methods'] = ', '.join(self.cors_config.allowed_methods)
            cors_headers['Access-Control-Allow-Headers'] = ', '.join(self.cors_config.allowed_headers)
            
            if self.cors_config.exposed_headers:
                cors_headers['Access-Control-Expose-Headers'] = ', '.join(self.cors_config.exposed_headers)
            
            if self.cors_config.allow_credentials:
                cors_headers['Access-Control-Allow-Credentials'] = 'true'
            
            cors_headers['Access-Control-Max-Age'] = str(self.cors_config.max_age)
            
            return True, cors_headers
            
        except Exception as e:
            self.logger.error(f"Error validating CORS: {e}")
            return False, {}
    
    def block_ip(self, ip: str, reason: str = ""):
        """Block an IP address."""
        try:
            self.blocked_ips.add(ip)
            self.logger.warning(f"Blocked IP {ip}: {reason}")
            
        except Exception as e:
            self.logger.error(f"Error blocking IP: {e}")
    
    def unblock_ip(self, ip: str):
        """Unblock an IP address."""
        try:
            self.blocked_ips.discard(ip)
            self.logger.info(f"Unblocked IP {ip}")
            
        except Exception as e:
            self.logger.error(f"Error unblocking IP: {e}")
    
    def revoke_api_key(self, key_id: str) -> bool:
        """Revoke an API key."""
        try:
            if key_id not in self.api_keys:
                return False
            
            api_key = self.api_keys[key_id]
            api_key.status = APIKeyStatus.REVOKED
            
            # Remove from hash mapping
            if api_key.key_hash in self.key_hashes:
                del self.key_hashes[api_key.key_hash]
            
            self.logger.info(f"Revoked API key: {key_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error revoking API key: {e}")
            return False
    
    def get_api_key_info(self, key_id: str) -> Optional[APIKey]:
        """Get API key information."""
        return self.api_keys.get(key_id)
    
    def list_api_keys(self) -> List[APIKey]:
        """List all API keys."""
        return list(self.api_keys.values())
    
    def get_security_threats(self, limit: int = 100) -> List[SecurityThreat]:
        """Get recent security threats."""
        return self.threats[-limit:]
    
    def get_blocked_ips(self) -> Set[str]:
        """Get blocked IP addresses."""
        return self.blocked_ips.copy()
    
    def get_rate_limit_status(self, client_id: str) -> List[RateLimitStatus]:
        """Get rate limit status for client."""
        return self.rate_limits.get(client_id, [])
    
    def cleanup_old_data(self, days: int = 30):
        """Clean up old security data."""
        try:
            cutoff = datetime.utcnow() - timedelta(days=days)
            
            # Clean old threats
            self.threats = [threat for threat in self.threats if threat.timestamp > cutoff]
            
            # Clean old failed attempts
            for ip in list(self.failed_attempts.keys()):
                self.failed_attempts[ip] = [
                    attempt for attempt in self.failed_attempts[ip]
                    if attempt > cutoff
                ]
                if not self.failed_attempts[ip]:
                    del self.failed_attempts[ip]
            
            # Clean old rate limit data
            for client_id in list(self.rate_limits.keys()):
                self.rate_limits[client_id] = [
                    status for status in self.rate_limits[client_id]
                    if status.window_start > cutoff
                ]
                if not self.rate_limits[client_id]:
                    del self.rate_limits[client_id]
            
            self.logger.info(f"Cleaned up security data older than {days} days")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up old data: {e}")
    
    def get_security_stats(self) -> Dict[str, Any]:
        """Get security statistics."""
        try:
            total_threats = len(self.threats)
            threat_types = {}
            for threat in self.threats:
                threat_type = threat.threat_type.value
                threat_types[threat_type] = threat_types.get(threat_type, 0) + 1
            
            severity_counts = {}
            for threat in self.threats:
                severity = threat.severity.value
                severity_counts[severity] = severity_counts.get(severity, 0) + 1
            
            active_keys = sum(1 for key in self.api_keys.values() if key.status == APIKeyStatus.ACTIVE)
            total_requests = len(self.request_history)
            
            return {
                'total_api_keys': len(self.api_keys),
                'active_api_keys': active_keys,
                'total_threats': total_threats,
                'threat_types': threat_types,
                'severity_distribution': severity_counts,
                'blocked_ips': len(self.blocked_ips),
                'suspicious_ips': len(self.suspicious_ips),
                'total_requests': total_requests,
                'rate_limited_clients': len(self.rate_limits)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting security stats: {e}")
            return {}
    
    def export_security_data(self) -> Dict[str, Any]:
        """Export security data for backup or analysis."""
        try:
            return {
                'api_keys': {
                    key_id: {
                        'key_id': key.key_id,
                        'name': key.name,
                        'status': key.status.value,
                        'created_at': key.created_at.isoformat(),
                        'expires_at': key.expires_at.isoformat() if key.expires_at else None,
                        'last_used_at': key.last_used_at.isoformat() if key.last_used_at else None,
                        'usage_count': key.usage_count,
                        'permissions': key.permissions,
                        'allowed_ips': key.allowed_ips,
                        'allowed_endpoints': key.allowed_endpoints,
                        'metadata': key.metadata
                    }
                    for key_id, key in self.api_keys.items()
                },
                'threats': [threat.to_dict() for threat in self.threats],
                'blocked_ips': list(self.blocked_ips),
                'suspicious_ips': dict(self.suspicious_ips),
                'cors_config': {
                    'allowed_origins': self.cors_config.allowed_origins,
                    'allowed_methods': self.cors_config.allowed_methods,
                    'allowed_headers': self.cors_config.allowed_headers,
                    'exposed_headers': self.cors_config.exposed_headers,
                    'allow_credentials': self.cors_config.allow_credentials,
                    'max_age': self.cors_config.max_age
                }
            }
            
        except Exception as e:
            self.logger.error(f"Error exporting security data: {e}")
            return {}