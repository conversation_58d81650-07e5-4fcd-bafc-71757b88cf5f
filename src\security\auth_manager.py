"""Authentication and Authorization Manager for AI Trading Agent.

This module provides comprehensive authentication, authorization, and user management
functionalities including multi-factor authentication, role-based access control,
session management, and security event logging.

Author: inkbytefo
"""

import os
import hashlib
import secrets
import jwt
import pyotp
import qrcode
from typing import Dict, List, Optional, Set, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
import json
import asyncio
from pathlib import Path
import bcrypt
from cryptography.fernet import Fernet
import base64
from io import BytesIO


class AuthenticationMethod(Enum):
    """Authentication methods."""
    PASSWORD = "password"
    API_KEY = "api_key"
    JWT_TOKEN = "jwt_token"
    OAUTH2 = "oauth2"
    LDAP = "ldap"
    MULTI_FACTOR = "multi_factor"
    BIOMETRIC = "biometric"
    CERTIFICATE = "certificate"


class Permission(Enum):
    """System permissions."""
    # Trading permissions
    TRADE_VIEW = "trade_view"
    TRADE_EXECUTE = "trade_execute"
    TRADE_CANCEL = "trade_cancel"
    TRADE_MODIFY = "trade_modify"
    
    # Portfolio permissions
    PORTFOLIO_VIEW = "portfolio_view"
    PORTFOLIO_MODIFY = "portfolio_modify"
    
    # Strategy permissions
    STRATEGY_VIEW = "strategy_view"
    STRATEGY_CREATE = "strategy_create"
    STRATEGY_MODIFY = "strategy_modify"
    STRATEGY_DELETE = "strategy_delete"
    STRATEGY_EXECUTE = "strategy_execute"
    
    # Data permissions
    DATA_VIEW = "data_view"
    DATA_EXPORT = "data_export"
    DATA_IMPORT = "data_import"
    
    # Configuration permissions
    CONFIG_VIEW = "config_view"
    CONFIG_MODIFY = "config_modify"
    
    # User management permissions
    USER_VIEW = "user_view"
    USER_CREATE = "user_create"
    USER_MODIFY = "user_modify"
    USER_DELETE = "user_delete"
    
    # System permissions
    SYSTEM_ADMIN = "system_admin"
    SYSTEM_MONITOR = "system_monitor"
    SYSTEM_BACKUP = "system_backup"
    
    # API permissions
    API_ACCESS = "api_access"
    API_ADMIN = "api_admin"
    
    # Audit permissions
    AUDIT_VIEW = "audit_view"
    AUDIT_EXPORT = "audit_export"


@dataclass
class Role:
    """User role with permissions."""
    name: str
    description: str
    permissions: Set[Permission]
    is_active: bool = True
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if role has a specific permission."""
        return permission in self.permissions
    
    def add_permission(self, permission: Permission):
        """Add a permission to the role."""
        self.permissions.add(permission)
        self.updated_at = datetime.utcnow()
    
    def remove_permission(self, permission: Permission):
        """Remove a permission from the role."""
        self.permissions.discard(permission)
        self.updated_at = datetime.utcnow()


@dataclass
class User:
    """User account information."""
    username: str
    email: str
    password_hash: str
    roles: Set[str]
    is_active: bool = True
    is_verified: bool = False
    mfa_enabled: bool = False
    mfa_secret: Optional[str] = None
    api_keys: List[str] = field(default_factory=list)
    last_login: Optional[datetime] = None
    login_attempts: int = 0
    locked_until: Optional[datetime] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        return (self.locked_until is not None and 
                self.locked_until > datetime.utcnow())
    
    def unlock(self):
        """Unlock user account."""
        self.locked_until = None
        self.login_attempts = 0
        self.updated_at = datetime.utcnow()
    
    def lock(self, duration_minutes: int = 30):
        """Lock user account for specified duration."""
        self.locked_until = datetime.utcnow() + timedelta(minutes=duration_minutes)
        self.updated_at = datetime.utcnow()


@dataclass
class SessionInfo:
    """User session information."""
    session_id: str
    user_id: str
    username: str
    roles: Set[str]
    permissions: Set[Permission]
    created_at: datetime
    expires_at: datetime
    last_activity: datetime
    ip_address: str
    user_agent: str
    is_active: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def extend_session(self, duration_hours: int = 24):
        """Extend session expiration."""
        self.expires_at = datetime.utcnow() + timedelta(hours=duration_hours)
        self.last_activity = datetime.utcnow()
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if session has a specific permission."""
        return permission in self.permissions


@dataclass
class LoginAttempt:
    """Login attempt record."""
    username: str
    ip_address: str
    user_agent: str
    success: bool
    method: AuthenticationMethod
    timestamp: datetime = field(default_factory=datetime.utcnow)
    failure_reason: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SecurityEvent:
    """Security event record."""
    event_type: str
    user_id: Optional[str]
    username: Optional[str]
    description: str
    severity: str
    ip_address: Optional[str]
    timestamp: datetime = field(default_factory=datetime.utcnow)
    metadata: Dict[str, Any] = field(default_factory=dict)


class AuthManager:
    """Authentication and authorization manager."""
    
    def __init__(self, config_dir: str = "config", secret_key: Optional[str] = None):
        """Initialize authentication manager."""
        self.config_dir = Path(config_dir)
        self.logger = logging.getLogger(__name__)
        
        # Security configuration
        self.secret_key = secret_key or os.getenv('AUTH_SECRET_KEY') or self._generate_secret_key()
        self.jwt_algorithm = 'HS256'
        self.session_duration_hours = 24
        self.max_login_attempts = 5
        self.lockout_duration_minutes = 30
        self.password_min_length = 8
        self.require_mfa = False
        
        # Data storage
        self.users: Dict[str, User] = {}
        self.roles: Dict[str, Role] = {}
        self.sessions: Dict[str, SessionInfo] = {}
        self.login_attempts: List[LoginAttempt] = []
        self.security_events: List[SecurityEvent] = []
        
        # Encryption for sensitive data
        self.fernet = Fernet(base64.urlsafe_b64encode(self.secret_key[:32].encode().ljust(32)[:32]))
        
        # Initialize default roles and admin user
        self._initialize_default_roles()
        self._load_users()
        self._create_admin_user()
        
        self.logger.info("Authentication manager initialized")
    
    def _generate_secret_key(self) -> str:
        """Generate a secure secret key."""
        return secrets.token_urlsafe(32)
    
    def _initialize_default_roles(self):
        """Initialize default system roles."""
        try:
            # Admin role - full permissions
            admin_permissions = set(Permission)
            self.roles['admin'] = Role(
                name='admin',
                description='System administrator with full permissions',
                permissions=admin_permissions
            )
            
            # Trader role - trading and portfolio permissions
            trader_permissions = {
                Permission.TRADE_VIEW, Permission.TRADE_EXECUTE, Permission.TRADE_CANCEL,
                Permission.TRADE_MODIFY, Permission.PORTFOLIO_VIEW, Permission.PORTFOLIO_MODIFY,
                Permission.STRATEGY_VIEW, Permission.STRATEGY_EXECUTE, Permission.DATA_VIEW,
                Permission.CONFIG_VIEW, Permission.API_ACCESS
            }
            self.roles['trader'] = Role(
                name='trader',
                description='Trader with trading and portfolio management permissions',
                permissions=trader_permissions
            )
            
            # Analyst role - view and analysis permissions
            analyst_permissions = {
                Permission.TRADE_VIEW, Permission.PORTFOLIO_VIEW, Permission.STRATEGY_VIEW,
                Permission.DATA_VIEW, Permission.DATA_EXPORT, Permission.CONFIG_VIEW,
                Permission.API_ACCESS, Permission.AUDIT_VIEW
            }
            self.roles['analyst'] = Role(
                name='analyst',
                description='Analyst with view and data analysis permissions',
                permissions=analyst_permissions
            )
            
            # Viewer role - read-only permissions
            viewer_permissions = {
                Permission.TRADE_VIEW, Permission.PORTFOLIO_VIEW, Permission.STRATEGY_VIEW,
                Permission.DATA_VIEW, Permission.CONFIG_VIEW
            }
            self.roles['viewer'] = Role(
                name='viewer',
                description='Read-only access to system data',
                permissions=viewer_permissions
            )
            
            # Strategy Developer role
            strategy_dev_permissions = {
                Permission.STRATEGY_VIEW, Permission.STRATEGY_CREATE, Permission.STRATEGY_MODIFY,
                Permission.STRATEGY_DELETE, Permission.DATA_VIEW, Permission.DATA_EXPORT,
                Permission.CONFIG_VIEW, Permission.API_ACCESS
            }
            self.roles['strategy_developer'] = Role(
                name='strategy_developer',
                description='Strategy development and testing permissions',
                permissions=strategy_dev_permissions
            )
            
        except Exception as e:
            self.logger.error(f"Error initializing default roles: {e}")
    
    def _load_users(self):
        """Load users from storage."""
        try:
            users_file = self.config_dir / "users.json"
            if users_file.exists():
                with open(users_file, 'r', encoding='utf-8') as f:
                    users_data = json.load(f)
                
                for username, user_data in users_data.items():
                    self.users[username] = User(
                        username=user_data['username'],
                        email=user_data['email'],
                        password_hash=user_data['password_hash'],
                        roles=set(user_data['roles']),
                        is_active=user_data.get('is_active', True),
                        is_verified=user_data.get('is_verified', False),
                        mfa_enabled=user_data.get('mfa_enabled', False),
                        mfa_secret=user_data.get('mfa_secret'),
                        api_keys=user_data.get('api_keys', []),
                        last_login=datetime.fromisoformat(user_data['last_login']) if user_data.get('last_login') else None,
                        login_attempts=user_data.get('login_attempts', 0),
                        locked_until=datetime.fromisoformat(user_data['locked_until']) if user_data.get('locked_until') else None,
                        created_at=datetime.fromisoformat(user_data['created_at']),
                        updated_at=datetime.fromisoformat(user_data['updated_at']),
                        metadata=user_data.get('metadata', {})
                    )
        except Exception as e:
            self.logger.error(f"Error loading users: {e}")
    
    def _create_admin_user(self):
        """Create default admin user if none exists."""
        try:
            # Check if admin user already exists
            admin_users = [user for user in self.users.values() if 'admin' in user.roles]
            
            if not admin_users:
                # Create default admin user
                admin_password = os.getenv('ADMIN_PASSWORD', 'admin123')
                admin_email = os.getenv('ADMIN_EMAIL', '<EMAIL>')
                
                self.create_user(
                    username='admin',
                    email=admin_email,
                    password=admin_password,
                    roles={'admin'},
                    is_verified=True
                )
                
                self.logger.warning("Default admin user created. Please change the password!")
        except Exception as e:
            self.logger.error(f"Error creating admin user: {e}")
    
    def _hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
    
    def _verify_password(self, password: str, password_hash: str) -> bool:
        """Verify a password against its hash."""
        try:
            return bcrypt.checkpw(password.encode('utf-8'), password_hash.encode('utf-8'))
        except Exception:
            return False
    
    def _validate_password(self, password: str) -> List[str]:
        """Validate password strength."""
        errors = []
        
        if len(password) < self.password_min_length:
            errors.append(f"Password must be at least {self.password_min_length} characters long")
        
        if not any(c.isupper() for c in password):
            errors.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            errors.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            errors.append("Password must contain at least one digit")
        
        if not any(c in '!@#$%^&*()_+-=[]{}|;:,.<>?' for c in password):
            errors.append("Password must contain at least one special character")
        
        return errors
    
    def _generate_session_id(self) -> str:
        """Generate a unique session ID."""
        return secrets.token_urlsafe(32)
    
    def _log_security_event(self, event_type: str, description: str, 
                           user_id: Optional[str] = None, username: Optional[str] = None,
                           severity: str = "INFO", ip_address: Optional[str] = None,
                           metadata: Optional[Dict[str, Any]] = None):
        """Log a security event."""
        try:
            event = SecurityEvent(
                event_type=event_type,
                user_id=user_id,
                username=username,
                description=description,
                severity=severity,
                ip_address=ip_address,
                metadata=metadata or {}
            )
            
            self.security_events.append(event)
            
            # Log to system logger
            log_level = getattr(logging, severity.upper(), logging.INFO)
            self.logger.log(log_level, f"Security Event [{event_type}]: {description}")
            
        except Exception as e:
            self.logger.error(f"Error logging security event: {e}")
    
    def create_user(self, username: str, email: str, password: str, 
                   roles: Set[str], is_verified: bool = False) -> bool:
        """Create a new user."""
        try:
            # Validate input
            if username in self.users:
                self.logger.error(f"User '{username}' already exists")
                return False
            
            # Validate password
            password_errors = self._validate_password(password)
            if password_errors:
                self.logger.error(f"Password validation failed: {', '.join(password_errors)}")
                return False
            
            # Validate roles
            invalid_roles = roles - set(self.roles.keys())
            if invalid_roles:
                self.logger.error(f"Invalid roles: {invalid_roles}")
                return False
            
            # Create user
            user = User(
                username=username,
                email=email,
                password_hash=self._hash_password(password),
                roles=roles,
                is_verified=is_verified
            )
            
            self.users[username] = user
            
            # Log security event
            self._log_security_event(
                event_type="USER_CREATED",
                description=f"User '{username}' created with roles: {', '.join(roles)}",
                username=username,
                severity="INFO"
            )
            
            self.logger.info(f"User '{username}' created successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Error creating user: {e}")
            return False
    
    def authenticate_user(self, username: str, password: str, 
                         ip_address: str = "unknown", user_agent: str = "unknown",
                         mfa_token: Optional[str] = None) -> Optional[SessionInfo]:
        """Authenticate a user and create a session."""
        try:
            # Record login attempt
            attempt = LoginAttempt(
                username=username,
                ip_address=ip_address,
                user_agent=user_agent,
                success=False,
                method=AuthenticationMethod.PASSWORD
            )
            
            # Check if user exists
            user = self.users.get(username)
            if not user:
                attempt.failure_reason = "User not found"
                self.login_attempts.append(attempt)
                self._log_security_event(
                    event_type="LOGIN_FAILED",
                    description=f"Login attempt for non-existent user '{username}'",
                    username=username,
                    severity="WARNING",
                    ip_address=ip_address
                )
                return None
            
            # Check if user is active
            if not user.is_active:
                attempt.failure_reason = "User account disabled"
                self.login_attempts.append(attempt)
                self._log_security_event(
                    event_type="LOGIN_FAILED",
                    description=f"Login attempt for disabled user '{username}'",
                    username=username,
                    severity="WARNING",
                    ip_address=ip_address
                )
                return None
            
            # Check if user is locked
            if user.is_locked():
                attempt.failure_reason = "User account locked"
                self.login_attempts.append(attempt)
                self._log_security_event(
                    event_type="LOGIN_FAILED",
                    description=f"Login attempt for locked user '{username}'",
                    username=username,
                    severity="WARNING",
                    ip_address=ip_address
                )
                return None
            
            # Verify password
            if not self._verify_password(password, user.password_hash):
                user.login_attempts += 1
                
                # Lock account if too many failed attempts
                if user.login_attempts >= self.max_login_attempts:
                    user.lock(self.lockout_duration_minutes)
                    self._log_security_event(
                        event_type="ACCOUNT_LOCKED",
                        description=f"User '{username}' locked due to too many failed login attempts",
                        username=username,
                        severity="WARNING",
                        ip_address=ip_address
                    )
                
                attempt.failure_reason = "Invalid password"
                self.login_attempts.append(attempt)
                self._log_security_event(
                    event_type="LOGIN_FAILED",
                    description=f"Invalid password for user '{username}'",
                    username=username,
                    severity="WARNING",
                    ip_address=ip_address
                )
                return None
            
            # Check MFA if enabled
            if user.mfa_enabled:
                if not mfa_token:
                    attempt.failure_reason = "MFA token required"
                    self.login_attempts.append(attempt)
                    return None
                
                if not self._verify_mfa_token(user, mfa_token):
                    attempt.failure_reason = "Invalid MFA token"
                    self.login_attempts.append(attempt)
                    self._log_security_event(
                        event_type="MFA_FAILED",
                        description=f"Invalid MFA token for user '{username}'",
                        username=username,
                        severity="WARNING",
                        ip_address=ip_address
                    )
                    return None
            
            # Authentication successful
            user.login_attempts = 0
            user.last_login = datetime.utcnow()
            user.updated_at = datetime.utcnow()
            
            # Get user permissions
            permissions = self._get_user_permissions(user)
            
            # Create session
            session = SessionInfo(
                session_id=self._generate_session_id(),
                user_id=username,
                username=username,
                roles=user.roles,
                permissions=permissions,
                created_at=datetime.utcnow(),
                expires_at=datetime.utcnow() + timedelta(hours=self.session_duration_hours),
                last_activity=datetime.utcnow(),
                ip_address=ip_address,
                user_agent=user_agent
            )
            
            self.sessions[session.session_id] = session
            
            # Record successful login
            attempt.success = True
            self.login_attempts.append(attempt)
            
            self._log_security_event(
                event_type="LOGIN_SUCCESS",
                description=f"User '{username}' logged in successfully",
                user_id=username,
                username=username,
                severity="INFO",
                ip_address=ip_address
            )
            
            return session
            
        except Exception as e:
            self.logger.error(f"Error authenticating user: {e}")
            return None
    
    def _get_user_permissions(self, user: User) -> Set[Permission]:
        """Get all permissions for a user based on their roles."""
        permissions = set()
        
        for role_name in user.roles:
            role = self.roles.get(role_name)
            if role and role.is_active:
                permissions.update(role.permissions)
        
        return permissions
    
    def _verify_mfa_token(self, user: User, token: str) -> bool:
        """Verify MFA token."""
        try:
            if not user.mfa_secret:
                return False
            
            # Decrypt MFA secret
            decrypted_secret = self.fernet.decrypt(user.mfa_secret.encode()).decode()
            
            # Verify TOTP token
            totp = pyotp.TOTP(decrypted_secret)
            return totp.verify(token, valid_window=1)
            
        except Exception as e:
            self.logger.error(f"Error verifying MFA token: {e}")
            return False
    
    def enable_mfa(self, username: str) -> Optional[Tuple[str, str]]:
        """Enable MFA for a user and return secret and QR code."""
        try:
            user = self.users.get(username)
            if not user:
                return None
            
            # Generate MFA secret
            secret = pyotp.random_base32()
            
            # Encrypt and store secret
            encrypted_secret = self.fernet.encrypt(secret.encode()).decode()
            user.mfa_secret = encrypted_secret
            user.mfa_enabled = True
            user.updated_at = datetime.utcnow()
            
            # Generate QR code
            totp = pyotp.TOTP(secret)
            provisioning_uri = totp.provisioning_uri(
                name=user.email,
                issuer_name="AI Trading Agent"
            )
            
            # Create QR code
            qr = qrcode.QRCode(version=1, box_size=10, border=5)
            qr.add_data(provisioning_uri)
            qr.make(fit=True)
            
            img = qr.make_image(fill_color="black", back_color="white")
            
            # Convert to base64
            buffer = BytesIO()
            img.save(buffer, format='PNG')
            qr_code_base64 = base64.b64encode(buffer.getvalue()).decode()
            
            self._log_security_event(
                event_type="MFA_ENABLED",
                description=f"MFA enabled for user '{username}'",
                user_id=username,
                username=username,
                severity="INFO"
            )
            
            return secret, qr_code_base64
            
        except Exception as e:
            self.logger.error(f"Error enabling MFA: {e}")
            return None
    
    def disable_mfa(self, username: str) -> bool:
        """Disable MFA for a user."""
        try:
            user = self.users.get(username)
            if not user:
                return False
            
            user.mfa_enabled = False
            user.mfa_secret = None
            user.updated_at = datetime.utcnow()
            
            self._log_security_event(
                event_type="MFA_DISABLED",
                description=f"MFA disabled for user '{username}'",
                user_id=username,
                username=username,
                severity="INFO"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error disabling MFA: {e}")
            return False
    
    def validate_session(self, session_id: str) -> Optional[SessionInfo]:
        """Validate and return session information."""
        try:
            session = self.sessions.get(session_id)
            
            if not session:
                return None
            
            # Check if session is expired
            if session.is_expired():
                self.logout(session_id)
                return None
            
            # Check if user is still active
            user = self.users.get(session.username)
            if not user or not user.is_active:
                self.logout(session_id)
                return None
            
            # Update last activity
            session.last_activity = datetime.utcnow()
            
            return session
            
        except Exception as e:
            self.logger.error(f"Error validating session: {e}")
            return None
    
    def logout(self, session_id: str) -> bool:
        """Logout a user session."""
        try:
            session = self.sessions.get(session_id)
            
            if session:
                session.is_active = False
                del self.sessions[session_id]
                
                self._log_security_event(
                    event_type="LOGOUT",
                    description=f"User '{session.username}' logged out",
                    user_id=session.user_id,
                    username=session.username,
                    severity="INFO",
                    ip_address=session.ip_address
                )
                
                return True
            
            return False
            
        except Exception as e:
            self.logger.error(f"Error logging out: {e}")
            return False
    
    def change_password(self, username: str, old_password: str, new_password: str) -> bool:
        """Change user password."""
        try:
            user = self.users.get(username)
            if not user:
                return False
            
            # Verify old password
            if not self._verify_password(old_password, user.password_hash):
                self._log_security_event(
                    event_type="PASSWORD_CHANGE_FAILED",
                    description=f"Invalid old password for user '{username}'",
                    user_id=username,
                    username=username,
                    severity="WARNING"
                )
                return False
            
            # Validate new password
            password_errors = self._validate_password(new_password)
            if password_errors:
                self.logger.error(f"New password validation failed: {', '.join(password_errors)}")
                return False
            
            # Update password
            user.password_hash = self._hash_password(new_password)
            user.updated_at = datetime.utcnow()
            
            self._log_security_event(
                event_type="PASSWORD_CHANGED",
                description=f"Password changed for user '{username}'",
                user_id=username,
                username=username,
                severity="INFO"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error changing password: {e}")
            return False
    
    def has_permission(self, session_id: str, permission: Permission) -> bool:
        """Check if a session has a specific permission."""
        try:
            session = self.validate_session(session_id)
            if not session:
                return False
            
            return session.has_permission(permission)
            
        except Exception as e:
            self.logger.error(f"Error checking permission: {e}")
            return False
    
    def get_user_info(self, username: str) -> Optional[Dict[str, Any]]:
        """Get user information (excluding sensitive data)."""
        try:
            user = self.users.get(username)
            if not user:
                return None
            
            return {
                'username': user.username,
                'email': user.email,
                'roles': list(user.roles),
                'is_active': user.is_active,
                'is_verified': user.is_verified,
                'mfa_enabled': user.mfa_enabled,
                'last_login': user.last_login.isoformat() if user.last_login else None,
                'created_at': user.created_at.isoformat(),
                'updated_at': user.updated_at.isoformat(),
                'metadata': user.metadata
            }
            
        except Exception as e:
            self.logger.error(f"Error getting user info: {e}")
            return None
    
    def list_users(self) -> List[Dict[str, Any]]:
        """List all users (excluding sensitive data)."""
        try:
            users_info = []
            
            for user in self.users.values():
                user_info = self.get_user_info(user.username)
                if user_info:
                    users_info.append(user_info)
            
            return users_info
            
        except Exception as e:
            self.logger.error(f"Error listing users: {e}")
            return []
    
    def update_user(self, username: str, **kwargs) -> bool:
        """Update user information."""
        try:
            user = self.users.get(username)
            if not user:
                return False
            
            # Update allowed fields
            allowed_fields = ['email', 'roles', 'is_active', 'is_verified', 'metadata']
            
            for field, value in kwargs.items():
                if field in allowed_fields:
                    if field == 'roles':
                        # Validate roles
                        if isinstance(value, (list, set)):
                            invalid_roles = set(value) - set(self.roles.keys())
                            if invalid_roles:
                                self.logger.error(f"Invalid roles: {invalid_roles}")
                                continue
                            user.roles = set(value)
                    else:
                        setattr(user, field, value)
            
            user.updated_at = datetime.utcnow()
            
            self._log_security_event(
                event_type="USER_UPDATED",
                description=f"User '{username}' updated",
                user_id=username,
                username=username,
                severity="INFO"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error updating user: {e}")
            return False
    
    def delete_user(self, username: str) -> bool:
        """Delete a user."""
        try:
            if username not in self.users:
                return False
            
            # Don't allow deleting the last admin
            user = self.users[username]
            if 'admin' in user.roles:
                admin_count = sum(1 for u in self.users.values() if 'admin' in u.roles)
                if admin_count <= 1:
                    self.logger.error("Cannot delete the last admin user")
                    return False
            
            # Invalidate all user sessions
            sessions_to_remove = [sid for sid, session in self.sessions.items() 
                                if session.username == username]
            for session_id in sessions_to_remove:
                self.logout(session_id)
            
            # Delete user
            del self.users[username]
            
            self._log_security_event(
                event_type="USER_DELETED",
                description=f"User '{username}' deleted",
                username=username,
                severity="WARNING"
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting user: {e}")
            return False
    
    def save_users(self) -> bool:
        """Save users to storage."""
        try:
            self.config_dir.mkdir(parents=True, exist_ok=True)
            
            users_data = {}
            for username, user in self.users.items():
                users_data[username] = {
                    'username': user.username,
                    'email': user.email,
                    'password_hash': user.password_hash,
                    'roles': list(user.roles),
                    'is_active': user.is_active,
                    'is_verified': user.is_verified,
                    'mfa_enabled': user.mfa_enabled,
                    'mfa_secret': user.mfa_secret,
                    'api_keys': user.api_keys,
                    'last_login': user.last_login.isoformat() if user.last_login else None,
                    'login_attempts': user.login_attempts,
                    'locked_until': user.locked_until.isoformat() if user.locked_until else None,
                    'created_at': user.created_at.isoformat(),
                    'updated_at': user.updated_at.isoformat(),
                    'metadata': user.metadata
                }
            
            users_file = self.config_dir / "users.json"
            with open(users_file, 'w', encoding='utf-8') as f:
                json.dump(users_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving users: {e}")
            return False
    
    def cleanup_sessions(self):
        """Clean up expired sessions."""
        try:
            expired_sessions = [sid for sid, session in self.sessions.items() 
                              if session.is_expired()]
            
            for session_id in expired_sessions:
                self.logout(session_id)
            
            if expired_sessions:
                self.logger.info(f"Cleaned up {len(expired_sessions)} expired sessions")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up sessions: {e}")
    
    def get_security_events(self, limit: int = 100) -> List[SecurityEvent]:
        """Get recent security events."""
        try:
            return sorted(self.security_events, key=lambda x: x.timestamp, reverse=True)[:limit]
        except Exception as e:
            self.logger.error(f"Error getting security events: {e}")
            return []
    
    def get_login_attempts(self, username: Optional[str] = None, limit: int = 100) -> List[LoginAttempt]:
        """Get recent login attempts."""
        try:
            attempts = self.login_attempts
            
            if username:
                attempts = [attempt for attempt in attempts if attempt.username == username]
            
            return sorted(attempts, key=lambda x: x.timestamp, reverse=True)[:limit]
            
        except Exception as e:
            self.logger.error(f"Error getting login attempts: {e}")
            return []
    
    def get_active_sessions(self) -> List[SessionInfo]:
        """Get all active sessions."""
        try:
            return [session for session in self.sessions.values() 
                   if session.is_active and not session.is_expired()]
        except Exception as e:
            self.logger.error(f"Error getting active sessions: {e}")
            return []
    
    def get_auth_stats(self) -> Dict[str, Any]:
        """Get authentication statistics."""
        try:
            total_users = len(self.users)
            active_users = sum(1 for user in self.users.values() if user.is_active)
            verified_users = sum(1 for user in self.users.values() if user.is_verified)
            mfa_users = sum(1 for user in self.users.values() if user.mfa_enabled)
            locked_users = sum(1 for user in self.users.values() if user.is_locked())
            
            active_sessions = len(self.get_active_sessions())
            
            recent_logins = len([attempt for attempt in self.login_attempts 
                               if attempt.success and 
                               attempt.timestamp > datetime.utcnow() - timedelta(hours=24)])
            
            failed_logins = len([attempt for attempt in self.login_attempts 
                               if not attempt.success and 
                               attempt.timestamp > datetime.utcnow() - timedelta(hours=24)])
            
            return {
                'total_users': total_users,
                'active_users': active_users,
                'verified_users': verified_users,
                'mfa_enabled_users': mfa_users,
                'locked_users': locked_users,
                'active_sessions': active_sessions,
                'recent_successful_logins_24h': recent_logins,
                'recent_failed_logins_24h': failed_logins,
                'total_login_attempts': len(self.login_attempts),
                'total_security_events': len(self.security_events)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting auth stats: {e}")
            return {}