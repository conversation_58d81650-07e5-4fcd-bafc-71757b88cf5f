"""Encryption and Cryptographic Manager for AI Trading Agent.

This module provides comprehensive encryption, decryption, key management,
and cryptographic operations including symmetric/asymmetric encryption,
digital signatures, and certificate management.

Author: inkbytefo
"""

import os
import secrets
import hashlib
import base64
import json
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from pathlib import Path

# Cryptographic libraries
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes, serialization
from cryptography.hazmat.primitives.asymmetric import rsa, padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives.kdf.scrypt import Scrypt
from cryptography.hazmat.backends import default_backend
from cryptography import x509
from cryptography.x509.oid import NameOID
import cryptography.hazmat.primitives.asymmetric.ec as ec


class EncryptionMethod(Enum):
    """Encryption methods."""
    FERNET = "fernet"  # Symmetric encryption (AES 128)
    AES_256_GCM = "aes_256_gcm"  # AES 256 GCM
    AES_256_CBC = "aes_256_cbc"  # AES 256 CBC
    RSA_2048 = "rsa_2048"  # RSA 2048-bit
    RSA_4096 = "rsa_4096"  # RSA 4096-bit
    ECC_P256 = "ecc_p256"  # Elliptic Curve P-256
    ECC_P384 = "ecc_p384"  # Elliptic Curve P-384
    CHACHA20_POLY1305 = "chacha20_poly1305"  # ChaCha20-Poly1305


class KeyType(Enum):
    """Cryptographic key types."""
    SYMMETRIC = "symmetric"
    ASYMMETRIC_PRIVATE = "asymmetric_private"
    ASYMMETRIC_PUBLIC = "asymmetric_public"
    DERIVED = "derived"
    MASTER = "master"


class HashAlgorithm(Enum):
    """Hash algorithms."""
    SHA256 = "sha256"
    SHA384 = "sha384"
    SHA512 = "sha512"
    BLAKE2B = "blake2b"
    BLAKE2S = "blake2s"


@dataclass
class KeyInfo:
    """Cryptographic key information."""
    key_id: str
    key_type: KeyType
    algorithm: EncryptionMethod
    created_at: datetime
    expires_at: Optional[datetime] = None
    description: str = ""
    metadata: Dict[str, Any] = field(default_factory=dict)
    is_active: bool = True
    usage_count: int = 0
    max_usage: Optional[int] = None
    
    def is_expired(self) -> bool:
        """Check if key is expired."""
        return (self.expires_at is not None and 
                datetime.utcnow() > self.expires_at)
    
    def is_usage_exceeded(self) -> bool:
        """Check if key usage limit is exceeded."""
        return (self.max_usage is not None and 
                self.usage_count >= self.max_usage)
    
    def can_use(self) -> bool:
        """Check if key can be used."""
        return (self.is_active and 
                not self.is_expired() and 
                not self.is_usage_exceeded())


@dataclass
class EncryptedData:
    """Encrypted data container."""
    data: bytes
    algorithm: EncryptionMethod
    key_id: str
    iv: Optional[bytes] = None
    tag: Optional[bytes] = None
    salt: Optional[bytes] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    created_at: datetime = field(default_factory=datetime.utcnow)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'data': base64.b64encode(self.data).decode('utf-8'),
            'algorithm': self.algorithm.value,
            'key_id': self.key_id,
            'iv': base64.b64encode(self.iv).decode('utf-8') if self.iv else None,
            'tag': base64.b64encode(self.tag).decode('utf-8') if self.tag else None,
            'salt': base64.b64encode(self.salt).decode('utf-8') if self.salt else None,
            'metadata': self.metadata,
            'created_at': self.created_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EncryptedData':
        """Create from dictionary."""
        return cls(
            data=base64.b64decode(data['data']),
            algorithm=EncryptionMethod(data['algorithm']),
            key_id=data['key_id'],
            iv=base64.b64decode(data['iv']) if data.get('iv') else None,
            tag=base64.b64decode(data['tag']) if data.get('tag') else None,
            salt=base64.b64decode(data['salt']) if data.get('salt') else None,
            metadata=data.get('metadata', {}),
            created_at=datetime.fromisoformat(data['created_at'])
        )


@dataclass
class CertificateInfo:
    """X.509 certificate information."""
    cert_id: str
    subject: str
    issuer: str
    serial_number: str
    not_before: datetime
    not_after: datetime
    public_key_algorithm: str
    signature_algorithm: str
    fingerprint_sha256: str
    is_ca: bool = False
    key_usage: List[str] = field(default_factory=list)
    extended_key_usage: List[str] = field(default_factory=list)
    san_dns: List[str] = field(default_factory=list)
    san_ip: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def is_expired(self) -> bool:
        """Check if certificate is expired."""
        return datetime.utcnow() > self.not_after
    
    def is_valid(self) -> bool:
        """Check if certificate is currently valid."""
        now = datetime.utcnow()
        return self.not_before <= now <= self.not_after


class EncryptionManager:
    """Encryption and cryptographic operations manager."""
    
    def __init__(self, config_dir: str = "config", master_key: Optional[str] = None):
        """Initialize encryption manager."""
        self.config_dir = Path(config_dir)
        self.keys_dir = self.config_dir / "keys"
        self.certs_dir = self.config_dir / "certificates"
        self.logger = logging.getLogger(__name__)
        
        # Create directories
        self.keys_dir.mkdir(parents=True, exist_ok=True)
        self.certs_dir.mkdir(parents=True, exist_ok=True)
        
        # Master key for key encryption
        self.master_key = master_key or os.getenv('ENCRYPTION_MASTER_KEY') or self._generate_master_key()
        self.master_fernet = Fernet(base64.urlsafe_b64encode(self.master_key[:32].encode().ljust(32)[:32]))
        
        # Key storage
        self.keys: Dict[str, KeyInfo] = {}
        self.key_data: Dict[str, bytes] = {}  # Encrypted key data
        self.certificates: Dict[str, CertificateInfo] = {}
        self.certificate_data: Dict[str, bytes] = {}  # Certificate data
        
        # Load existing keys and certificates
        self._load_keys()
        self._load_certificates()
        
        # Create default encryption key if none exists
        if not self.keys:
            self._create_default_key()
        
        self.logger.info("Encryption manager initialized")
    
    def _generate_master_key(self) -> str:
        """Generate a master key for key encryption."""
        return secrets.token_urlsafe(32)
    
    def _create_default_key(self):
        """Create default encryption key."""
        try:
            self.generate_key(
                key_id="default",
                algorithm=EncryptionMethod.FERNET,
                description="Default encryption key"
            )
        except Exception as e:
            self.logger.error(f"Error creating default key: {e}")
    
    def _load_keys(self):
        """Load keys from storage."""
        try:
            keys_file = self.keys_dir / "keys.json"
            if keys_file.exists():
                with open(keys_file, 'r', encoding='utf-8') as f:
                    keys_data = json.load(f)
                
                for key_id, key_info in keys_data.items():
                    self.keys[key_id] = KeyInfo(
                        key_id=key_info['key_id'],
                        key_type=KeyType(key_info['key_type']),
                        algorithm=EncryptionMethod(key_info['algorithm']),
                        created_at=datetime.fromisoformat(key_info['created_at']),
                        expires_at=datetime.fromisoformat(key_info['expires_at']) if key_info.get('expires_at') else None,
                        description=key_info.get('description', ''),
                        metadata=key_info.get('metadata', {}),
                        is_active=key_info.get('is_active', True),
                        usage_count=key_info.get('usage_count', 0),
                        max_usage=key_info.get('max_usage')
                    )
                    
                    # Load encrypted key data
                    key_file = self.keys_dir / f"{key_id}.key"
                    if key_file.exists():
                        with open(key_file, 'rb') as f:
                            self.key_data[key_id] = f.read()
        
        except Exception as e:
            self.logger.error(f"Error loading keys: {e}")
    
    def _load_certificates(self):
        """Load certificates from storage."""
        try:
            certs_file = self.certs_dir / "certificates.json"
            if certs_file.exists():
                with open(certs_file, 'r', encoding='utf-8') as f:
                    certs_data = json.load(f)
                
                for cert_id, cert_info in certs_data.items():
                    self.certificates[cert_id] = CertificateInfo(
                        cert_id=cert_info['cert_id'],
                        subject=cert_info['subject'],
                        issuer=cert_info['issuer'],
                        serial_number=cert_info['serial_number'],
                        not_before=datetime.fromisoformat(cert_info['not_before']),
                        not_after=datetime.fromisoformat(cert_info['not_after']),
                        public_key_algorithm=cert_info['public_key_algorithm'],
                        signature_algorithm=cert_info['signature_algorithm'],
                        fingerprint_sha256=cert_info['fingerprint_sha256'],
                        is_ca=cert_info.get('is_ca', False),
                        key_usage=cert_info.get('key_usage', []),
                        extended_key_usage=cert_info.get('extended_key_usage', []),
                        san_dns=cert_info.get('san_dns', []),
                        san_ip=cert_info.get('san_ip', []),
                        metadata=cert_info.get('metadata', {})
                    )
                    
                    # Load certificate data
                    cert_file = self.certs_dir / f"{cert_id}.pem"
                    if cert_file.exists():
                        with open(cert_file, 'rb') as f:
                            self.certificate_data[cert_id] = f.read()
        
        except Exception as e:
            self.logger.error(f"Error loading certificates: {e}")
    
    def _get_key_data(self, key_id: str) -> Optional[bytes]:
        """Get decrypted key data."""
        try:
            if key_id not in self.key_data:
                return None
            
            # Decrypt key data using master key
            encrypted_data = self.key_data[key_id]
            return self.master_fernet.decrypt(encrypted_data)
            
        except Exception as e:
            self.logger.error(f"Error getting key data: {e}")
            return None
    
    def _store_key_data(self, key_id: str, key_data: bytes):
        """Store encrypted key data."""
        try:
            # Encrypt key data using master key
            encrypted_data = self.master_fernet.encrypt(key_data)
            self.key_data[key_id] = encrypted_data
            
            # Save to file
            key_file = self.keys_dir / f"{key_id}.key"
            with open(key_file, 'wb') as f:
                f.write(encrypted_data)
                
        except Exception as e:
            self.logger.error(f"Error storing key data: {e}")
    
    def generate_key(self, key_id: str, algorithm: EncryptionMethod,
                    description: str = "", expires_in_days: Optional[int] = None,
                    max_usage: Optional[int] = None) -> bool:
        """Generate a new cryptographic key."""
        try:
            if key_id in self.keys:
                self.logger.error(f"Key '{key_id}' already exists")
                return False
            
            # Generate key based on algorithm
            if algorithm == EncryptionMethod.FERNET:
                key_data = Fernet.generate_key()
                key_type = KeyType.SYMMETRIC
            
            elif algorithm == EncryptionMethod.AES_256_GCM:
                key_data = secrets.token_bytes(32)  # 256 bits
                key_type = KeyType.SYMMETRIC
            
            elif algorithm == EncryptionMethod.AES_256_CBC:
                key_data = secrets.token_bytes(32)  # 256 bits
                key_type = KeyType.SYMMETRIC
            
            elif algorithm == EncryptionMethod.RSA_2048:
                private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=2048,
                    backend=default_backend()
                )
                key_data = private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                key_type = KeyType.ASYMMETRIC_PRIVATE
            
            elif algorithm == EncryptionMethod.RSA_4096:
                private_key = rsa.generate_private_key(
                    public_exponent=65537,
                    key_size=4096,
                    backend=default_backend()
                )
                key_data = private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                key_type = KeyType.ASYMMETRIC_PRIVATE
            
            elif algorithm == EncryptionMethod.ECC_P256:
                private_key = ec.generate_private_key(ec.SECP256R1(), default_backend())
                key_data = private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                key_type = KeyType.ASYMMETRIC_PRIVATE
            
            elif algorithm == EncryptionMethod.ECC_P384:
                private_key = ec.generate_private_key(ec.SECP384R1(), default_backend())
                key_data = private_key.private_bytes(
                    encoding=serialization.Encoding.PEM,
                    format=serialization.PrivateFormat.PKCS8,
                    encryption_algorithm=serialization.NoEncryption()
                )
                key_type = KeyType.ASYMMETRIC_PRIVATE
            
            elif algorithm == EncryptionMethod.CHACHA20_POLY1305:
                key_data = secrets.token_bytes(32)  # 256 bits
                key_type = KeyType.SYMMETRIC
            
            else:
                self.logger.error(f"Unsupported algorithm: {algorithm}")
                return False
            
            # Create key info
            expires_at = None
            if expires_in_days:
                expires_at = datetime.utcnow() + timedelta(days=expires_in_days)
            
            key_info = KeyInfo(
                key_id=key_id,
                key_type=key_type,
                algorithm=algorithm,
                created_at=datetime.utcnow(),
                expires_at=expires_at,
                description=description,
                max_usage=max_usage
            )
            
            # Store key
            self.keys[key_id] = key_info
            self._store_key_data(key_id, key_data)
            
            self.logger.info(f"Generated {algorithm.value} key '{key_id}'")
            return True
            
        except Exception as e:
            self.logger.error(f"Error generating key: {e}")
            return False
    
    def derive_key(self, key_id: str, password: str, salt: Optional[bytes] = None,
                  algorithm: EncryptionMethod = EncryptionMethod.AES_256_GCM,
                  iterations: int = 100000) -> bool:
        """Derive a key from a password."""
        try:
            if key_id in self.keys:
                self.logger.error(f"Key '{key_id}' already exists")
                return False
            
            # Generate salt if not provided
            if salt is None:
                salt = secrets.token_bytes(16)
            
            # Derive key using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,  # 256 bits
                salt=salt,
                iterations=iterations,
                backend=default_backend()
            )
            
            key_data = kdf.derive(password.encode('utf-8'))
            
            # Create key info
            key_info = KeyInfo(
                key_id=key_id,
                key_type=KeyType.DERIVED,
                algorithm=algorithm,
                created_at=datetime.utcnow(),
                description=f"Key derived from password using PBKDF2",
                metadata={'salt': base64.b64encode(salt).decode('utf-8'), 'iterations': iterations}
            )
            
            # Store key
            self.keys[key_id] = key_info
            self._store_key_data(key_id, key_data)
            
            self.logger.info(f"Derived key '{key_id}' from password")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deriving key: {e}")
            return False
    
    def encrypt(self, data: Union[str, bytes], key_id: str = "default") -> Optional[EncryptedData]:
        """Encrypt data using specified key."""
        try:
            # Get key info
            key_info = self.keys.get(key_id)
            if not key_info:
                self.logger.error(f"Key '{key_id}' not found")
                return None
            
            if not key_info.can_use():
                self.logger.error(f"Key '{key_id}' cannot be used")
                return None
            
            # Convert string to bytes
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Get key data
            key_data = self._get_key_data(key_id)
            if not key_data:
                return None
            
            # Encrypt based on algorithm
            if key_info.algorithm == EncryptionMethod.FERNET:
                fernet = Fernet(key_data)
                encrypted_data = fernet.encrypt(data)
                
                result = EncryptedData(
                    data=encrypted_data,
                    algorithm=key_info.algorithm,
                    key_id=key_id
                )
            
            elif key_info.algorithm == EncryptionMethod.AES_256_GCM:
                # Generate random IV
                iv = secrets.token_bytes(12)  # 96 bits for GCM
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key_data),
                    modes.GCM(iv),
                    backend=default_backend()
                )
                
                encryptor = cipher.encryptor()
                ciphertext = encryptor.update(data) + encryptor.finalize()
                
                result = EncryptedData(
                    data=ciphertext,
                    algorithm=key_info.algorithm,
                    key_id=key_id,
                    iv=iv,
                    tag=encryptor.tag
                )
            
            elif key_info.algorithm == EncryptionMethod.AES_256_CBC:
                # Generate random IV
                iv = secrets.token_bytes(16)  # 128 bits for CBC
                
                # Pad data to block size
                block_size = 16
                padding_length = block_size - (len(data) % block_size)
                padded_data = data + bytes([padding_length] * padding_length)
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key_data),
                    modes.CBC(iv),
                    backend=default_backend()
                )
                
                encryptor = cipher.encryptor()
                ciphertext = encryptor.update(padded_data) + encryptor.finalize()
                
                result = EncryptedData(
                    data=ciphertext,
                    algorithm=key_info.algorithm,
                    key_id=key_id,
                    iv=iv
                )
            
            elif key_info.algorithm in [EncryptionMethod.RSA_2048, EncryptionMethod.RSA_4096]:
                # Load private key
                private_key = serialization.load_pem_private_key(
                    key_data,
                    password=None,
                    backend=default_backend()
                )
                
                # Get public key
                public_key = private_key.public_key()
                
                # Encrypt with public key
                ciphertext = public_key.encrypt(
                    data,
                    padding.OAEP(
                        mgf=padding.MGF1(algorithm=hashes.SHA256()),
                        algorithm=hashes.SHA256(),
                        label=None
                    )
                )
                
                result = EncryptedData(
                    data=ciphertext,
                    algorithm=key_info.algorithm,
                    key_id=key_id
                )
            
            elif key_info.algorithm == EncryptionMethod.CHACHA20_POLY1305:
                # Generate random nonce
                nonce = secrets.token_bytes(12)  # 96 bits
                
                # Create cipher
                cipher = Cipher(
                    algorithms.ChaCha20(key_data, nonce),
                    mode=None,
                    backend=default_backend()
                )
                
                encryptor = cipher.encryptor()
                ciphertext = encryptor.update(data) + encryptor.finalize()
                
                result = EncryptedData(
                    data=ciphertext,
                    algorithm=key_info.algorithm,
                    key_id=key_id,
                    iv=nonce
                )
            
            else:
                self.logger.error(f"Encryption not implemented for {key_info.algorithm}")
                return None
            
            # Update key usage
            key_info.usage_count += 1
            
            return result
            
        except Exception as e:
            self.logger.error(f"Error encrypting data: {e}")
            return None
    
    def decrypt(self, encrypted_data: EncryptedData) -> Optional[bytes]:
        """Decrypt data."""
        try:
            # Get key info
            key_info = self.keys.get(encrypted_data.key_id)
            if not key_info:
                self.logger.error(f"Key '{encrypted_data.key_id}' not found")
                return None
            
            # Get key data
            key_data = self._get_key_data(encrypted_data.key_id)
            if not key_data:
                return None
            
            # Decrypt based on algorithm
            if encrypted_data.algorithm == EncryptionMethod.FERNET:
                fernet = Fernet(key_data)
                return fernet.decrypt(encrypted_data.data)
            
            elif encrypted_data.algorithm == EncryptionMethod.AES_256_GCM:
                if not encrypted_data.iv or not encrypted_data.tag:
                    self.logger.error("IV and tag required for AES GCM decryption")
                    return None
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key_data),
                    modes.GCM(encrypted_data.iv, encrypted_data.tag),
                    backend=default_backend()
                )
                
                decryptor = cipher.decryptor()
                return decryptor.update(encrypted_data.data) + decryptor.finalize()
            
            elif encrypted_data.algorithm == EncryptionMethod.AES_256_CBC:
                if not encrypted_data.iv:
                    self.logger.error("IV required for AES CBC decryption")
                    return None
                
                # Create cipher
                cipher = Cipher(
                    algorithms.AES(key_data),
                    modes.CBC(encrypted_data.iv),
                    backend=default_backend()
                )
                
                decryptor = cipher.decryptor()
                padded_data = decryptor.update(encrypted_data.data) + decryptor.finalize()
                
                # Remove padding
                padding_length = padded_data[-1]
                return padded_data[:-padding_length]
            
            elif encrypted_data.algorithm in [EncryptionMethod.RSA_2048, EncryptionMethod.RSA_4096]:
                # Load private key
                private_key = serialization.load_pem_private_key(
                    key_data,
                    password=None,
                    backend=default_backend()
                )
                
                # Decrypt with private key
                return private_key.decrypt(
                    encrypted_data.data,
                    padding.OAEP(
                        mgf=padding.MGF1(algorithm=hashes.SHA256()),
                        algorithm=hashes.SHA256(),
                        label=None
                    )
                )
            
            elif encrypted_data.algorithm == EncryptionMethod.CHACHA20_POLY1305:
                if not encrypted_data.iv:
                    self.logger.error("Nonce required for ChaCha20 decryption")
                    return None
                
                # Create cipher
                cipher = Cipher(
                    algorithms.ChaCha20(key_data, encrypted_data.iv),
                    mode=None,
                    backend=default_backend()
                )
                
                decryptor = cipher.decryptor()
                return decryptor.update(encrypted_data.data) + decryptor.finalize()
            
            else:
                self.logger.error(f"Decryption not implemented for {encrypted_data.algorithm}")
                return None
            
        except Exception as e:
            self.logger.error(f"Error decrypting data: {e}")
            return None
    
    def hash_data(self, data: Union[str, bytes], algorithm: HashAlgorithm = HashAlgorithm.SHA256) -> str:
        """Hash data using specified algorithm."""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            if algorithm == HashAlgorithm.SHA256:
                hash_obj = hashlib.sha256(data)
            elif algorithm == HashAlgorithm.SHA384:
                hash_obj = hashlib.sha384(data)
            elif algorithm == HashAlgorithm.SHA512:
                hash_obj = hashlib.sha512(data)
            elif algorithm == HashAlgorithm.BLAKE2B:
                hash_obj = hashlib.blake2b(data)
            elif algorithm == HashAlgorithm.BLAKE2S:
                hash_obj = hashlib.blake2s(data)
            else:
                raise ValueError(f"Unsupported hash algorithm: {algorithm}")
            
            return hash_obj.hexdigest()
            
        except Exception as e:
            self.logger.error(f"Error hashing data: {e}")
            return ""
    
    def sign_data(self, data: Union[str, bytes], key_id: str) -> Optional[bytes]:
        """Sign data using private key."""
        try:
            # Get key info
            key_info = self.keys.get(key_id)
            if not key_info or key_info.key_type != KeyType.ASYMMETRIC_PRIVATE:
                self.logger.error(f"Private key '{key_id}' not found")
                return None
            
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Get key data
            key_data = self._get_key_data(key_id)
            if not key_data:
                return None
            
            # Load private key
            private_key = serialization.load_pem_private_key(
                key_data,
                password=None,
                backend=default_backend()
            )
            
            # Sign based on key type
            if key_info.algorithm in [EncryptionMethod.RSA_2048, EncryptionMethod.RSA_4096]:
                signature = private_key.sign(
                    data,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
            elif key_info.algorithm in [EncryptionMethod.ECC_P256, EncryptionMethod.ECC_P384]:
                signature = private_key.sign(
                    data,
                    ec.ECDSA(hashes.SHA256())
                )
            else:
                self.logger.error(f"Signing not supported for {key_info.algorithm}")
                return None
            
            return signature
            
        except Exception as e:
            self.logger.error(f"Error signing data: {e}")
            return None
    
    def verify_signature(self, data: Union[str, bytes], signature: bytes, key_id: str) -> bool:
        """Verify signature using public key."""
        try:
            # Get key info
            key_info = self.keys.get(key_id)
            if not key_info or key_info.key_type != KeyType.ASYMMETRIC_PRIVATE:
                self.logger.error(f"Private key '{key_id}' not found")
                return False
            
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            # Get key data
            key_data = self._get_key_data(key_id)
            if not key_data:
                return False
            
            # Load private key and get public key
            private_key = serialization.load_pem_private_key(
                key_data,
                password=None,
                backend=default_backend()
            )
            public_key = private_key.public_key()
            
            # Verify based on key type
            if key_info.algorithm in [EncryptionMethod.RSA_2048, EncryptionMethod.RSA_4096]:
                public_key.verify(
                    signature,
                    data,
                    padding.PSS(
                        mgf=padding.MGF1(hashes.SHA256()),
                        salt_length=padding.PSS.MAX_LENGTH
                    ),
                    hashes.SHA256()
                )
            elif key_info.algorithm in [EncryptionMethod.ECC_P256, EncryptionMethod.ECC_P384]:
                public_key.verify(
                    signature,
                    data,
                    ec.ECDSA(hashes.SHA256())
                )
            else:
                self.logger.error(f"Signature verification not supported for {key_info.algorithm}")
                return False
            
            return True
            
        except Exception:
            return False
    
    def get_public_key(self, key_id: str) -> Optional[bytes]:
        """Get public key from private key."""
        try:
            # Get key info
            key_info = self.keys.get(key_id)
            if not key_info or key_info.key_type != KeyType.ASYMMETRIC_PRIVATE:
                return None
            
            # Get key data
            key_data = self._get_key_data(key_id)
            if not key_data:
                return None
            
            # Load private key
            private_key = serialization.load_pem_private_key(
                key_data,
                password=None,
                backend=default_backend()
            )
            
            # Get public key
            public_key = private_key.public_key()
            
            # Serialize public key
            return public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )
            
        except Exception as e:
            self.logger.error(f"Error getting public key: {e}")
            return None
    
    def delete_key(self, key_id: str) -> bool:
        """Delete a key."""
        try:
            if key_id not in self.keys:
                return False
            
            # Remove from memory
            del self.keys[key_id]
            if key_id in self.key_data:
                del self.key_data[key_id]
            
            # Remove file
            key_file = self.keys_dir / f"{key_id}.key"
            if key_file.exists():
                key_file.unlink()
            
            self.logger.info(f"Deleted key '{key_id}'")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting key: {e}")
            return False
    
    def list_keys(self) -> List[KeyInfo]:
        """List all keys."""
        return list(self.keys.values())
    
    def get_key_info(self, key_id: str) -> Optional[KeyInfo]:
        """Get key information."""
        return self.keys.get(key_id)
    
    def save_keys(self) -> bool:
        """Save keys to storage."""
        try:
            keys_data = {}
            for key_id, key_info in self.keys.items():
                keys_data[key_id] = {
                    'key_id': key_info.key_id,
                    'key_type': key_info.key_type.value,
                    'algorithm': key_info.algorithm.value,
                    'created_at': key_info.created_at.isoformat(),
                    'expires_at': key_info.expires_at.isoformat() if key_info.expires_at else None,
                    'description': key_info.description,
                    'metadata': key_info.metadata,
                    'is_active': key_info.is_active,
                    'usage_count': key_info.usage_count,
                    'max_usage': key_info.max_usage
                }
            
            keys_file = self.keys_dir / "keys.json"
            with open(keys_file, 'w', encoding='utf-8') as f:
                json.dump(keys_data, f, indent=2, ensure_ascii=False)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Error saving keys: {e}")
            return False
    
    def cleanup_expired_keys(self):
        """Clean up expired keys."""
        try:
            expired_keys = [key_id for key_id, key_info in self.keys.items() 
                          if key_info.is_expired()]
            
            for key_id in expired_keys:
                self.delete_key(key_id)
            
            if expired_keys:
                self.logger.info(f"Cleaned up {len(expired_keys)} expired keys")
                
        except Exception as e:
            self.logger.error(f"Error cleaning up expired keys: {e}")
    
    def get_encryption_stats(self) -> Dict[str, Any]:
        """Get encryption statistics."""
        try:
            total_keys = len(self.keys)
            active_keys = sum(1 for key in self.keys.values() if key.is_active)
            expired_keys = sum(1 for key in self.keys.values() if key.is_expired())
            
            algorithm_counts = {}
            for key in self.keys.values():
                algo = key.algorithm.value
                algorithm_counts[algo] = algorithm_counts.get(algo, 0) + 1
            
            key_type_counts = {}
            for key in self.keys.values():
                key_type = key.key_type.value
                key_type_counts[key_type] = key_type_counts.get(key_type, 0) + 1
            
            total_usage = sum(key.usage_count for key in self.keys.values())
            
            return {
                'total_keys': total_keys,
                'active_keys': active_keys,
                'expired_keys': expired_keys,
                'algorithm_distribution': algorithm_counts,
                'key_type_distribution': key_type_counts,
                'total_usage_count': total_usage,
                'total_certificates': len(self.certificates)
            }
            
        except Exception as e:
            self.logger.error(f"Error getting encryption stats: {e}")
            return {}