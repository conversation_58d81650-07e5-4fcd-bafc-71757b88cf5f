#!/usr/bin/env python3
"""
Paper Trading Test Script

Author: inkbytefo
Description: Simple test script to verify paper trading configuration
"""

import os
import sys
import yaml
from pathlib import Path

def test_sandbox_configuration():
    """Test sandbox mode configuration."""
    
    print("=" * 60)
    print("PAPER TRADING CONFIGURATION TEST")
    print("=" * 60)
    
    # Get project root
    project_root = Path(__file__).parent.parent
    
    # Check exchanges.yaml
    exchanges_file = project_root / "config" / "exchanges.yaml"
    
    if not exchanges_file.exists():
        print("❌ exchanges.yaml file not found!")
        return False
    
    try:
        with open(exchanges_file, 'r') as f:
            exchanges_config = yaml.safe_load(f)
        
        print("✓ exchanges.yaml loaded successfully")
        
        # Check sandbox mode settings
        sandbox_enabled = False
        for exchange_name, config in exchanges_config.items():
            if isinstance(config, dict) and config.get('sandbox_mode', False):
                print(f"✓ Sandbox mode enabled for {exchange_name}")
                sandbox_enabled = True
            else:
                print(f"ℹ Sandbox mode disabled for {exchange_name}")
        
        if not sandbox_enabled:
            print("⚠ Warning: No exchanges have sandbox mode enabled")
            print("To enable sandbox mode, set 'sandbox_mode: true' in exchanges.yaml")
        
        # Check other config files
        config_files = [
            "trading.yaml",
            "risk.yaml",
            "hummingbot_api.yaml"
        ]
        
        for config_file in config_files:
            config_path = project_root / "config" / config_file
            if config_path.exists():
                print(f"✓ {config_file} found")
            else:
                print(f"⚠ {config_file} not found")
        
        # Check environment files
        env_files = [".env", ".env.hummingbot"]
        for env_file in env_files:
            env_path = project_root / env_file
            if env_path.exists():
                print(f"✓ {env_file} found")
            else:
                print(f"⚠ {env_file} not found")
        
        print("\n" + "=" * 60)
        print("CONFIGURATION SUMMARY")
        print("=" * 60)
        
        if sandbox_enabled:
            print("✅ Paper trading is configured and ready!")
            print("\nNext steps:")
            print("1. Start the trading agent with: python main.py")
            print("2. Monitor logs for ping-pong strategy signals")
            print("3. Check that testnet endpoints are being used")
        else:
            print("⚠ Paper trading needs configuration")
            print("\nTo enable paper trading:")
            print("1. Edit config/exchanges.yaml")
            print("2. Set 'sandbox_mode: true' for desired exchanges")
            print("3. Restart the application")
        
        print("=" * 60)
        return sandbox_enabled
        
    except Exception as e:
        print(f"❌ Error reading configuration: {e}")
        return False

def test_code_structure():
    """Test if required code files exist."""
    
    print("\n" + "=" * 60)
    print("CODE STRUCTURE TEST")
    print("=" * 60)
    
    project_root = Path(__file__).parent.parent
    src_dir = project_root / "src"
    
    required_files = [
        "src/core/agent.py",
        "src/decision/decision_manager.py",
        "src/execution/hummingbot_service.py",
        "src/config/settings.py"
    ]
    
    all_exist = True
    for file_path in required_files:
        full_path = project_root / file_path
        if full_path.exists():
            print(f"✓ {file_path}")
        else:
            print(f"❌ {file_path} missing")
            all_exist = False
    
    if all_exist:
        print("✅ All required files are present")
    else:
        print("❌ Some required files are missing")
    
    return all_exist

if __name__ == "__main__":
    try:
        config_ok = test_sandbox_configuration()
        structure_ok = test_code_structure()
        
        print("\n" + "=" * 60)
        print("FINAL RESULT")
        print("=" * 60)
        
        if config_ok and structure_ok:
            print("🎉 Paper trading system is ready!")
            print("You can now run the main application.")
        else:
            print("⚠ Some issues need to be resolved before running.")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)