#!/usr/bin/env python3
"""
Base Classes for AI Trading Agent

Author: inkbytefo
Description: Common base classes to reduce code duplication and ensure consistency
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass, field
from enum import Enum

from .common import get_logger, format_timestamp, PerformanceTimer


class ComponentStatus(Enum):
    """Status of system components."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class ComponentMetrics:
    """Base metrics for system components."""
    component_name: str
    status: ComponentStatus = ComponentStatus.STOPPED
    start_time: Optional[datetime] = None
    last_update: Optional[datetime] = None
    error_count: int = 0
    success_count: int = 0
    total_operations: int = 0
    average_operation_time: float = 0.0
    custom_metrics: Dict[str, Any] = field(default_factory=dict)


class BaseComponent(ABC):
    """Base class for all system components."""
    
    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        self.component_name = component_name
        self.config = config or {}
        self.logger = get_logger(f"{self.__class__.__module__}.{self.__class__.__name__}")
        
        # Component state
        self.metrics = ComponentMetrics(component_name=component_name)
        self._shutdown_event = asyncio.Event()
        self._tasks: List[asyncio.Task] = []
        
        # Performance tracking
        self._operation_times: List[float] = []
        self._max_operation_history = 1000
    
    async def start(self) -> None:
        """Start the component."""
        if self.metrics.status == ComponentStatus.RUNNING:
            self.logger.warning(f"{self.component_name} is already running")
            return
        
        self.logger.info(f"Starting {self.component_name}...")
        self.metrics.status = ComponentStatus.STARTING
        self.metrics.start_time = datetime.now()
        
        try:
            await self._start_implementation()
            self.metrics.status = ComponentStatus.RUNNING
            self.logger.info(f"{self.component_name} started successfully")
        except Exception as e:
            self.metrics.status = ComponentStatus.ERROR
            self.metrics.error_count += 1
            self.logger.error(f"Failed to start {self.component_name}: {e}")
            raise
    
    async def stop(self) -> None:
        """Stop the component."""
        if self.metrics.status == ComponentStatus.STOPPED:
            self.logger.warning(f"{self.component_name} is already stopped")
            return
        
        self.logger.info(f"Stopping {self.component_name}...")
        self.metrics.status = ComponentStatus.STOPPING
        
        # Signal shutdown
        self._shutdown_event.set()
        
        # Cancel all tasks
        for task in self._tasks:
            if not task.done():
                task.cancel()
        
        # Wait for tasks to complete
        if self._tasks:
            await asyncio.gather(*self._tasks, return_exceptions=True)
        
        try:
            await self._stop_implementation()
            self.metrics.status = ComponentStatus.STOPPED
            self.logger.info(f"{self.component_name} stopped successfully")
        except Exception as e:
            self.metrics.status = ComponentStatus.ERROR
            self.metrics.error_count += 1
            self.logger.error(f"Error stopping {self.component_name}: {e}")
            raise
    
    @abstractmethod
    async def _start_implementation(self) -> None:
        """Component-specific start logic."""
        pass
    
    @abstractmethod
    async def _stop_implementation(self) -> None:
        """Component-specific stop logic."""
        pass
    
    def get_status(self) -> Dict[str, Any]:
        """Get component status and metrics."""
        uptime = None
        if self.metrics.start_time:
            uptime = (datetime.now() - self.metrics.start_time).total_seconds()
        
        return {
            'component_name': self.component_name,
            'status': self.metrics.status.value,
            'start_time': format_timestamp(self.metrics.start_time) if self.metrics.start_time else None,
            'last_update': format_timestamp(self.metrics.last_update) if self.metrics.last_update else None,
            'uptime_seconds': uptime,
            'error_count': self.metrics.error_count,
            'success_count': self.metrics.success_count,
            'total_operations': self.metrics.total_operations,
            'average_operation_time': self.metrics.average_operation_time,
            'custom_metrics': self.metrics.custom_metrics.copy()
        }
    
    def _record_operation(self, duration: float, success: bool = True) -> None:
        """Record operation metrics."""
        self.metrics.total_operations += 1
        self.metrics.last_update = datetime.now()
        
        if success:
            self.metrics.success_count += 1
        else:
            self.metrics.error_count += 1
        
        # Track operation times
        self._operation_times.append(duration)
        if len(self._operation_times) > self._max_operation_history:
            self._operation_times.pop(0)
        
        # Update average
        if self._operation_times:
            self.metrics.average_operation_time = sum(self._operation_times) / len(self._operation_times)
    
    def _create_task(self, coro, name: str = None) -> asyncio.Task:
        """Create and track an asyncio task."""
        task = asyncio.create_task(coro, name=name)
        self._tasks.append(task)
        return task
    
    @property
    def is_running(self) -> bool:
        """Check if component is running."""
        return self.metrics.status == ComponentStatus.RUNNING
    
    @property
    def should_shutdown(self) -> bool:
        """Check if component should shutdown."""
        return self._shutdown_event.is_set()


class BaseManager(BaseComponent):
    """Base class for manager components."""
    
    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name, config)
        self._managed_components: Dict[str, BaseComponent] = {}
    
    async def add_component(self, name: str, component: BaseComponent) -> None:
        """Add a managed component."""
        self._managed_components[name] = component
        if self.is_running:
            await component.start()
    
    async def remove_component(self, name: str) -> None:
        """Remove a managed component."""
        if name in self._managed_components:
            component = self._managed_components.pop(name)
            if component.is_running:
                await component.stop()
    
    async def _start_implementation(self) -> None:
        """Start all managed components."""
        for name, component in self._managed_components.items():
            try:
                await component.start()
            except Exception as e:
                self.logger.error(f"Failed to start component {name}: {e}")
                raise
    
    async def _stop_implementation(self) -> None:
        """Stop all managed components."""
        for name, component in self._managed_components.items():
            try:
                await component.stop()
            except Exception as e:
                self.logger.error(f"Error stopping component {name}: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get manager and all component statuses."""
        status = super().get_status()
        status['managed_components'] = {
            name: component.get_status() 
            for name, component in self._managed_components.items()
        }
        return status


class BaseDataCollector(BaseComponent):
    """Base class for data collectors."""
    
    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name, config)
        self.last_update: Optional[datetime] = None
        self.data_cache: Dict[str, Any] = {}
        self.collection_interval = self.config.get('collection_interval', 60)  # seconds
    
    async def _start_implementation(self) -> None:
        """Start data collection loop."""
        self._create_task(self._collection_loop(), name=f"{self.component_name}_collection")
    
    async def _stop_implementation(self) -> None:
        """Stop data collection."""
        pass  # Tasks are cancelled in base class
    
    async def _collection_loop(self) -> None:
        """Main data collection loop."""
        while not self.should_shutdown:
            try:
                with PerformanceTimer(f"{self.component_name}_collection") as timer:
                    await self._collect_data()
                    self.last_update = datetime.now()
                
                self._record_operation(timer.duration, success=True)
                
            except Exception as e:
                self.logger.error(f"Error in {self.component_name} collection: {e}")
                self._record_operation(0, success=False)
            
            # Wait for next collection cycle
            try:
                await asyncio.wait_for(
                    self._shutdown_event.wait(), 
                    timeout=self.collection_interval
                )
                break  # Shutdown requested
            except asyncio.TimeoutError:
                continue  # Normal timeout, continue loop
    
    @abstractmethod
    async def _collect_data(self) -> None:
        """Implement data collection logic."""
        pass
    
    def get_last_update(self) -> Optional[datetime]:
        """Get timestamp of last successful data collection."""
        return self.last_update
    
    def get_data_freshness(self) -> Dict[str, Any]:
        """Get data freshness information."""
        if self.last_update:
            age_seconds = (datetime.now() - self.last_update).total_seconds()
            is_stale = age_seconds > (self.collection_interval * 2)
        else:
            age_seconds = None
            is_stale = True
        
        return {
            'last_update': format_timestamp(self.last_update) if self.last_update else None,
            'age_seconds': age_seconds,
            'is_stale': is_stale,
            'collection_interval': self.collection_interval
        }


class BaseMonitor(BaseComponent):
    """Base class for monitoring components."""
    
    def __init__(self, component_name: str, config: Optional[Dict[str, Any]] = None):
        super().__init__(component_name, config)
        self.monitoring_interval = self.config.get('monitoring_interval', 30)  # seconds
        self.alerts: List[Dict[str, Any]] = []
        self.max_alerts = self.config.get('max_alerts', 1000)
    
    async def _start_implementation(self) -> None:
        """Start monitoring loop."""
        self._create_task(self._monitoring_loop(), name=f"{self.component_name}_monitoring")
    
    async def _stop_implementation(self) -> None:
        """Stop monitoring."""
        pass  # Tasks are cancelled in base class
    
    async def _monitoring_loop(self) -> None:
        """Main monitoring loop."""
        while not self.should_shutdown:
            try:
                with PerformanceTimer(f"{self.component_name}_monitoring") as timer:
                    await self._perform_monitoring()
                
                self._record_operation(timer.duration, success=True)
                
            except Exception as e:
                self.logger.error(f"Error in {self.component_name} monitoring: {e}")
                self._record_operation(0, success=False)
            
            # Wait for next monitoring cycle
            try:
                await asyncio.wait_for(
                    self._shutdown_event.wait(), 
                    timeout=self.monitoring_interval
                )
                break  # Shutdown requested
            except asyncio.TimeoutError:
                continue  # Normal timeout, continue loop
    
    @abstractmethod
    async def _perform_monitoring(self) -> None:
        """Implement monitoring logic."""
        pass
    
    def add_alert(self, alert_type: str, message: str, severity: str = "info", 
                  details: Optional[Dict[str, Any]] = None) -> None:
        """Add an alert."""
        alert = {
            'timestamp': datetime.now(),
            'type': alert_type,
            'message': message,
            'severity': severity,
            'details': details or {},
            'component': self.component_name
        }
        
        self.alerts.append(alert)
        
        # Limit alert history
        if len(self.alerts) > self.max_alerts:
            self.alerts.pop(0)
        
        # Log alert
        log_level = getattr(logging, severity.upper(), logging.INFO)
        self.logger.log(log_level, f"Alert: {message}")
    
    def get_alerts(self, severity: Optional[str] = None, 
                   limit: Optional[int] = None) -> List[Dict[str, Any]]:
        """Get alerts, optionally filtered by severity."""
        alerts = self.alerts
        
        if severity:
            alerts = [a for a in alerts if a['severity'] == severity]
        
        if limit:
            alerts = alerts[-limit:]
        
        return [{
            **alert,
            'timestamp': format_timestamp(alert['timestamp'])
        } for alert in alerts]