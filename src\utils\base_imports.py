#!/usr/bin/env python3
"""
Base Imports Module for AI Trading Agent

Author: inkbytefo
Description: Common imports to reduce code duplication across modules
"""

# Standard library imports
import asyncio
import logging
import json
import os
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple, Union, Callable
from dataclasses import dataclass, field
from enum import Enum
from functools import wraps
from collections import defaultdict, deque
import statistics
import uuid

# Third-party imports
import numpy as np
import pandas as pd
import aiohttp

# Common logger setup
def get_module_logger(module_name: str) -> logging.Logger:
    """Get a logger for the specified module."""
    return logging.getLogger(module_name)

# Common decorators
def async_retry(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for async functions with retry logic."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
            
        return wrapper
    return decorator

# Common utility functions
def safe_float(value: Any, default: float = 0.0) -> float:
    """Safely convert value to float."""
    try:
        return float(value)
    except (ValueError, TypeError):
        return default

def safe_int(value: Any, default: int = 0) -> int:
    """Safely convert value to int."""
    try:
        return int(value)
    except (ValueError, TypeError):
        return default

def timestamp_to_datetime(timestamp: Union[int, float, str]) -> datetime:
    """Convert timestamp to datetime object."""
    try:
        if isinstance(timestamp, str):
            return datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
        return datetime.fromtimestamp(float(timestamp))
    except (ValueError, TypeError):
        return datetime.now()

def format_currency(amount: float, currency: str = 'USD') -> str:
    """Format currency amount."""
    return f"{amount:.2f} {currency}"

def calculate_percentage_change(old_value: float, new_value: float) -> float:
    """Calculate percentage change between two values."""
    if old_value == 0:
        return 0.0
    return ((new_value - old_value) / old_value) * 100

# Common validation functions
def validate_symbol(symbol: str) -> bool:
    """Validate trading symbol format."""
    if not symbol or not isinstance(symbol, str):
        return False
    return len(symbol) >= 3 and symbol.replace('/', '').replace('-', '').isalnum()

def validate_price(price: Union[int, float]) -> bool:
    """Validate price value."""
    try:
        return float(price) > 0
    except (ValueError, TypeError):
        return False

def validate_quantity(quantity: Union[int, float]) -> bool:
    """Validate quantity value."""
    try:
        return float(quantity) > 0
    except (ValueError, TypeError):
        return False