#!/usr/bin/env python3
"""
Common Utilities for AI Trading Agent

Author: inkbytefo
Description: Shared utility functions to reduce code duplication
"""

import logging
import asyncio
from typing import Dict, Any, Optional, List, Union, Callable
from datetime import datetime, timedelta
from functools import wraps
import time


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance with consistent configuration.
    
    Args:
        name: Logger name (usually __name__ or class name)
        
    Returns:
        Configured logger instance
    """
    return logging.getLogger(name)


def safe_get(data: Dict[str, Any], key: str, default: Any = None) -> Any:
    """Safely get value from dictionary with default fallback.
    
    Args:
        data: Dictionary to get value from
        key: Key to look for
        default: Default value if key not found
        
    Returns:
        Value from dictionary or default
    """
    try:
        return data.get(key, default)
    except (AttributeError, TypeError):
        return default


def format_timestamp(timestamp: Optional[datetime] = None) -> str:
    """Format timestamp for consistent logging and display.
    
    Args:
        timestamp: Timestamp to format (defaults to now)
        
    Returns:
        Formatted timestamp string
    """
    if timestamp is None:
        timestamp = datetime.now()
    return timestamp.strftime('%Y-%m-%d %H:%M:%S')


def calculate_duration(start_time: datetime, end_time: Optional[datetime] = None) -> float:
    """Calculate duration between two timestamps.
    
    Args:
        start_time: Start timestamp
        end_time: End timestamp (defaults to now)
        
    Returns:
        Duration in seconds
    """
    if end_time is None:
        end_time = datetime.now()
    return (end_time - start_time).total_seconds()


def retry_async(max_retries: int = 3, delay: float = 1.0, backoff: float = 2.0):
    """Decorator for async functions with retry logic.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries
        backoff: Backoff multiplier for delay
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    last_exception = e
                    if attempt < max_retries:
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff
                    else:
                        raise last_exception
            
        return wrapper
    return decorator


def validate_config_section(config: Dict[str, Any], required_fields: List[str], 
                          section_name: str = "config") -> bool:
    """Validate that required fields exist in config section.
    
    Args:
        config: Configuration dictionary
        required_fields: List of required field names
        section_name: Name of config section for error messages
        
    Returns:
        True if all required fields present, False otherwise
    """
    missing_fields = []
    for field in required_fields:
        if field not in config:
            missing_fields.append(field)
    
    if missing_fields:
        logger = get_logger(__name__)
        logger.error(f"Missing required fields in {section_name}: {missing_fields}")
        return False
    
    return True


def merge_dicts(*dicts: Dict[str, Any]) -> Dict[str, Any]:
    """Merge multiple dictionaries with later ones taking precedence.
    
    Args:
        *dicts: Variable number of dictionaries to merge
        
    Returns:
        Merged dictionary
    """
    result = {}
    for d in dicts:
        if isinstance(d, dict):
            result.update(d)
    return result


def get_nested_value(data: Dict[str, Any], path: str, default: Any = None) -> Any:
    """Get value from nested dictionary using dot notation.
    
    Args:
        data: Dictionary to search in
        path: Dot-separated path (e.g., 'section.subsection.key')
        default: Default value if path not found
        
    Returns:
        Value at path or default
    """
    try:
        keys = path.split('.')
        current = data
        for key in keys:
            current = current[key]
        return current
    except (KeyError, TypeError, AttributeError):
        return default


def create_error_response(error_message: str, error_code: str = "UNKNOWN_ERROR", 
                         details: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Create standardized error response.
    
    Args:
        error_message: Human-readable error message
        error_code: Machine-readable error code
        details: Additional error details
        
    Returns:
        Standardized error response dictionary
    """
    response = {
        'success': False,
        'error': {
            'message': error_message,
            'code': error_code,
            'timestamp': format_timestamp()
        }
    }
    
    if details:
        response['error']['details'] = details
    
    return response


def create_success_response(data: Any = None, message: str = "Success") -> Dict[str, Any]:
    """Create standardized success response.
    
    Args:
        data: Response data
        message: Success message
        
    Returns:
        Standardized success response dictionary
    """
    response = {
        'success': True,
        'message': message,
        'timestamp': format_timestamp()
    }
    
    if data is not None:
        response['data'] = data
    
    return response


class PerformanceTimer:
    """Context manager for measuring execution time."""
    
    def __init__(self, operation_name: str = "operation"):
        self.operation_name = operation_name
        self.start_time = None
        self.end_time = None
        self.logger = get_logger(self.__class__.__name__)
    
    def __enter__(self):
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        self.end_time = time.time()
        duration = self.end_time - self.start_time
        self.logger.debug(f"{self.operation_name} completed in {duration:.3f}s")
    
    @property
    def duration(self) -> Optional[float]:
        """Get the measured duration in seconds."""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


class RateLimiter:
    """Simple rate limiter for API calls."""
    
    def __init__(self, max_calls: int, time_window: float):
        self.max_calls = max_calls
        self.time_window = time_window
        self.calls = []
        self._lock = asyncio.Lock()
    
    async def acquire(self):
        """Acquire permission to make a call, waiting if necessary."""
        async with self._lock:
            now = time.time()
            
            # Remove old calls outside the time window
            self.calls = [call_time for call_time in self.calls 
                         if now - call_time < self.time_window]
            
            # If we're at the limit, wait
            if len(self.calls) >= self.max_calls:
                sleep_time = self.time_window - (now - self.calls[0])
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                    return await self.acquire()
            
            # Record this call
            self.calls.append(now)