#!/usr/bin/env python3
"""
Common Enums for AI Trading Agent

Author: inkbytefo
Description: Centralized enum definitions to reduce code duplication
"""

from enum import Enum


# Component and System Status Enums
class ComponentStatus(Enum):
    """Status of system components."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class TradingState(Enum):
    """Trading system states."""
    INITIALIZING = "initializing"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"


# Order Related Enums
class OrderStatus(Enum):
    """Order status enumeration."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    NEW = "new"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    CANCELED = "canceled"  # Alternative spelling
    PENDING_CANCEL = "pending_cancel"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"


class OrderType(Enum):
    """Order type enumeration."""
    MARKET = "market"
    LIMIT = "limit"
    LIMIT_MAKER = "limit_maker"
    STOP = "stop"
    STOP_LOSS = "stop_loss"
    STOP_LIMIT = "stop_limit"
    STOP_LOSS_LIMIT = "stop_loss_limit"
    TAKE_PROFIT = "take_profit"
    TAKE_PROFIT_LIMIT = "take_profit_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"
    HIDDEN = "hidden"


class OrderSide(Enum):
    """Order side enumeration."""
    BUY = "buy"
    SELL = "sell"


class TimeInForce(Enum):
    """Time in force enumeration."""
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate Or Cancel
    FOK = "fok"  # Fill Or Kill
    DAY = "day"  # Day order
    GTD = "gtd"  # Good Till Date


class OrderPriority(Enum):
    """Order priority levels."""
    LOW = 1
    NORMAL = 2
    HIGH = 3
    URGENT = 4
    CRITICAL = 5


# Trading Decision Enums
class DecisionType(Enum):
    """Types of trading decisions."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    CLOSE_POSITION = "close_position"
    ADJUST_POSITION = "adjust_position"
    EMERGENCY_EXIT = "emergency_exit"


class SignalType(Enum):
    """Trading signal types."""
    BUY = "buy"
    SELL = "sell"
    HOLD = "hold"
    STRONG_BUY = "strong_buy"
    STRONG_SELL = "strong_sell"


class ConfidenceLevel(Enum):
    """Confidence levels for decisions."""
    VERY_LOW = 0.2
    LOW = 0.4
    MEDIUM = 0.6
    HIGH = 0.8
    VERY_HIGH = 0.9


class RiskLevel(Enum):
    """Risk assessment levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


# Exchange and Venue Enums
class ExchangeType(Enum):
    """Supported exchange types."""
    BINANCE = "binance"
    COINBASE = "coinbase"
    KRAKEN = "kraken"
    BYBIT = "bybit"
    OKEX = "okex"
    HUOBI = "huobi"
    KUCOIN = "kucoin"
    BITFINEX = "bitfinex"
    BITMEX = "bitmex"
    FTX = "ftx"
    DERIBIT = "deribit"
    GATE = "gate"


class ExecutionVenue(Enum):
    """Execution venues."""
    HUMMINGBOT = "hummingbot"
    BINANCE = "binance"
    COINBASE = "coinbase"
    KRAKEN = "kraken"
    UNISWAP = "uniswap"
    SIMULATION = "simulation"


class ExecutionAlgorithm(Enum):
    """Order execution algorithms."""
    MARKET = "market"
    LIMIT = "limit"
    TWAP = "twap"
    VWAP = "vwap"
    IMPLEMENTATION_SHORTFALL = "implementation_shortfall"
    ARRIVAL_PRICE = "arrival_price"
    PARTICIPATION_RATE = "participation_rate"
    ICEBERG = "iceberg"
    HIDDEN = "hidden"
    SMART_ROUTING = "smart_routing"


# Event and System Enums
class EventType(Enum):
    """System event types."""
    MARKET_DATA_UPDATE = "market_data_update"
    TRADE_SIGNAL = "trade_signal"
    ORDER_EXECUTED = "order_executed"
    RISK_ALERT = "risk_alert"
    SYSTEM_STATUS = "system_status"
    ERROR = "error"


# Hummingbot Specific Enums
class HummingbotStatus(Enum):
    """Hummingbot status enumeration."""
    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


class BotStatus(Enum):
    """Bot status enumeration (uppercase variant)."""
    RUNNING = "RUNNING"
    STOPPED = "STOPPED"
    ERROR = "ERROR"
    STARTING = "STARTING"
    STOPPING = "STOPPING"


# Execution Status
class ExecutionStatus(Enum):
    """Execution status types."""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"
    FAILED = "failed"