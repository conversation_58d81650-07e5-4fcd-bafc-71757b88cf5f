#!/usr/bin/env python3
"""
Error Handling Utilities for AI Trading Agent

Author: inkbytefo
Description: Centralized error handling patterns to reduce code duplication
"""

import logging
import traceback
import functools
import asyncio
from typing import Any, Callable, Optional, Union, Dict, List, Type
from datetime import datetime
from enum import Enum


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for better classification."""
    NETWORK = "network"
    DATA = "data"
    VALIDATION = "validation"
    CALCULATION = "calculation"
    CONFIGURATION = "configuration"
    EXTERNAL_API = "external_api"
    DATABASE = "database"
    TRADING = "trading"
    UNKNOWN = "unknown"


class TradingError(Exception):
    """Base exception class for trading-related errors."""
    
    def __init__(self, message: str, category: ErrorCategory = ErrorCategory.UNKNOWN, 
                 severity: ErrorSeverity = ErrorSeverity.MEDIUM, details: Optional[Dict] = None):
        super().__init__(message)
        self.message = message
        self.category = category
        self.severity = severity
        self.details = details or {}
        self.timestamp = datetime.now()


class NetworkError(TradingError):
    """Network-related errors."""
    
    def __init__(self, message: str, status_code: Optional[int] = None, **kwargs):
        super().__init__(message, ErrorCategory.NETWORK, **kwargs)
        self.status_code = status_code


class DataError(TradingError):
    """Data-related errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.DATA, **kwargs)


class ValidationError(TradingError):
    """Validation-related errors."""
    
    def __init__(self, message: str, field: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.VALIDATION, **kwargs)
        self.field = field


class CalculationError(TradingError):
    """Calculation-related errors."""
    
    def __init__(self, message: str, **kwargs):
        super().__init__(message, ErrorCategory.CALCULATION, **kwargs)


class ConfigurationError(TradingError):
    """Configuration-related errors."""
    
    def __init__(self, message: str, config_key: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.CONFIGURATION, **kwargs)
        self.config_key = config_key


class ExternalAPIError(TradingError):
    """External API-related errors."""
    
    def __init__(self, message: str, api_name: Optional[str] = None, **kwargs):
        super().__init__(message, ErrorCategory.EXTERNAL_API, **kwargs)
        self.api_name = api_name


class ErrorHandler:
    """Centralized error handler with logging and recovery strategies."""
    
    def __init__(self, logger: Optional[logging.Logger] = None):
        self.logger = logger or logging.getLogger(__name__)
        self.error_counts: Dict[str, int] = {}
        self.last_errors: Dict[str, datetime] = {}
        self.recovery_strategies: Dict[Type[Exception], Callable] = {}
    
    def register_recovery_strategy(self, exception_type: Type[Exception], strategy: Callable):
        """Register a recovery strategy for a specific exception type.
        
        Args:
            exception_type: Exception class to handle
            strategy: Recovery function to call
        """
        self.recovery_strategies[exception_type] = strategy
    
    def handle_error(self, error: Exception, context: Optional[str] = None, 
                    suppress: bool = False, default_return: Any = None) -> Any:
        """Handle an error with logging and optional recovery.
        
        Args:
            error: Exception to handle
            context: Additional context information
            suppress: Whether to suppress the exception
            default_return: Default value to return if suppressing
            
        Returns:
            Default return value if suppressing, otherwise re-raises
        """
        error_key = f"{type(error).__name__}:{context or 'unknown'}"
        self.error_counts[error_key] = self.error_counts.get(error_key, 0) + 1
        self.last_errors[error_key] = datetime.now()
        
        # Log the error
        self._log_error(error, context)
        
        # Try recovery strategy
        recovery_result = self._try_recovery(error)
        if recovery_result is not None:
            return recovery_result
        
        if suppress:
            return default_return
        else:
            raise error
    
    def _log_error(self, error: Exception, context: Optional[str] = None):
        """Log error with appropriate level based on severity."""
        error_msg = f"Error in {context or 'unknown context'}: {str(error)}"
        
        if isinstance(error, TradingError):
            if error.severity == ErrorSeverity.CRITICAL:
                self.logger.critical(error_msg, exc_info=True)
            elif error.severity == ErrorSeverity.HIGH:
                self.logger.error(error_msg, exc_info=True)
            elif error.severity == ErrorSeverity.MEDIUM:
                self.logger.warning(error_msg)
            else:
                self.logger.info(error_msg)
        else:
            self.logger.error(error_msg, exc_info=True)
    
    def _try_recovery(self, error: Exception) -> Any:
        """Try to recover from error using registered strategies."""
        for exception_type, strategy in self.recovery_strategies.items():
            if isinstance(error, exception_type):
                try:
                    return strategy(error)
                except Exception as recovery_error:
                    self.logger.warning(f"Recovery strategy failed: {recovery_error}")
        return None
    
    def get_error_stats(self) -> Dict[str, Any]:
        """Get error statistics."""
        return {
            'error_counts': self.error_counts.copy(),
            'last_errors': {k: v.isoformat() for k, v in self.last_errors.items()},
            'total_errors': sum(self.error_counts.values())
        }


# Global error handler instance
_global_error_handler = ErrorHandler()


def safe_execute(func: Callable, *args, default_return: Any = None, 
                context: Optional[str] = None, suppress_errors: bool = True, **kwargs) -> Any:
    """Safely execute a function with error handling.
    
    Args:
        func: Function to execute
        *args: Function arguments
        default_return: Value to return on error
        context: Context for error logging
        suppress_errors: Whether to suppress exceptions
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        return _global_error_handler.handle_error(
            e, context or func.__name__, suppress_errors, default_return
        )


async def safe_execute_async(func: Callable, *args, default_return: Any = None,
                           context: Optional[str] = None, suppress_errors: bool = True, **kwargs) -> Any:
    """Safely execute an async function with error handling.
    
    Args:
        func: Async function to execute
        *args: Function arguments
        default_return: Value to return on error
        context: Context for error logging
        suppress_errors: Whether to suppress exceptions
        **kwargs: Function keyword arguments
        
    Returns:
        Function result or default_return on error
    """
    try:
        if asyncio.iscoroutinefunction(func):
            return await func(*args, **kwargs)
        else:
            return func(*args, **kwargs)
    except Exception as e:
        return _global_error_handler.handle_error(
            e, context or func.__name__, suppress_errors, default_return
        )


def error_handler(default_return: Any = None, suppress: bool = True, 
                 context: Optional[str] = None, log_level: str = "error"):
    """Decorator for automatic error handling.
    
    Args:
        default_return: Value to return on error
        suppress: Whether to suppress exceptions
        context: Context for error logging
        log_level: Logging level for errors
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            return safe_execute(
                func, *args, 
                default_return=default_return,
                context=context or func.__name__,
                suppress_errors=suppress,
                **kwargs
            )
        return wrapper
    return decorator


def async_error_handler(default_return: Any = None, suppress: bool = True,
                       context: Optional[str] = None, log_level: str = "error"):
    """Decorator for automatic async error handling.
    
    Args:
        default_return: Value to return on error
        suppress: Whether to suppress exceptions
        context: Context for error logging
        log_level: Logging level for errors
        
    Returns:
        Decorated async function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            return await safe_execute_async(
                func, *args,
                default_return=default_return,
                context=context or func.__name__,
                suppress_errors=suppress,
                **kwargs
            )
        return wrapper
    return decorator


def retry_on_error(max_retries: int = 3, delay: float = 1.0, 
                  backoff_factor: float = 2.0, exceptions: tuple = (Exception,)):
    """Decorator for retrying function calls on specific exceptions.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exception types to retry on
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        _global_error_handler.logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {current_delay} seconds..."
                        )
                        import time
                        time.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        _global_error_handler.logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
            
            if last_exception:
                raise last_exception
        return wrapper
    return decorator


async def async_retry_on_error(max_retries: int = 3, delay: float = 1.0,
                              backoff_factor: float = 2.0, exceptions: tuple = (Exception,)):
    """Async decorator for retrying function calls on specific exceptions.
    
    Args:
        max_retries: Maximum number of retry attempts
        delay: Initial delay between retries (seconds)
        backoff_factor: Multiplier for delay on each retry
        exceptions: Tuple of exception types to retry on
        
    Returns:
        Decorated async function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            last_exception = None
            current_delay = delay
            
            for attempt in range(max_retries + 1):
                try:
                    if asyncio.iscoroutinefunction(func):
                        return await func(*args, **kwargs)
                    else:
                        return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_retries:
                        _global_error_handler.logger.warning(
                            f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                            f"Retrying in {current_delay} seconds..."
                        )
                        await asyncio.sleep(current_delay)
                        current_delay *= backoff_factor
                    else:
                        _global_error_handler.logger.error(
                            f"All {max_retries + 1} attempts failed for {func.__name__}"
                        )
            
            if last_exception:
                raise last_exception
        return wrapper
    return decorator


def circuit_breaker(failure_threshold: int = 5, recovery_timeout: float = 60.0):
    """Circuit breaker pattern decorator.
    
    Args:
        failure_threshold: Number of failures before opening circuit
        recovery_timeout: Time to wait before trying to close circuit
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        state = {'failures': 0, 'last_failure': None, 'state': 'closed'}
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            now = datetime.now()
            
            # Check if circuit should be half-open
            if (state['state'] == 'open' and state['last_failure'] and 
                (now - state['last_failure']).total_seconds() > recovery_timeout):
                state['state'] = 'half-open'
            
            # If circuit is open, fail fast
            if state['state'] == 'open':
                raise TradingError(
                    f"Circuit breaker is open for {func.__name__}",
                    ErrorCategory.UNKNOWN,
                    ErrorSeverity.HIGH
                )
            
            try:
                result = func(*args, **kwargs)
                # Success - reset circuit
                if state['state'] == 'half-open':
                    state['state'] = 'closed'
                    state['failures'] = 0
                return result
            except Exception as e:
                state['failures'] += 1
                state['last_failure'] = now
                
                if state['failures'] >= failure_threshold:
                    state['state'] = 'open'
                    _global_error_handler.logger.error(
                        f"Circuit breaker opened for {func.__name__} after {failure_threshold} failures"
                    )
                
                raise e
        return wrapper
    return decorator


def get_global_error_handler() -> ErrorHandler:
    """Get the global error handler instance."""
    return _global_error_handler


def configure_global_error_handler(logger: Optional[logging.Logger] = None) -> ErrorHandler:
    """Configure the global error handler.
    
    Args:
        logger: Logger instance to use
        
    Returns:
        Configured error handler
    """
    global _global_error_handler
    if logger:
        _global_error_handler.logger = logger
    return _global_error_handler