"""Helper Utilities for AI Trading Agent.

This module provides various utility functions for trading operations.

Author: inkbytefo
"""

import re
import math
from decimal import Decimal, ROUND_HALF_UP
from typing import Union, Optional, Tuple, List
from datetime import datetime, timedelta


def format_currency(
    amount: Union[float, int, Decimal],
    currency: str = "USD",
    decimals: int = 2
) -> str:
    """Format amount as currency string.
    
    Args:
        amount: Amount to format
        currency: Currency symbol or code
        decimals: Number of decimal places
        
    Returns:
        Formatted currency string
    """
    try:
        if isinstance(amount, (int, float)):
            amount = Decimal(str(amount))
        
        # Round to specified decimals
        rounded_amount = amount.quantize(
            Decimal('0.' + '0' * decimals),
            rounding=ROUND_HALF_UP
        )
        
        # Format with commas
        formatted = f"{rounded_amount:,.{decimals}f}"
        
        # Add currency symbol
        currency_symbols = {
            'USD': '$',
            'EUR': '€',
            'GBP': '£',
            'JPY': '¥',
            'BTC': '₿',
            'ETH': 'Ξ'
        }
        
        symbol = currency_symbols.get(currency.upper(), currency.upper())
        
        if currency.upper() in ['USD', 'EUR', 'GBP']:
            return f"{symbol}{formatted}"
        else:
            return f"{formatted} {symbol}"
            
    except Exception:
        return f"{amount} {currency}"


def calculate_percentage_change(
    old_value: Union[float, int, Decimal],
    new_value: Union[float, int, Decimal],
    decimals: int = 2
) -> float:
    """Calculate percentage change between two values.
    
    Args:
        old_value: Original value
        new_value: New value
        decimals: Number of decimal places
        
    Returns:
        Percentage change
    """
    try:
        if old_value == 0:
            return 0.0 if new_value == 0 else float('inf')
        
        change = ((new_value - old_value) / old_value) * 100
        return round(float(change), decimals)
        
    except (TypeError, ValueError, ZeroDivisionError):
        return 0.0


def validate_trading_pair(pair: str) -> bool:
    """Validate trading pair format.
    
    Args:
        pair: Trading pair string (e.g., 'BTC/USDT', 'BTC-USDT', 'BTCUSDT')
        
    Returns:
        True if valid format, False otherwise
    """
    if not isinstance(pair, str):
        return False
    
    # Common trading pair patterns
    patterns = [
        r'^[A-Z]{2,10}[/\-][A-Z]{2,10}$',  # BTC/USDT, BTC-USDT
        r'^[A-Z]{6,20}$'                   # BTCUSDT
    ]
    
    for pattern in patterns:
        if re.match(pattern, pair.upper()):
            return True
    
    return False


def normalize_trading_pair(pair: str, separator: str = '/') -> str:
    """Normalize trading pair to standard format.
    
    Args:
        pair: Trading pair string
        separator: Desired separator
        
    Returns:
        Normalized trading pair
    """
    if not validate_trading_pair(pair):
        raise ValueError(f"Invalid trading pair format: {pair}")
    
    pair = pair.upper()
    
    # If already has separator, replace it
    if '/' in pair:
        return pair.replace('/', separator)
    elif '-' in pair:
        return pair.replace('-', separator)
    
    # For concatenated pairs like BTCUSDT, try to split
    # This is a simple heuristic and may not work for all cases
    common_quote_currencies = ['USDT', 'USDC', 'BTC', 'ETH', 'BNB', 'USD', 'EUR']
    
    for quote in common_quote_currencies:
        if pair.endswith(quote) and len(pair) > len(quote):
            base = pair[:-len(quote)]
            return f"{base}{separator}{quote}"
    
    # If can't split, return as is
    return pair


def parse_timeframe(timeframe: str) -> int:
    """Parse timeframe string to seconds.
    
    Args:
        timeframe: Timeframe string (e.g., '1m', '5m', '1h', '1d')
        
    Returns:
        Timeframe in seconds
    """
    if not isinstance(timeframe, str):
        raise ValueError("Timeframe must be a string")
    
    timeframe = timeframe.lower().strip()
    
    # Extract number and unit
    match = re.match(r'^(\d+)([smhd])$', timeframe)
    if not match:
        raise ValueError(f"Invalid timeframe format: {timeframe}")
    
    number, unit = match.groups()
    number = int(number)
    
    multipliers = {
        's': 1,
        'm': 60,
        'h': 3600,
        'd': 86400
    }
    
    return number * multipliers[unit]


def safe_divide(
    numerator: Union[float, int, Decimal],
    denominator: Union[float, int, Decimal],
    default: Union[float, int, Decimal] = 0
) -> Union[float, Decimal]:
    """Safely divide two numbers, returning default if division by zero.
    
    Args:
        numerator: Numerator
        denominator: Denominator
        default: Default value if division by zero
        
    Returns:
        Division result or default value
    """
    try:
        if denominator == 0:
            return default
        return numerator / denominator
    except (TypeError, ValueError):
        return default


def round_to_precision(
    value: Union[float, int, Decimal],
    precision: int
) -> Decimal:
    """Round value to specified precision.
    
    Args:
        value: Value to round
        precision: Number of decimal places
        
    Returns:
        Rounded decimal value
    """
    if isinstance(value, (int, float)):
        value = Decimal(str(value))
    
    if precision < 0:
        precision = 0
    
    return value.quantize(
        Decimal('0.' + '0' * precision),
        rounding=ROUND_HALF_UP
    )


def calculate_position_size(
    account_balance: float,
    risk_percentage: float,
    entry_price: float,
    stop_loss_price: float,
    min_position_size: float = 0.001
) -> float:
    """Calculate position size based on risk management.
    
    Args:
        account_balance: Total account balance
        risk_percentage: Risk percentage (0.01 = 1%)
        entry_price: Entry price
        stop_loss_price: Stop loss price
        min_position_size: Minimum position size
        
    Returns:
        Calculated position size
    """
    try:
        if stop_loss_price <= 0 or entry_price <= 0:
            return min_position_size
        
        risk_amount = account_balance * risk_percentage
        price_difference = abs(entry_price - stop_loss_price)
        
        if price_difference == 0:
            return min_position_size
        
        position_size = risk_amount / price_difference
        
        return max(position_size, min_position_size)
        
    except (TypeError, ValueError, ZeroDivisionError):
        return min_position_size


def calculate_profit_loss(
    entry_price: float,
    current_price: float,
    quantity: float,
    side: str = 'long'
) -> Tuple[float, float]:
    """Calculate profit/loss for a position.
    
    Args:
        entry_price: Entry price
        current_price: Current price
        quantity: Position quantity
        side: Position side ('long' or 'short')
        
    Returns:
        Tuple of (absolute_pnl, percentage_pnl)
    """
    try:
        if side.lower() == 'long':
            absolute_pnl = (current_price - entry_price) * quantity
        else:  # short
            absolute_pnl = (entry_price - current_price) * quantity
        
        percentage_pnl = calculate_percentage_change(entry_price, current_price)
        if side.lower() == 'short':
            percentage_pnl = -percentage_pnl
        
        return absolute_pnl, percentage_pnl
        
    except (TypeError, ValueError):
        return 0.0, 0.0


def is_market_hours(
    market: str = 'crypto',
    timezone: str = 'UTC'
) -> bool:
    """Check if market is currently open.
    
    Args:
        market: Market type ('crypto', 'forex', 'stock')
        timezone: Timezone for market hours
        
    Returns:
        True if market is open, False otherwise
    """
    now = datetime.utcnow()
    
    if market.lower() == 'crypto':
        # Crypto markets are always open
        return True
    elif market.lower() == 'forex':
        # Forex is closed on weekends
        return now.weekday() < 5
    elif market.lower() == 'stock':
        # Simplified stock market hours (9:30 AM - 4:00 PM EST, weekdays)
        if now.weekday() >= 5:  # Weekend
            return False
        
        # Convert to EST (approximate)
        est_hour = (now.hour - 5) % 24
        return 9 <= est_hour < 16
    
    return True


def validate_price(price: Union[float, int, str]) -> bool:
    """Validate if price is a valid positive number.
    
    Args:
        price: Price value to validate
        
    Returns:
        True if valid price, False otherwise
    """
    try:
        price_float = float(price)
        return price_float > 0 and math.isfinite(price_float)
    except (TypeError, ValueError):
        return False


def validate_quantity(quantity: Union[float, int, str]) -> bool:
    """Validate if quantity is a valid positive number.
    
    Args:
        quantity: Quantity value to validate
        
    Returns:
        True if valid quantity, False otherwise
    """
    try:
        quantity_float = float(quantity)
        return quantity_float > 0 and math.isfinite(quantity_float)
    except (TypeError, ValueError):
        return False


def generate_order_id(prefix: str = 'ORDER') -> str:
    """Generate a unique order ID.
    
    Args:
        prefix: Prefix for the order ID
        
    Returns:
        Unique order ID string
    """
    timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S_%f')[:-3]
    return f"{prefix}_{timestamp}"


def chunks(lst: List, chunk_size: int) -> List[List]:
    """Split list into chunks of specified size.
    
    Args:
        lst: List to split
        chunk_size: Size of each chunk
        
    Returns:
        List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def flatten_dict(d: dict, parent_key: str = '', sep: str = '.') -> dict:
    """Flatten nested dictionary.
    
    Args:
        d: Dictionary to flatten
        parent_key: Parent key prefix
        sep: Separator for nested keys
        
    Returns:
        Flattened dictionary
    """
    items = []
    for k, v in d.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        else:
            items.append((new_key, v))
    return dict(items)