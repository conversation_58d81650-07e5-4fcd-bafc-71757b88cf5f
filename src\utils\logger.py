"""Logging Utilities for AI Trading Agent.

This module provides logging setup and configuration utilities.

Author: inkbytefo
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
from datetime import datetime


def setup_logger(
    name: Optional[str] = None,
    level: str = "INFO",
    log_file: Optional[str] = None,
    log_dir: str = "logs",
    max_file_size: int = 10 * 1024 * 1024,  # 10MB
    backup_count: int = 5,
    console_output: bool = True,
    file_output: bool = True,
    format_string: Optional[str] = None
) -> logging.Logger:
    """Setup and configure logger.
    
    Args:
        name: Logger name. If None, uses root logger.
        level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_file: Log file name. If None, auto-generates based on timestamp.
        log_dir: Directory for log files
        max_file_size: Maximum size of log file before rotation
        backup_count: Number of backup files to keep
        console_output: Whether to output to console
        file_output: Whether to output to file
        format_string: Custom format string
        
    Returns:
        Configured logger instance
    """
    # Create logger
    logger = logging.getLogger(name)
    
    # Clear existing handlers to avoid duplicates
    logger.handlers.clear()
    
    # Set level
    numeric_level = getattr(logging, level.upper(), logging.INFO)
    logger.setLevel(numeric_level)
    
    # Create formatter
    if format_string is None:
        format_string = (
            '%(asctime)s - %(name)s - %(levelname)s - '
            '[%(filename)s:%(lineno)d] - %(message)s'
        )
    
    formatter = logging.Formatter(
        format_string,
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # Console handler
    if console_output:
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(numeric_level)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
    
    # File handler
    if file_output:
        # Create log directory
        log_path = Path(log_dir)
        log_path.mkdir(parents=True, exist_ok=True)
        
        # Generate log file name if not provided
        if log_file is None:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            log_file = f"trading_agent_{timestamp}.log"
        
        log_file_path = log_path / log_file
        
        # Rotating file handler
        file_handler = logging.handlers.RotatingFileHandler(
            log_file_path,
            maxBytes=max_file_size,
            backupCount=backup_count,
            encoding='utf-8'
        )
        file_handler.setLevel(numeric_level)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
    
    # Prevent propagation to root logger
    logger.propagate = False
    
    logger.info(f"Logger '{name or 'root'}' configured with level {level}")
    
    return logger


def get_logger(name: str) -> logging.Logger:
    """Get a logger instance.
    
    Args:
        name: Logger name
        
    Returns:
        Logger instance
    """
    return logging.getLogger(name)


def configure_trading_logger(
    config: Optional[Dict[str, Any]] = None
) -> logging.Logger:
    """Configure logger specifically for trading operations.
    
    Args:
        config: Configuration dictionary
        
    Returns:
        Configured trading logger
    """
    if config is None:
        config = {}
    
    # Get logging configuration
    log_config = config.get('logging', {})
    general_config = config.get('general', {})
    
    # Determine log level
    log_level = (
        log_config.get('level') or 
        general_config.get('log_level') or 
        'INFO'
    )
    
    # Determine if debug mode
    debug_mode = general_config.get('debug', False)
    if debug_mode:
        log_level = 'DEBUG'
    
    # Setup logger
    logger = setup_logger(
        name='trading_agent',
        level=log_level,
        log_file=log_config.get('file'),
        log_dir=log_config.get('directory', 'logs'),
        max_file_size=log_config.get('max_file_size', 10 * 1024 * 1024),
        backup_count=log_config.get('backup_count', 5),
        console_output=log_config.get('console_output', True),
        file_output=log_config.get('file_output', True)
    )
    
    return logger


def setup_component_logger(
    component_name: str,
    parent_logger: Optional[logging.Logger] = None,
    level: Optional[str] = None
) -> logging.Logger:
    """Setup logger for a specific component.
    
    Args:
        component_name: Name of the component
        parent_logger: Parent logger to inherit from
        level: Specific log level for this component
        
    Returns:
        Component logger
    """
    if parent_logger:
        logger_name = f"{parent_logger.name}.{component_name}"
    else:
        logger_name = f"trading_agent.{component_name}"
    
    logger = logging.getLogger(logger_name)
    
    if level:
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        logger.setLevel(numeric_level)
    
    return logger


def log_trade_execution(
    logger: logging.Logger,
    action: str,
    symbol: str,
    quantity: float,
    price: float,
    order_id: Optional[str] = None,
    extra_info: Optional[Dict[str, Any]] = None
):
    """Log trade execution with structured format.
    
    Args:
        logger: Logger instance
        action: Trade action (BUY, SELL, etc.)
        symbol: Trading symbol
        quantity: Trade quantity
        price: Trade price
        order_id: Order ID if available
        extra_info: Additional information
    """
    log_msg = f"TRADE_EXECUTION | {action} | {symbol} | Qty: {quantity} | Price: {price}"
    
    if order_id:
        log_msg += f" | Order ID: {order_id}"
    
    if extra_info:
        extra_str = " | ".join([f"{k}: {v}" for k, v in extra_info.items()])
        log_msg += f" | {extra_str}"
    
    logger.info(log_msg)


def log_performance_metrics(
    logger: logging.Logger,
    metrics: Dict[str, Any],
    prefix: str = "PERFORMANCE"
):
    """Log performance metrics with structured format.
    
    Args:
        logger: Logger instance
        metrics: Performance metrics dictionary
        prefix: Log message prefix
    """
    metrics_str = " | ".join([f"{k}: {v}" for k, v in metrics.items()])
    logger.info(f"{prefix} | {metrics_str}")


def log_error_with_context(
    logger: logging.Logger,
    error: Exception,
    context: Dict[str, Any],
    action: str = "OPERATION"
):
    """Log error with additional context information.
    
    Args:
        logger: Logger instance
        error: Exception that occurred
        context: Context information
        action: Action being performed when error occurred
    """
    context_str = " | ".join([f"{k}: {v}" for k, v in context.items()])
    logger.error(
        f"ERROR | {action} | {type(error).__name__}: {str(error)} | Context: {context_str}",
        exc_info=True
    )


class TradingLoggerAdapter(logging.LoggerAdapter):
    """Logger adapter for trading operations with context."""
    
    def __init__(self, logger: logging.Logger, extra: Dict[str, Any]):
        super().__init__(logger, extra)
    
    def process(self, msg, kwargs):
        """Process log message with extra context."""
        extra_str = " | ".join([f"{k}: {v}" for k, v in self.extra.items()])
        return f"[{extra_str}] {msg}", kwargs


def create_trading_adapter(
    logger: logging.Logger,
    component: str,
    exchange: Optional[str] = None,
    strategy: Optional[str] = None
) -> TradingLoggerAdapter:
    """Create a trading logger adapter with context.
    
    Args:
        logger: Base logger
        component: Component name
        exchange: Exchange name
        strategy: Strategy name
        
    Returns:
        Logger adapter with context
    """
    extra = {'component': component}
    
    if exchange:
        extra['exchange'] = exchange
    
    if strategy:
        extra['strategy'] = strategy
    
    return TradingLoggerAdapter(logger, extra)