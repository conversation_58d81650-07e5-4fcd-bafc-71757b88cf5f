#!/usr/bin/env python3
"""
Performance Optimization Utilities - Performance improvements for AI trading agent

Author: inkbytefo
Description: Utilities and decorators for optimizing performance-critical operations
"""

import asyncio
import functools
import time
import logging
from typing import Dict, List, Any, Optional, Callable, Union
from datetime import datetime, timedelta
import numpy as np
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import threading
from collections import defaultdict, deque
import weakref
import gc
from dataclasses import dataclass
from enum import Enum


class CacheStrategy(Enum):
    """Cache strategy types."""
    LRU = "lru"
    TTL = "ttl"
    LFU = "lfu"
    FIFO = "fifo"


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    function_name: str
    execution_time: float
    memory_usage: float
    cpu_usage: float
    call_count: int
    average_time: float
    max_time: float
    min_time: float
    last_called: datetime


class PerformanceProfiler:
    """Performance profiler for monitoring function execution."""
    
    def __init__(self):
        self.metrics = defaultdict(lambda: {
            'total_time': 0.0,
            'call_count': 0,
            'max_time': 0.0,
            'min_time': float('inf'),
            'last_called': None
        })
        self.logger = logging.getLogger(__name__)
    
    def profile(self, func_name: str = None):
        """Decorator for profiling function performance."""
        def decorator(func):
            name = func_name or f"{func.__module__}.{func.__name__}"
            
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = func(*args, **kwargs)
                    return result
                finally:
                    execution_time = time.perf_counter() - start_time
                    self._update_metrics(name, execution_time)
            
            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                start_time = time.perf_counter()
                try:
                    result = await func(*args, **kwargs)
                    return result
                finally:
                    execution_time = time.perf_counter() - start_time
                    self._update_metrics(name, execution_time)
            
            return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
        return decorator
    
    def _update_metrics(self, func_name: str, execution_time: float):
        """Update performance metrics for a function."""
        metrics = self.metrics[func_name]
        metrics['total_time'] += execution_time
        metrics['call_count'] += 1
        metrics['max_time'] = max(metrics['max_time'], execution_time)
        metrics['min_time'] = min(metrics['min_time'], execution_time)
        metrics['last_called'] = datetime.now()
    
    def get_metrics(self, func_name: str = None) -> Union[PerformanceMetrics, Dict[str, PerformanceMetrics]]:
        """Get performance metrics for a function or all functions."""
        if func_name:
            if func_name in self.metrics:
                m = self.metrics[func_name]
                return PerformanceMetrics(
                    function_name=func_name,
                    execution_time=m['total_time'],
                    memory_usage=0.0,  # TODO: Implement memory tracking
                    cpu_usage=0.0,     # TODO: Implement CPU tracking
                    call_count=m['call_count'],
                    average_time=m['total_time'] / m['call_count'] if m['call_count'] > 0 else 0,
                    max_time=m['max_time'],
                    min_time=m['min_time'] if m['min_time'] != float('inf') else 0,
                    last_called=m['last_called']
                )
            return None
        
        return {name: self.get_metrics(name) for name in self.metrics.keys()}
    
    def reset_metrics(self, func_name: str = None):
        """Reset performance metrics."""
        if func_name:
            if func_name in self.metrics:
                del self.metrics[func_name]
        else:
            self.metrics.clear()


class SmartCache:
    """Smart caching system with multiple strategies."""
    
    def __init__(self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU, ttl: int = 300):
        self.max_size = max_size
        self.strategy = strategy
        self.ttl = ttl
        self.cache = {}
        self.access_times = {}
        self.access_counts = defaultdict(int)
        self.insertion_order = deque()
        self.lock = threading.RLock()
    
    def get(self, key: str) -> Any:
        """Get value from cache."""
        with self.lock:
            if key not in self.cache:
                return None
            
            # Check TTL expiration
            if self.strategy == CacheStrategy.TTL:
                if time.time() - self.access_times.get(key, 0) > self.ttl:
                    self._remove(key)
                    return None
            
            # Update access patterns
            self.access_times[key] = time.time()
            self.access_counts[key] += 1
            
            # Move to end for LRU
            if self.strategy == CacheStrategy.LRU:
                self.insertion_order.remove(key)
                self.insertion_order.append(key)
            
            return self.cache[key]
    
    def put(self, key: str, value: Any):
        """Put value in cache."""
        with self.lock:
            # If key exists, update it
            if key in self.cache:
                self.cache[key] = value
                self.access_times[key] = time.time()
                return
            
            # Check if cache is full
            if len(self.cache) >= self.max_size:
                self._evict()
            
            # Add new entry
            self.cache[key] = value
            self.access_times[key] = time.time()
            self.access_counts[key] = 1
            self.insertion_order.append(key)
    
    def _evict(self):
        """Evict entry based on strategy."""
        if not self.cache:
            return
        
        if self.strategy == CacheStrategy.LRU:
            key_to_remove = self.insertion_order[0]
        elif self.strategy == CacheStrategy.LFU:
            key_to_remove = min(self.access_counts.keys(), key=lambda k: self.access_counts[k])
        elif self.strategy == CacheStrategy.FIFO:
            key_to_remove = self.insertion_order[0]
        elif self.strategy == CacheStrategy.TTL:
            # Remove oldest by access time
            key_to_remove = min(self.access_times.keys(), key=lambda k: self.access_times[k])
        else:
            key_to_remove = next(iter(self.cache))
        
        self._remove(key_to_remove)
    
    def _remove(self, key: str):
        """Remove key from cache."""
        if key in self.cache:
            del self.cache[key]
            del self.access_times[key]
            del self.access_counts[key]
            if key in self.insertion_order:
                self.insertion_order.remove(key)
    
    def clear(self):
        """Clear all cache entries."""
        with self.lock:
            self.cache.clear()
            self.access_times.clear()
            self.access_counts.clear()
            self.insertion_order.clear()
    
    def size(self) -> int:
        """Get current cache size."""
        return len(self.cache)


def cached(max_size: int = 128, strategy: CacheStrategy = CacheStrategy.LRU, ttl: int = 300):
    """Decorator for caching function results."""
    def decorator(func):
        cache = SmartCache(max_size, strategy, ttl)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # Create cache key
            key = _create_cache_key(func.__name__, args, kwargs)
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = func(*args, **kwargs)
            cache.put(key, result)
            return result
        
        @functools.wraps(func)
        async def async_wrapper(*args, **kwargs):
            # Create cache key
            key = _create_cache_key(func.__name__, args, kwargs)
            
            # Try to get from cache
            result = cache.get(key)
            if result is not None:
                return result
            
            # Execute function and cache result
            result = await func(*args, **kwargs)
            cache.put(key, result)
            return result
        
        # Add cache management methods
        wrapper.cache_clear = cache.clear
        wrapper.cache_size = cache.size
        async_wrapper.cache_clear = cache.clear
        async_wrapper.cache_size = cache.size
        
        return async_wrapper if asyncio.iscoroutinefunction(func) else wrapper
    return decorator


def _create_cache_key(func_name: str, args: tuple, kwargs: dict) -> str:
    """Create a cache key from function arguments."""
    try:
        # Convert args and kwargs to a hashable representation
        key_parts = [func_name]
        
        # Add positional arguments
        for arg in args:
            if isinstance(arg, (str, int, float, bool, type(None))):
                key_parts.append(str(arg))
            elif isinstance(arg, (list, tuple)):
                key_parts.append(str(tuple(arg)))
            elif isinstance(arg, dict):
                key_parts.append(str(sorted(arg.items())))
            else:
                key_parts.append(str(type(arg).__name__))
        
        # Add keyword arguments
        for k, v in sorted(kwargs.items()):
            key_parts.append(f"{k}={v}")
        
        return "|".join(key_parts)
    except Exception:
        # Fallback to a simple hash
        return f"{func_name}_{hash((args, tuple(sorted(kwargs.items()))))}"


class BatchProcessor:
    """Batch processor for optimizing bulk operations."""
    
    def __init__(self, batch_size: int = 100, max_wait_time: float = 1.0):
        self.batch_size = batch_size
        self.max_wait_time = max_wait_time
        self.pending_items = []
        self.pending_futures = []
        self.last_batch_time = time.time()
        self.lock = asyncio.Lock()
        self.processor_task = None
    
    async def add_item(self, item: Any, processor_func: Callable) -> Any:
        """Add item to batch for processing."""
        future = asyncio.Future()
        
        async with self.lock:
            self.pending_items.append((item, processor_func))
            self.pending_futures.append(future)
            
            # Start processor if not running
            if self.processor_task is None or self.processor_task.done():
                self.processor_task = asyncio.create_task(self._process_batch())
            
            # Process immediately if batch is full
            if len(self.pending_items) >= self.batch_size:
                await self._flush_batch()
        
        return await future
    
    async def _process_batch(self):
        """Process batch periodically."""
        while True:
            await asyncio.sleep(self.max_wait_time)
            
            async with self.lock:
                if self.pending_items and time.time() - self.last_batch_time >= self.max_wait_time:
                    await self._flush_batch()
                elif not self.pending_items:
                    break
    
    async def _flush_batch(self):
        """Flush current batch."""
        if not self.pending_items:
            return
        
        items = self.pending_items.copy()
        futures = self.pending_futures.copy()
        
        self.pending_items.clear()
        self.pending_futures.clear()
        self.last_batch_time = time.time()
        
        # Group items by processor function
        grouped_items = defaultdict(list)
        item_to_future = {}
        
        for i, (item, processor_func) in enumerate(items):
            grouped_items[processor_func].append(item)
            item_to_future[id(item)] = futures[i]
        
        # Process each group
        for processor_func, group_items in grouped_items.items():
            try:
                if asyncio.iscoroutinefunction(processor_func):
                    results = await processor_func(group_items)
                else:
                    results = processor_func(group_items)
                
                # Set results for futures
                for item, result in zip(group_items, results):
                    future = item_to_future.get(id(item))
                    if future and not future.done():
                        future.set_result(result)
                        
            except Exception as e:
                # Set exception for all futures in this group
                for item in group_items:
                    future = item_to_future.get(id(item))
                    if future and not future.done():
                        future.set_exception(e)


class MemoryOptimizer:
    """Memory optimization utilities."""
    
    @staticmethod
    def optimize_dataframe(df: pd.DataFrame) -> pd.DataFrame:
        """Optimize pandas DataFrame memory usage."""
        optimized_df = df.copy()
        
        for col in optimized_df.columns:
            col_type = optimized_df[col].dtype
            
            if col_type != 'object':
                c_min = optimized_df[col].min()
                c_max = optimized_df[col].max()
                
                if str(col_type)[:3] == 'int':
                    if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                        optimized_df[col] = optimized_df[col].astype(np.int8)
                    elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                        optimized_df[col] = optimized_df[col].astype(np.int16)
                    elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                        optimized_df[col] = optimized_df[col].astype(np.int32)
                    elif c_min > np.iinfo(np.int64).min and c_max < np.iinfo(np.int64).max:
                        optimized_df[col] = optimized_df[col].astype(np.int64)
                        
                elif str(col_type)[:5] == 'float':
                    if c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                        optimized_df[col] = optimized_df[col].astype(np.float32)
                    else:
                        optimized_df[col] = optimized_df[col].astype(np.float64)
        
        return optimized_df
    
    @staticmethod
    def force_garbage_collection():
        """Force garbage collection."""
        gc.collect()
    
    @staticmethod
    def get_memory_usage() -> Dict[str, float]:
        """Get current memory usage statistics."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        
        return {
            'rss': memory_info.rss / 1024 / 1024,  # MB
            'vms': memory_info.vms / 1024 / 1024,  # MB
            'percent': process.memory_percent()
        }


class AsyncPoolExecutor:
    """Async pool executor for CPU-intensive tasks."""
    
    def __init__(self, max_workers: int = None, executor_type: str = 'thread'):
        self.max_workers = max_workers
        self.executor_type = executor_type
        
        if executor_type == 'thread':
            self.executor = ThreadPoolExecutor(max_workers=max_workers)
        elif executor_type == 'process':
            self.executor = ProcessPoolExecutor(max_workers=max_workers)
        else:
            raise ValueError("executor_type must be 'thread' or 'process'")
    
    async def submit(self, func: Callable, *args, **kwargs) -> Any:
        """Submit function for async execution."""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(self.executor, functools.partial(func, **kwargs), *args)
    
    def shutdown(self, wait: bool = True):
        """Shutdown the executor."""
        self.executor.shutdown(wait=wait)


# Global instances
default_profiler = PerformanceProfiler()
default_cache = SmartCache()
default_batch_processor = BatchProcessor()
default_memory_optimizer = MemoryOptimizer()


# Convenience decorators
profile = default_profiler.profile
memoize = functools.partial(cached, max_size=256, strategy=CacheStrategy.LRU, ttl=600)


def optimize_numpy_operations():
    """Optimize NumPy operations for better performance."""
    # Set optimal number of threads for NumPy operations
    import os
    os.environ['OMP_NUM_THREADS'] = str(min(4, os.cpu_count()))
    os.environ['OPENBLAS_NUM_THREADS'] = str(min(4, os.cpu_count()))
    os.environ['MKL_NUM_THREADS'] = str(min(4, os.cpu_count()))
    os.environ['VECLIB_MAXIMUM_THREADS'] = str(min(4, os.cpu_count()))
    os.environ['NUMEXPR_NUM_THREADS'] = str(min(4, os.cpu_count()))


def optimize_pandas_operations():
    """Optimize Pandas operations for better performance."""
    import pandas as pd
    
    # Set optimal options for pandas
    pd.set_option('compute.use_bottleneck', True)
    pd.set_option('compute.use_numexpr', True)
    pd.set_option('mode.chained_assignment', None)


def initialize_performance_optimizations():
    """Initialize all performance optimizations."""
    optimize_numpy_operations()
    optimize_pandas_operations()
    default_memory_optimizer.force_garbage_collection()