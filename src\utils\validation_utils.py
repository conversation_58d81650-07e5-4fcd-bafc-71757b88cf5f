#!/usr/bin/env python3
"""
Validation Utilities for AI Trading Agent

Author: inkbytefo
Description: Centralized validation functions to reduce code duplication
"""

import re
import json
from typing import Union, List, Dict, Any, Optional
from datetime import datetime
from decimal import Decimal, InvalidOperation


def validate_symbol(symbol: str) -> bool:
    """Validate trading symbol format.
    
    Args:
        symbol: Trading symbol string (e.g., 'BTC/USDT', 'BTC-USDT', 'BTCUSDT')
        
    Returns:
        True if valid format, False otherwise
    """
    if not symbol or not isinstance(symbol, str):
        return False
    
    # Common trading pair patterns
    patterns = [
        r'^[A-Z]{2,10}[/\-][A-Z]{2,10}$',  # BTC/USDT, BTC-USDT
        r'^[A-Z]{6,20}$'                   # BTCUSDT
    ]
    
    symbol_upper = symbol.upper()
    for pattern in patterns:
        if re.match(pattern, symbol_upper):
            return True
    
    # Fallback: basic alphanumeric check
    return len(symbol) >= 3 and symbol.replace('/', '').replace('-', '').isalnum()


def validate_trading_pair(pair: str) -> bool:
    """Validate trading pair format (alias for validate_symbol).
    
    Args:
        pair: Trading pair string
        
    Returns:
        True if valid format, False otherwise
    """
    return validate_symbol(pair)


def validate_price(price: Union[int, float, str, Decimal]) -> bool:
    """Validate price value.
    
    Args:
        price: Price value to validate
        
    Returns:
        True if valid price (positive number), False otherwise
    """
    try:
        price_float = float(price)
        return price_float > 0 and not (price_float != price_float)  # Check for NaN
    except (ValueError, TypeError, OverflowError):
        return False


def validate_quantity(quantity: Union[int, float, str, Decimal]) -> bool:
    """Validate quantity value.
    
    Args:
        quantity: Quantity value to validate
        
    Returns:
        True if valid quantity (positive number), False otherwise
    """
    try:
        quantity_float = float(quantity)
        return quantity_float > 0 and not (quantity_float != quantity_float)  # Check for NaN
    except (ValueError, TypeError, OverflowError):
        return False


def validate_percentage(percentage: Union[int, float, str]) -> bool:
    """Validate percentage value (0-100).
    
    Args:
        percentage: Percentage value to validate
        
    Returns:
        True if valid percentage (0-100), False otherwise
    """
    try:
        pct_float = float(percentage)
        return 0 <= pct_float <= 100
    except (ValueError, TypeError, OverflowError):
        return False


def validate_email(email: str) -> bool:
    """Validate email format.
    
    Args:
        email: Email string to validate
        
    Returns:
        True if valid email format, False otherwise
    """
    if not email or not isinstance(email, str):
        return False
    
    # Basic email validation
    email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(email_pattern, email) is not None


def validate_timestamp(timestamp: Union[str, int, float, datetime]) -> bool:
    """Validate timestamp format.
    
    Args:
        timestamp: Timestamp to validate
        
    Returns:
        True if valid timestamp, False otherwise
    """
    try:
        if isinstance(timestamp, datetime):
            return True
        elif isinstance(timestamp, (int, float)):
            # Unix timestamp validation
            return 0 < timestamp < 2147483647  # Valid Unix timestamp range
        elif isinstance(timestamp, str):
            # Try to parse ISO format
            datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return True
        return False
    except (ValueError, TypeError, OverflowError):
        return False


def validate_json(json_string: str) -> bool:
    """Validate JSON string format.
    
    Args:
        json_string: JSON string to validate
        
    Returns:
        True if valid JSON, False otherwise
    """
    try:
        json.loads(json_string)
        return True
    except (json.JSONDecodeError, TypeError):
        return False


def validate_range(value: Union[int, float], min_val: Optional[Union[int, float]] = None, 
                  max_val: Optional[Union[int, float]] = None) -> bool:
    """Validate if value is within specified range.
    
    Args:
        value: Value to validate
        min_val: Minimum allowed value (inclusive)
        max_val: Maximum allowed value (inclusive)
        
    Returns:
        True if value is within range, False otherwise
    """
    try:
        val_float = float(value)
        if min_val is not None and val_float < min_val:
            return False
        if max_val is not None and val_float > max_val:
            return False
        return True
    except (ValueError, TypeError, OverflowError):
        return False


def validate_string_length(text: str, min_length: int = 0, max_length: Optional[int] = None) -> bool:
    """Validate string length.
    
    Args:
        text: String to validate
        min_length: Minimum required length
        max_length: Maximum allowed length
        
    Returns:
        True if string length is valid, False otherwise
    """
    if not isinstance(text, str):
        return False
    
    length = len(text)
    if length < min_length:
        return False
    if max_length is not None and length > max_length:
        return False
    return True


def sanitize_input(input_string: str, remove_html: bool = True, remove_sql: bool = True) -> str:
    """Sanitize input string by removing dangerous characters.
    
    Args:
        input_string: String to sanitize
        remove_html: Whether to remove HTML-like characters
        remove_sql: Whether to remove SQL injection characters
        
    Returns:
        Sanitized string
    """
    if not isinstance(input_string, str):
        return str(input_string)
    
    sanitized = input_string
    
    if remove_html:
        # Remove HTML-like characters
        html_chars = ['<', '>', '&', '"', "'"]
        for char in html_chars:
            sanitized = sanitized.replace(char, '')
    
    if remove_sql:
        # Remove common SQL injection characters
        sql_chars = [';', '--', '/*', '*/', 'xp_', 'sp_']
        for char in sql_chars:
            sanitized = sanitized.replace(char, '')
    
    return sanitized.strip()


def validate_market_data(data: Dict[str, Any]) -> bool:
    """Validate market data structure.
    
    Args:
        data: Market data dictionary to validate
        
    Returns:
        True if valid market data, False otherwise
    """
    required_fields = ['timestamp', 'open', 'high', 'low', 'close', 'volume']
    
    if not isinstance(data, dict):
        return False
    
    # Check required fields
    for field in required_fields:
        if field not in data:
            return False
    
    # Validate timestamp
    if not validate_timestamp(data['timestamp']):
        return False
    
    # Validate OHLCV values
    ohlcv_fields = ['open', 'high', 'low', 'close']
    for field in ohlcv_fields:
        if not validate_price(data[field]):
            return False
    
    # Validate volume
    if not validate_quantity(data['volume']):
        return False
    
    # Validate OHLC relationships
    try:
        o, h, l, c = float(data['open']), float(data['high']), float(data['low']), float(data['close'])
        if not (l <= o <= h and l <= c <= h):
            return False
    except (ValueError, TypeError):
        return False
    
    return True


class ValidationManager:
    """Centralized validation manager for complex validation rules."""
    
    def __init__(self):
        self.validation_rules: Dict[str, List[Dict[str, Any]]] = {}
    
    def add_rule(self, field_name: str, rule_type: str, **kwargs):
        """Add a validation rule for a field.
        
        Args:
            field_name: Name of the field to validate
            rule_type: Type of validation rule
            **kwargs: Additional rule parameters
        """
        if field_name not in self.validation_rules:
            self.validation_rules[field_name] = []
        
        rule = {'type': rule_type, **kwargs}
        self.validation_rules[field_name].append(rule)
    
    def validate_data(self, data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate data against all rules.
        
        Args:
            data: Data dictionary to validate
            
        Returns:
            Tuple of (is_valid, error_list)
        """
        errors = []
        
        for field_name, rules in self.validation_rules.items():
            if field_name not in data:
                errors.append(f"Missing required field: {field_name}")
                continue
            
            value = data[field_name]
            
            for rule in rules:
                error = self._apply_rule(field_name, value, rule)
                if error:
                    errors.append(error)
        
        return len(errors) == 0, errors
    
    def _apply_rule(self, field_name: str, value: Any, rule: Dict[str, Any]) -> Optional[str]:
        """Apply a single validation rule.
        
        Args:
            field_name: Name of the field
            value: Value to validate
            rule: Validation rule dictionary
            
        Returns:
            Error message if validation fails, None if passes
        """
        rule_type = rule['type']
        
        try:
            if rule_type == 'required':
                if value is None or (isinstance(value, str) and not value.strip()):
                    return f"{field_name} is required"
            
            elif rule_type == 'type':
                expected_type = rule.get('expected_type')
                if not isinstance(value, expected_type):
                    return f"{field_name} must be of type {expected_type.__name__}"
            
            elif rule_type == 'range':
                min_val = rule.get('min')
                max_val = rule.get('max')
                if not validate_range(value, min_val, max_val):
                    return f"{field_name} must be between {min_val} and {max_val}"
            
            elif rule_type == 'length':
                min_length = rule.get('min_length', 0)
                max_length = rule.get('max_length')
                if not validate_string_length(str(value), min_length, max_length):
                    return f"{field_name} length must be between {min_length} and {max_length}"
            
            elif rule_type == 'email':
                if not validate_email(str(value)):
                    return f"{field_name} must be a valid email address"
            
            elif rule_type == 'symbol':
                if not validate_symbol(str(value)):
                    return f"{field_name} must be a valid trading symbol"
            
            elif rule_type == 'price':
                if not validate_price(value):
                    return f"{field_name} must be a valid positive price"
            
            elif rule_type == 'quantity':
                if not validate_quantity(value):
                    return f"{field_name} must be a valid positive quantity"
        
        except Exception as e:
            return f"Validation error for {field_name}: {str(e)}"
        
        return None