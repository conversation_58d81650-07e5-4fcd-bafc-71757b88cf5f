#!/usr/bin/env python3
"""
API Key Validation Test

Bu script API keylerinin geçerliliğini test eder.
"""

import ccxt
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_api_keys():
    """Test API keys with different configurations."""
    
    api_key = os.getenv('BINANCE_API_KEY')
    api_secret = os.getenv('BINANCE_API_SECRET')
    
    print(f"🔑 Testing API Keys:")
    print(f"   API Key: {api_key[:8]}...{api_key[-8:] if len(api_key) > 16 else api_key}")
    print(f"   Secret: {api_secret[:8]}...{api_secret[-8:] if len(api_secret) > 16 else api_secret}")
    print()
    
    # Test 1: Mainnet (live) API
    print("🌐 Testing with Binance Mainnet...")
    try:
        exchange_main = ccxt.binance({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': False,  # Mainnet
            'enableRateLimit': True,
        })
        
        # Test authentication
        balance = exchange_main.fetch_balance()
        print("✅ Mainnet API keys are VALID!")
        print(f"   Account has {len([k for k, v in balance['total'].items() if v > 0])} currencies with balance")
        
        # Show some balances
        for currency, amount in balance['total'].items():
            if amount > 0:
                print(f"   {currency}: {amount}")
        
        return True, "mainnet"
        
    except Exception as e:
        print(f"❌ Mainnet test failed: {e}")
    
    # Test 2: Testnet API
    print("\n🧪 Testing with Binance Testnet...")
    try:
        exchange_test = ccxt.binance({
            'apiKey': api_key,
            'secret': api_secret,
            'sandbox': True,  # Testnet
            'enableRateLimit': True,
        })
        
        # Set testnet URLs
        exchange_test.urls['api']['public'] = 'https://testnet.binance.vision/api'
        exchange_test.urls['api']['private'] = 'https://testnet.binance.vision/api'
        
        # Test authentication
        balance = exchange_test.fetch_balance()
        print("✅ Testnet API keys are VALID!")
        print(f"   Account has {len([k for k, v in balance['total'].items() if v > 0])} currencies with balance")
        
        # Show some balances
        for currency, amount in balance['total'].items():
            if amount > 0:
                print(f"   {currency}: {amount}")
        
        return True, "testnet"
        
    except Exception as e:
        print(f"❌ Testnet test failed: {e}")
    
    # Test 3: Try without sandbox flag (auto-detect)
    print("\n🔄 Testing with auto-detection...")
    try:
        exchange_auto = ccxt.binance({
            'apiKey': api_key,
            'secret': api_secret,
            'enableRateLimit': True,
        })
        
        # Test authentication
        balance = exchange_auto.fetch_balance()
        print("✅ Auto-detection successful!")
        print(f"   Account has {len([k for k, v in balance['total'].items() if v > 0])} currencies with balance")
        
        return True, "auto"
        
    except Exception as e:
        print(f"❌ Auto-detection test failed: {e}")
    
    return False, None

def test_public_endpoints():
    """Test public endpoints (no authentication needed)."""
    print("\n📊 Testing public endpoints...")
    
    try:
        # Test mainnet public endpoint
        exchange = ccxt.binance({'enableRateLimit': True})
        ticker = exchange.fetch_ticker('BTC/USDT')
        print(f"✅ Mainnet public API working - BTC/USDT: ${ticker['last']:,.2f}")
        
        # Test testnet public endpoint
        exchange_test = ccxt.binance({
            'sandbox': True,
            'enableRateLimit': True
        })
        exchange_test.urls['api']['public'] = 'https://testnet.binance.vision/api'
        
        try:
            ticker_test = exchange_test.fetch_ticker('BTC/USDT')
            print(f"✅ Testnet public API working - BTC/USDT: ${ticker_test['last']:,.2f}")
        except Exception as e:
            print(f"❌ Testnet public API failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Public endpoint test failed: {e}")
        return False

if __name__ == "__main__":
    print("🚀 API Key Validation Test")
    print("=" * 50)
    
    # Test public endpoints first
    public_ok = test_public_endpoints()
    
    # Test API keys
    api_valid, api_type = test_api_keys()
    
    print("\n" + "=" * 50)
    print("📋 SUMMARY")
    print("=" * 50)
    
    if public_ok:
        print("✅ Public API endpoints are working")
    else:
        print("❌ Public API endpoints have issues")
    
    if api_valid:
        print(f"✅ API keys are valid for {api_type}")
        
        if api_type == "mainnet":
            print("⚠️ WARNING: These are LIVE/MAINNET API keys!")
            print("   Be very careful with real money!")
            print("   Consider using testnet keys for development")
        elif api_type == "testnet":
            print("✅ These are testnet keys - safe for development")
        
        print("\n📋 Next Steps:")
        print("1. ✅ API keys are working")
        print("2. 🔄 Update trading system configuration")
        print("3. 🚀 Start trading system")
        
    else:
        print("❌ API keys are not valid")
        print("\n📋 Next Steps:")
        print("1. ❌ Check API key format")
        print("2. 🔧 Verify API keys in Binance account")
        print("3. 🔄 Generate new API keys if needed")
        print("4. ⚠️ Make sure keys have proper permissions")