#!/usr/bin/env python3
"""
Hummingbot Connection Test Script

This script tests the connection between the AI Trading Agent and Hummingbot.
It verifies API connectivity, authentication, and basic functionality.

Author: inkbytefo
"""

import asyncio
import logging
import sys
from datetime import datetime
from typing import Dict, Any, Optional

# Add src to path
sys.path.append('src')

from src.config.hummingbot_config import HummingbotConfigManager, HummingbotAPIConfig
from src.execution.hummingbot_service import HummingbotAPIClient, HummingbotConfig, HummingbotAPIError
from src.utils.logger import setup_logger, get_logger


class HummingbotConnectionTester:
    """Test Hummingbot connection and functionality."""
    
    def __init__(self):
        """Initialize the connection tester."""
        self.logger = get_logger(__name__)
        self.config_manager = HummingbotConfigManager()
        self.api_client: Optional[HummingbotAPIClient] = None
        self.test_results = {}
        
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all connection tests."""
        self.logger.info("🚀 Starting Hummingbot Connection Tests")
        self.logger.info("=" * 50)
        
        tests = [
            ("Configuration Test", self.test_configuration),
            ("API Connection Test", self.test_api_connection),
            ("Authentication Test", self.test_authentication),
            ("Health Check Test", self.test_health_check),
            ("Gateway Connection Test", self.test_gateway_connection),
            ("Exchange Status Test", self.test_exchange_status),
            ("Trading Pairs Test", self.test_trading_pairs),
            ("Balance Retrieval Test", self.test_balance_retrieval)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                if result:
                    self.logger.info(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    self.logger.warning(f"❌ {test_name}: FAILED")
                self.test_results[test_name] = result
            except Exception as e:
                self.logger.error(f"💥 {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
        
        # Summary
        success_rate = (passed_tests / total_tests) * 100
        self.logger.info(f"\n📊 Test Summary:")
        self.logger.info(f"Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 80:
            self.logger.info("🎉 Hummingbot connection is working well!")
        elif success_rate >= 50:
            self.logger.warning("⚠️ Hummingbot connection has some issues")
        else:
            self.logger.error("🚨 Hummingbot connection has major problems")
            
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results,
            'timestamp': datetime.now().isoformat()
        }
    
    async def test_configuration(self) -> bool:
        """Test configuration loading."""
        try:
            # Test API config
            api_config = self.config_manager.api_config
            self.logger.info(f"API Config: {api_config.host}:{api_config.port}")
            
            # Test Gateway config
            gateway_config = self.config_manager.gateway_config
            self.logger.info(f"Gateway Config: {gateway_config.host}:{gateway_config.port}")
            
            # Test exchanges config
            exchanges = self.config_manager.exchanges
            self.logger.info(f"Configured exchanges: {list(exchanges.keys())}")
            
            return True
        except Exception as e:
            self.logger.error(f"Configuration test failed: {e}")
            return False
    
    async def test_api_connection(self) -> bool:
        """Test API connection."""
        try:
            # Create API client
            api_config = self.config_manager.api_config
            hummingbot_config = HummingbotConfig(
                api_url=api_config.base_url,
                username=api_config.username,
                password=api_config.password,
                timeout=api_config.timeout,
                enable_ssl_verify=api_config.enable_ssl
            )
            
            self.api_client = HummingbotAPIClient(hummingbot_config)
            
            # Test connection
            connected = await self.api_client.connect()
            if connected:
                self.logger.info("Successfully connected to Hummingbot API")
                return True
            else:
                self.logger.warning("Failed to connect to Hummingbot API")
                return False
                
        except Exception as e:
            self.logger.error(f"API connection test failed: {e}")
            return False
    
    async def test_authentication(self) -> bool:
        """Test API authentication."""
        try:
            if not self.api_client or not self.api_client.is_connected:
                self.logger.warning("API client not connected")
                return False
            
            # Try to make an authenticated request
            response = await self.api_client._make_request("GET", "/bot-orchestration/status")
            if response:
                self.logger.info("Authentication successful")
                return True
            else:
                self.logger.warning("Authentication failed")
                return False
                
        except HummingbotAPIError as e:
            if e.status_code == 401:
                self.logger.error("Authentication failed - Invalid credentials")
            else:
                self.logger.error(f"Authentication test failed: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Authentication test failed: {e}")
            return False
    
    async def test_health_check(self) -> bool:
        """Test health check endpoint."""
        try:
            if not self.api_client:
                return False
            
            health_ok = await self.api_client.health_check()
            if health_ok:
                self.logger.info("Health check passed")
                return True
            else:
                self.logger.warning("Health check failed")
                return False
                
        except Exception as e:
            self.logger.error(f"Health check test failed: {e}")
            return False
    
    async def test_gateway_connection(self) -> bool:
        """Test Gateway connection."""
        try:
            # This is a placeholder - actual Gateway testing would require
            # Gateway-specific API calls
            gateway_config = self.config_manager.gateway_config
            self.logger.info(f"Gateway configured at: {gateway_config.base_url}")
            
            # For now, just check if Gateway config is valid
            if gateway_config.host and gateway_config.port:
                self.logger.info("Gateway configuration is valid")
                return True
            else:
                self.logger.warning("Gateway configuration is invalid")
                return False
                
        except Exception as e:
            self.logger.error(f"Gateway connection test failed: {e}")
            return False
    
    async def test_exchange_status(self) -> bool:
        """Test exchange status."""
        try:
            if not self.api_client:
                return False
            
            # Try to get available connectors
            try:
                response = await self.api_client._make_request("GET", "/connectors/")
                if response:
                    self.logger.info(f"Available connectors retrieved: {len(response)} connectors")
                    return True
                else:
                    self.logger.warning("No connector data received")
                    return False
            except HummingbotAPIError as e:
                if e.status_code == 404:
                    self.logger.info("Connectors endpoint not available (normal for some setups)")
                    return True
                else:
                    raise e
                    
        except Exception as e:
            self.logger.warning(f"Exchange status test failed: {e}")
            return False
    
    async def test_trading_pairs(self) -> bool:
        """Test trading pairs retrieval."""
        try:
            if not self.api_client:
                return False
            
            # Test with a common exchange
            exchanges = list(self.config_manager.exchanges.keys())
            if not exchanges:
                self.logger.warning("No exchanges configured")
                return False
            
            exchange = exchanges[0]
            trading_pairs = await self.api_client.get_trading_pairs(exchange)
            
            if trading_pairs:
                self.logger.info(f"Retrieved {len(trading_pairs)} trading pairs for {exchange}")
                return True
            else:
                self.logger.info(f"No trading pairs found for {exchange} (normal for some setups)")
                return True  # Consider this as success since API is responding
                
        except Exception as e:
            self.logger.warning(f"Trading pairs test failed: {e}")
            return False
    
    async def test_balance_retrieval(self) -> bool:
        """Test balance retrieval."""
        try:
            if not self.api_client:
                return False
            
            # Try to get portfolio state (balances)
            try:
                # Portfolio state endpoint requires POST with filter data
                filter_data = {"accounts": [], "connectors": []}
                response = await self.api_client._make_request("POST", "/portfolio/state", data=filter_data)
                if response:
                    self.logger.info("Portfolio state (balance) data retrieved successfully")
                    return True
                else:
                    self.logger.info("No portfolio state data received (normal for some setups)")
                    return True  # Consider this as success since API is responding
            except HummingbotAPIError as e:
                if e.status_code == 404:
                    self.logger.info("Portfolio endpoint not available (normal for some setups)")
                    return True
                else:
                    self.logger.warning(f"Balance retrieval failed: {e}")
                    return False
                    
        except Exception as e:
            self.logger.warning(f"Balance retrieval test failed: {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.api_client:
            await self.api_client.disconnect()


async def main():
    """Main test function."""
    # Setup logging
    setup_logger(level="INFO")
    logger = get_logger(__name__)
    
    tester = HummingbotConnectionTester()
    
    try:
        results = await tester.run_all_tests()
        
        # Print detailed results
        logger.info("\n" + "=" * 50)
        logger.info("📋 DETAILED TEST RESULTS")
        logger.info("=" * 50)
        
        for test_name, result in results['test_results'].items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name}: {status}")
        
        return results['success_rate'] >= 50
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return False
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)