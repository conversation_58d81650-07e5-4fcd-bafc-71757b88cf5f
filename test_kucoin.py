#!/usr/bin/env python3
"""
KuCoin API Test Script

Bu script KuCoin API bağlantısını test eder ve trading işlemlerini simüle eder.

Author: inkbytefo
"""

import asyncio
import logging
import os
import sys
from datetime import datetime
from typing import Dict, Any, Optional
import ccxt
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Add src to path
sys.path.append('src')

from src.utils.logger import setup_logger


class KuCoinTester:
    """KuCoin API bağlantı ve trading test sınıfı."""
    
    def __init__(self):
        """Initialize the KuCoin tester."""
        self.logger = logging.getLogger(__name__)
        self.exchange = None
        self.test_results = {}
        
        # KuCoin API credentials
        self.api_key = os.getenv('KUCOIN_API_KEY')
        self.api_secret = os.getenv('KUCOIN_API_SECRET')
        self.passphrase = os.getenv('KUCOIN_PASSPHRASE')
        
        if not self.api_key or not self.api_secret or not self.passphrase:
            self.logger.error("❌ KuCoin API credentials not found in .env file")
            raise ValueError("Missing KuCoin API credentials")
    
    def setup_exchange(self):
        """KuCoin exchange'i kurulum."""
        try:
            self.exchange = ccxt.kucoin({
                'apiKey': self.api_key,
                'secret': self.api_secret,
                'password': self.passphrase,  # KuCoin requires passphrase
                'sandbox': False,  # Use live API (safe with 0 balance)
                'enableRateLimit': True,
                'options': {
                    'defaultType': 'spot',  # spot trading
                }
            })
            
            self.logger.info("✅ KuCoin exchange configured")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to setup KuCoin: {e}")
            return False
    
    async def test_connection(self) -> bool:
        """Test basic connection to KuCoin."""
        try:
            self.logger.info("🔗 Testing KuCoin connection...")
            
            # Test connection by fetching server time
            server_time = self.exchange.fetch_time()
            current_time = datetime.now().timestamp() * 1000
            time_diff = abs(server_time - current_time)
            
            self.logger.info(f"✅ Server time: {datetime.fromtimestamp(server_time/1000)}")
            self.logger.info(f"✅ Time difference: {time_diff:.0f}ms")
            
            if time_diff > 5000:  # 5 seconds
                self.logger.warning("⚠️ Large time difference detected")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Connection test failed: {e}")
            return False
    
    async def test_authentication(self) -> bool:
        """Test API authentication."""
        try:
            self.logger.info("🔐 Testing API authentication...")
            
            # Fetch account balance (requires authentication)
            balance = self.exchange.fetch_balance()
            
            self.logger.info("✅ Authentication successful")
            self.logger.info(f"✅ Account balance retrieved")
            
            # Show all balances (including zero)
            total_currencies = len(balance['info']['data'])
            non_zero_balances = {k: v for k, v in balance['total'].items() if v > 0}
            
            self.logger.info(f"   Total currencies: {total_currencies}")
            self.logger.info(f"   Non-zero balances: {len(non_zero_balances)}")
            
            if non_zero_balances:
                self.logger.info("   Balances:")
                for currency, amount in non_zero_balances.items():
                    self.logger.info(f"     {currency}: {amount}")
            else:
                self.logger.info("   All balances are zero (safe for testing)")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Authentication test failed: {e}")
            return False
    
    async def test_market_data(self) -> bool:
        """Test market data retrieval."""
        try:
            self.logger.info("📊 Testing market data retrieval...")
            
            # Test symbols
            test_symbols = ['BTC/USDT', 'ETH/USDT', 'KCS/USDT']
            
            success_count = 0
            for symbol in test_symbols:
                try:
                    # Fetch ticker
                    ticker = self.exchange.fetch_ticker(symbol)
                    
                    self.logger.info(f"✅ {symbol}:")
                    self.logger.info(f"   Price: ${ticker['last']:,.2f}")
                    self.logger.info(f"   24h Change: {ticker['percentage']:.2f}%")
                    self.logger.info(f"   Volume: {ticker['baseVolume']:,.2f}")
                    success_count += 1
                    
                except Exception as e:
                    self.logger.error(f"❌ Failed to fetch {symbol}: {e}")
            
            return success_count > 0
            
        except Exception as e:
            self.logger.error(f"❌ Market data test failed: {e}")
            return False
    
    async def test_order_book(self) -> bool:
        """Test order book retrieval."""
        try:
            self.logger.info("📖 Testing order book retrieval...")
            
            symbol = 'BTC/USDT'
            order_book = self.exchange.fetch_order_book(symbol, limit=20)
            
            self.logger.info(f"✅ Order book for {symbol}:")
            self.logger.info("   Bids (Buy orders):")
            for i, (price, amount) in enumerate(order_book['bids'][:3]):
                self.logger.info(f"     {i+1}. ${price:,.2f} - {amount:.6f} BTC")
            
            self.logger.info("   Asks (Sell orders):")
            for i, (price, amount) in enumerate(order_book['asks'][:3]):
                self.logger.info(f"     {i+1}. ${price:,.2f} - {amount:.6f} BTC")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Order book test failed: {e}")
            return False
    
    async def test_trading_rules(self) -> bool:
        """Test trading rules and limits."""
        try:
            self.logger.info("📋 Testing trading rules...")
            
            # Load markets
            markets = self.exchange.load_markets()
            
            test_symbols = ['BTC/USDT', 'ETH/USDT', 'KCS/USDT']
            
            for symbol in test_symbols:
                if symbol in markets:
                    market = markets[symbol]
                    
                    self.logger.info(f"✅ Trading rules for {symbol}:")
                    self.logger.info(f"   Min order size: {market['limits']['amount']['min']}")
                    self.logger.info(f"   Max order size: {market['limits']['amount']['max']}")
                    self.logger.info(f"   Min price: {market['limits']['price']['min']}")
                    self.logger.info(f"   Min cost: {market['limits']['cost']['min']}")
                    self.logger.info(f"   Price precision: {market['precision']['price']}")
                    self.logger.info(f"   Amount precision: {market['precision']['amount']}")
                else:
                    self.logger.warning(f"⚠️ {symbol} not found in markets")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Trading rules test failed: {e}")
            return False
    
    async def test_account_info(self) -> bool:
        """Test account information retrieval."""
        try:
            self.logger.info("👤 Testing account information...")
            
            # Get account info
            balance = self.exchange.fetch_balance()
            
            # Check trading permissions
            self.logger.info("✅ Account information:")
            self.logger.info(f"   Account type: {balance['info'].get('type', 'Unknown')}")
            
            # Check if we can access trading endpoints
            try:
                # Try to get open orders (should work even with empty result)
                open_orders = self.exchange.fetch_open_orders()
                self.logger.info(f"   Open orders: {len(open_orders)}")
                
                # Try to get order history (limited)
                try:
                    order_history = self.exchange.fetch_orders(limit=1)
                    self.logger.info(f"   Order history accessible: Yes")
                except:
                    self.logger.info(f"   Order history accessible: No (normal for new account)")
                
            except Exception as e:
                self.logger.warning(f"   Trading endpoints: Limited access - {e}")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Account info test failed: {e}")
            return False
    
    async def test_paper_order_simulation(self) -> bool:
        """Test paper order simulation (no real orders)."""
        try:
            self.logger.info("📝 Testing paper order simulation...")
            
            symbol = 'BTC/USDT'
            
            # Get current price
            ticker = self.exchange.fetch_ticker(symbol)
            current_price = ticker['last']
            
            # Get trading rules
            markets = self.exchange.load_markets()
            market = markets[symbol]
            min_amount = market['limits']['amount']['min']
            min_cost = market['limits']['cost']['min']
            
            # Calculate order parameters
            buy_price = current_price * 0.99  # 1% below current price
            sell_price = current_price * 1.01  # 1% above current price
            
            # Use minimum amounts for simulation
            amount = max(min_amount, min_cost / current_price) if min_amount and min_cost else 0.001
            
            self.logger.info(f"✅ Paper trading simulation for {symbol}:")
            self.logger.info(f"   Current price: ${current_price:,.2f}")
            self.logger.info(f"   Min amount: {min_amount}")
            self.logger.info(f"   Min cost: ${min_cost}")
            self.logger.info(f"   Simulated BUY order: {amount:.6f} BTC @ ${buy_price:,.2f}")
            self.logger.info(f"   Simulated SELL order: {amount:.6f} BTC @ ${sell_price:,.2f}")
            self.logger.info(f"   Order value: ${amount * current_price:.2f}")
            
            # Note: We're not actually placing orders, just simulating
            self.logger.info("   ✅ Paper orders simulated successfully")
            self.logger.info("   ℹ️ No real orders placed (simulation only)")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Paper order simulation failed: {e}")
            return False
    
    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all KuCoin tests."""
        self.logger.info("🚀 Starting KuCoin API Tests")
        self.logger.info("=" * 50)
        
        # Setup exchange
        if not self.setup_exchange():
            return {'success': False, 'error': 'Failed to setup exchange'}
        
        tests = [
            ("Connection Test", self.test_connection),
            ("Authentication Test", self.test_authentication),
            ("Market Data Test", self.test_market_data),
            ("Order Book Test", self.test_order_book),
            ("Trading Rules Test", self.test_trading_rules),
            ("Account Info Test", self.test_account_info),
            ("Paper Order Simulation", self.test_paper_order_simulation)
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            self.logger.info(f"\n📋 Running: {test_name}")
            try:
                result = await test_func()
                if result:
                    self.logger.info(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    self.logger.warning(f"❌ {test_name}: FAILED")
                self.test_results[test_name] = result
            except Exception as e:
                self.logger.error(f"💥 {test_name}: ERROR - {e}")
                self.test_results[test_name] = False
        
        # Summary
        success_rate = (passed_tests / total_tests) * 100
        self.logger.info(f"\n📊 Test Summary:")
        self.logger.info(f"Passed: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        
        if success_rate >= 85:
            self.logger.info("🎉 KuCoin API connection is excellent!")
        elif success_rate >= 70:
            self.logger.info("✅ KuCoin API connection is good!")
        elif success_rate >= 50:
            self.logger.warning("⚠️ KuCoin API connection has some issues")
        else:
            self.logger.error("🚨 KuCoin API connection has major problems")
        
        return {
            'total_tests': total_tests,
            'passed_tests': passed_tests,
            'success_rate': success_rate,
            'test_results': self.test_results,
            'timestamp': datetime.now().isoformat()
        }
    
    async def cleanup(self):
        """Cleanup resources."""
        if self.exchange:
            # CCXT exchanges don't have async close method
            pass


async def main():
    """Main test function."""
    # Setup logging
    setup_logger(level="INFO")
    logger = logging.getLogger(__name__)
    
    logger.info("🚀 KuCoin API Connection and Trading Test")
    logger.info("=" * 60)
    
    tester = KuCoinTester()
    
    try:
        results = await tester.run_all_tests()
        
        # Print detailed results
        logger.info("\n" + "=" * 50)
        logger.info("📋 DETAILED TEST RESULTS")
        logger.info("=" * 50)
        
        for test_name, result in results['test_results'].items():
            status = "✅ PASS" if result else "❌ FAIL"
            logger.info(f"{test_name}: {status}")
        
        # Next steps
        logger.info("\n" + "=" * 50)
        logger.info("📋 NEXT STEPS")
        logger.info("=" * 50)
        
        if results['success_rate'] >= 70:
            logger.info("1. ✅ KuCoin API connection is working well")
            logger.info("2. 🔄 You can now integrate KuCoin into the main trading system")
            logger.info("3. 📊 Monitor the system performance")
            logger.info("4. 💰 Add funds to start real trading (currently 0 balance)")
            logger.info("5. 🤖 Configure AI trading strategies")
        else:
            logger.info("1. ❌ Fix connection issues before proceeding")
            logger.info("2. 🔧 Check API credentials in .env file")
            logger.info("3. 🌐 Verify internet connection")
            logger.info("4. 📞 Check KuCoin API permissions")
        
        return results['success_rate'] >= 50
        
    except Exception as e:
        logger.error(f"Test execution failed: {e}")
        return False
    finally:
        await tester.cleanup()


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)