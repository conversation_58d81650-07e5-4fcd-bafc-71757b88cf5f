#!/usr/bin/env python3
"""
Market Data Test Script

This script tests real market data retrieval from Hummingbot API.

Author: inkbytefo
"""

import asyncio
import sys
from datetime import datetime

# Add src to path
sys.path.append('src')

from src.config.hummingbot_config import HummingbotConfigManager
from src.execution.hummingbot_service import HummingbotAPIClient
from src.utils.logger import setup_logger, get_logger


async def test_market_data():
    """Test market data retrieval."""
    logger = get_logger(__name__)
    
    try:
        # Initialize configuration
        logger.info("📊 Market Data Test Starting...")
        config_manager = HummingbotConfigManager()
        config = config_manager.get_api_config()
        
        # Create API client
        client = HummingbotAPIClient(config)
        
        # Connect to API
        connected = await client.connect()
        if not connected:
            logger.error("❌ Failed to connect to Hummingbot API")
            return False
        
        logger.info("✅ Connected to Hummingbot API")
        
        # Test available connectors
        try:
            logger.info("Testing available connectors...")
            connectors = await client.get_trading_pairs()
            if connectors:
                logger.info(f"✅ Available connectors: {connectors[:5]}...")  # Show first 5
            else:
                logger.warning("⚠️ No connectors received")
        except Exception as e:
            logger.error(f"❌ Error getting connectors: {e}")
        
        # Test trading rules for different pairs
        test_pairs = [
            ('binance', 'BTC-USDT'),
            ('binance', 'ETH-USDT'),
            ('binance', 'BNB-USDT')
        ]
        
        for exchange, trading_pair in test_pairs:
            try:
                logger.info(f"Testing trading rules for {exchange} {trading_pair}...")
                
                # Get trading rules (this is the closest we can get to market data)
                trading_rules = await client._make_request(
                    "GET",
                    f"connectors/{exchange}/trading-rules?trading_pairs={trading_pair}"
                )
                
                if trading_rules:
                    logger.info(f"✅ Trading rules received for {trading_pair}:")
                    if isinstance(trading_rules, dict) and trading_pair in trading_rules:
                        rules = trading_rules[trading_pair]
                        logger.info(f"   Min order size: {rules.get('min_order_size')}")
                        logger.info(f"   Price increment: {rules.get('min_price_increment')}")
                        logger.info(f"   Min notional: {rules.get('min_notional_size')}")
                    else:
                        logger.info(f"   Data: {trading_rules}")
                else:
                    logger.warning(f"⚠️ No trading rules received for {trading_pair}")
                    
            except Exception as e:
                logger.error(f"❌ Error getting trading rules for {trading_pair}: {e}")
        
        # Test portfolio state
        try:
            logger.info("Testing portfolio state...")
            portfolio = await client._make_request("GET", "portfolio/state")
            if portfolio:
                logger.info(f"✅ Portfolio state received: {portfolio}")
            else:
                logger.warning("⚠️ No portfolio state received")
        except Exception as e:
            logger.error(f"❌ Error getting portfolio state: {e}")
        
        # Cleanup
        await client.disconnect()
        logger.info("🎉 Market data test completed!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Market data test failed: {e}")
        return False


if __name__ == "__main__":
    # Setup logging
    setup_logger()
    
    # Run the test
    success = asyncio.run(test_market_data())
    sys.exit(0 if success else 1)