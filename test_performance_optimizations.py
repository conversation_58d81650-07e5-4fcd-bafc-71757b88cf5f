#!/usr/bin/env python3
"""
Performance Optimization Test Script

Author: inkbytefo
Description: Test script to validate performance improvements across the trading system
"""

import asyncio
import time
import logging
import sys
import os
from typing import Dict, Any
from datetime import datetime, timedelta

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.utils.performance_optimization import (
    PerformanceProfiler, SmartCache, BatchProcessor, MemoryOptimizer,
    AsyncPoolExecutor, CacheStrategy, optimize_numpy_operations,
    optimize_pandas_operations
)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class PerformanceTestSuite:
    """Comprehensive performance test suite."""
    
    def __init__(self):
        self.profiler = PerformanceProfiler()
        self.cache = SmartCache(max_size=1000, strategy=CacheStrategy.LRU)
        self.batch_processor = BatchProcessor(batch_size=10, max_wait_time=1.0)
        self.memory_optimizer = MemoryOptimizer()
        self.executor = AsyncPoolExecutor(max_workers=4, executor_type='thread')
        
    async def test_caching_performance(self):
        """Test caching system performance."""
        logger.info("Testing caching performance...")
        
        # Test cache operations
        start_time = time.time()
        
        # Fill cache
        for i in range(1000):
            self.cache.set(f"key_{i}", f"value_{i}")
        
        # Test cache hits
        for i in range(1000):
            value = self.cache.get(f"key_{i}")
            assert value == f"value_{i}", f"Cache miss for key_{i}"
        
        end_time = time.time()
        logger.info(f"Cache operations completed in {end_time - start_time:.4f} seconds")
        
        # Test cache statistics
        stats = self.cache.get_stats()
        logger.info(f"Cache stats: {stats}")
        
    async def test_batch_processing(self):
        """Test batch processing performance."""
        logger.info("Testing batch processing...")
        
        processed_items = []
        
        async def process_item(item):
            # Simulate processing time
            await asyncio.sleep(0.01)
            processed_items.append(item * 2)
            return item * 2
        
        start_time = time.time()
        
        # Add items to batch processor
        tasks = []
        for i in range(50):
            task = self.batch_processor.add_item(i, process_item)
            tasks.append(task)
        
        # Wait for all items to be processed
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        logger.info(f"Batch processing completed in {end_time - start_time:.4f} seconds")
        logger.info(f"Processed {len(results)} items")
        
    async def test_memory_optimization(self):
        """Test memory optimization features."""
        logger.info("Testing memory optimization...")
        
        # Test memory monitoring
        initial_memory = self.memory_optimizer.get_memory_usage()
        logger.info(f"Initial memory usage: {initial_memory:.2f} MB")
        
        # Create some data structures
        large_data = []
        for i in range(10000):
            large_data.append({f"key_{j}": f"value_{j}" for j in range(100)})
        
        peak_memory = self.memory_optimizer.get_memory_usage()
        logger.info(f"Peak memory usage: {peak_memory:.2f} MB")
        
        # Clean up
        del large_data
        self.memory_optimizer.force_gc()
        
        final_memory = self.memory_optimizer.get_memory_usage()
        logger.info(f"Final memory usage: {final_memory:.2f} MB")
        
    async def test_async_execution(self):
        """Test async pool executor performance."""
        logger.info("Testing async execution...")
        
        def cpu_intensive_task(n):
            """Simulate CPU-intensive task."""
            result = 0
            for i in range(n * 1000):
                result += i ** 2
            return result
        
        start_time = time.time()
        
        # Submit tasks to executor
        tasks = []
        for i in range(20):
            task = self.executor.submit(cpu_intensive_task, 100)
            tasks.append(task)
        
        # Wait for completion
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        logger.info(f"Async execution completed in {end_time - start_time:.4f} seconds")
        logger.info(f"Completed {len(results)} tasks")
        
    async def test_profiling(self):
        """Test performance profiling."""
        logger.info("Testing performance profiling...")
        
        @self.profiler.profile("test_function")
        async def test_function():
            await asyncio.sleep(0.1)
            return "test_result"
        
        # Run function multiple times
        for _ in range(10):
            await test_function()
        
        # Get profiling results
        stats = self.profiler.get_stats()
        logger.info(f"Profiling stats: {stats}")
        
    async def test_numpy_optimization(self):
        """Test NumPy optimization."""
        logger.info("Testing NumPy optimization...")
        
        import numpy as np
        
        # Create test data
        data = np.random.rand(10000, 100)
        
        start_time = time.time()
        
        # Apply optimizations
        optimized_data = optimize_numpy_operations(data)
        
        # Perform some operations
        result = np.mean(optimized_data, axis=1)
        std_result = np.std(optimized_data, axis=1)
        
        end_time = time.time()
        logger.info(f"NumPy operations completed in {end_time - start_time:.4f} seconds")
        logger.info(f"Result shape: {result.shape}, Std shape: {std_result.shape}")
        
    async def test_pandas_optimization(self):
        """Test Pandas optimization."""
        logger.info("Testing Pandas optimization...")
        
        try:
            import pandas as pd
            
            # Create test DataFrame
            data = {
                'price': np.random.rand(10000),
                'volume': np.random.randint(1, 1000, 10000),
                'timestamp': pd.date_range('2024-01-01', periods=10000, freq='1min')
            }
            df = pd.DataFrame(data)
            
            start_time = time.time()
            
            # Apply optimizations
            optimized_df = optimize_pandas_operations(df)
            
            # Perform some operations
            result = optimized_df.groupby(optimized_df['timestamp'].dt.hour)['price'].mean()
            
            end_time = time.time()
            logger.info(f"Pandas operations completed in {end_time - start_time:.4f} seconds")
            logger.info(f"Result length: {len(result)}")
            
        except ImportError:
            logger.warning("Pandas not available, skipping pandas optimization test")
        
    async def run_all_tests(self):
        """Run all performance tests."""
        logger.info("Starting performance optimization test suite...")
        
        test_start = time.time()
        
        try:
            await self.test_caching_performance()
            await self.test_batch_processing()
            await self.test_memory_optimization()
            await self.test_async_execution()
            await self.test_profiling()
            await self.test_numpy_optimization()
            await self.test_pandas_optimization()
            
        except Exception as e:
            logger.error(f"Test failed: {e}")
            raise
        
        finally:
            # Cleanup
            await self.executor.shutdown()
        
        test_end = time.time()
        logger.info(f"All tests completed in {test_end - test_start:.4f} seconds")
        
    def generate_performance_report(self):
        """Generate performance optimization report."""
        report = {
            'timestamp': datetime.now().isoformat(),
            'cache_stats': self.cache.get_stats(),
            'profiler_stats': self.profiler.get_stats(),
            'memory_usage': self.memory_optimizer.get_memory_usage(),
            'optimizations_applied': [
                'Smart caching with LRU strategy',
                'Batch processing for concurrent operations',
                'Memory optimization with garbage collection',
                'Async pool executor for CPU-intensive tasks',
                'Performance profiling and monitoring',
                'NumPy and Pandas optimizations'
            ]
        }
        
        return report


async def main():
    """Main test function."""
    test_suite = PerformanceTestSuite()
    
    try:
        await test_suite.run_all_tests()
        
        # Generate report
        report = test_suite.generate_performance_report()
        logger.info("Performance Optimization Report:")
        for key, value in report.items():
            logger.info(f"  {key}: {value}")
        
        logger.info("Performance optimization tests completed successfully!")
        
    except Exception as e:
        logger.error(f"Performance tests failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())