#!/usr/bin/env python3
"""
System Architecture Test Script for AI Trading Agent

This script tests the core components and architecture of the trading system.

Author: inkbytefo
"""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Dict, Any

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.config.config_manager import ConfigManager
from src.config.settings import Settings
from src.utils.logger import setup_logger
from src.core.agent import TradingAgent


class SystemTester:
    """System architecture and component tester."""
    
    def __init__(self):
        self.logger = setup_logger("system_tester", level="INFO")
        self.config_manager = None
        self.settings = None
        self.agent = None
        self.test_results = {}
    
    async def run_tests(self) -> Dict[str, Any]:
        """Run comprehensive system tests."""
        self.logger.info("Starting system architecture tests...")
        
        tests = [
            ("config_test", self.test_configuration),
            ("settings_test", self.test_settings),
            ("agent_init_test", self.test_agent_initialization),
            ("component_test", self.test_components),
            ("data_flow_test", self.test_data_flow),
            ("risk_system_test", self.test_risk_system),
            ("monitoring_test", self.test_monitoring_system)
        ]
        
        for test_name, test_func in tests:
            try:
                self.logger.info(f"Running {test_name}...")
                result = await test_func()
                self.test_results[test_name] = {
                    "status": "PASS" if result else "FAIL",
                    "details": result if isinstance(result, dict) else {"success": result}
                }
                self.logger.info(f"{test_name}: {'PASS' if result else 'FAIL'}")
            except Exception as e:
                self.test_results[test_name] = {
                    "status": "ERROR",
                    "error": str(e)
                }
                self.logger.error(f"{test_name}: ERROR - {e}")
        
        return self.test_results
    
    async def test_configuration(self) -> bool:
        """Test configuration management."""
        try:
            self.config_manager = ConfigManager(environment="development")
            config = self.config_manager.get_config()
            
            # Check if essential sections exist
            required_sections = ['general', 'trading', 'ai', 'risk', 'data', 'execution', 'monitoring']
            for section in required_sections:
                if section not in config:
                    self.logger.error(f"Missing configuration section: {section}")
                    return False
            
            self.logger.info("Configuration management test passed")
            return True
        except Exception as e:
            self.logger.error(f"Configuration test failed: {e}")
            return False
    
    async def test_settings(self) -> bool:
        """Test settings initialization."""
        try:
            if not self.config_manager:
                return False
            
            self.settings = Settings.from_config(self.config_manager.get_config())
            
            # Validate settings
            if not hasattr(self.settings, 'general'):
                return False
            if not hasattr(self.settings, 'trading'):
                return False
            if not hasattr(self.settings, 'ai'):
                return False
            
            self.logger.info("Settings initialization test passed")
            return True
        except Exception as e:
            self.logger.error(f"Settings test failed: {e}")
            return False
    
    async def test_agent_initialization(self) -> bool:
        """Test trading agent initialization."""
        try:
            if not self.settings:
                return False
            
            self.agent = TradingAgent(self.settings)
            
            # Check if agent has required components
            required_components = [
                'data_collector', 'analysis_engine', 'decision_manager',
                'execution_manager', 'risk_monitor', 'dashboard'
            ]
            
            for component in required_components:
                if not hasattr(self.agent, component):
                    self.logger.error(f"Missing agent component: {component}")
                    return False
            
            self.logger.info("Agent initialization test passed")
            return True
        except Exception as e:
            self.logger.error(f"Agent initialization test failed: {e}")
            return False
    
    async def test_components(self) -> Dict[str, bool]:
        """Test individual components."""
        if not self.agent:
            return {"error": "Agent not initialized"}
        
        component_tests = {}
        
        # Test data collector
        try:
            data_collector = self.agent.data_collector
            if hasattr(data_collector, 'news_collector') and hasattr(data_collector, 'market_collector'):
                component_tests['data_collector'] = True
            else:
                component_tests['data_collector'] = False
        except Exception as e:
            component_tests['data_collector'] = False
            self.logger.error(f"Data collector test failed: {e}")
        
        # Test analysis engine
        try:
            analysis_engine = self.agent.analysis_engine
            if hasattr(analysis_engine, 'technical_analyzer') and hasattr(analysis_engine, 'sentiment_analyzer'):
                component_tests['analysis_engine'] = True
            else:
                component_tests['analysis_engine'] = False
        except Exception as e:
            component_tests['analysis_engine'] = False
            self.logger.error(f"Analysis engine test failed: {e}")
        
        # Test decision manager
        try:
            decision_manager = self.agent.decision_manager
            if hasattr(decision_manager, 'strategy_selector') and hasattr(decision_manager, 'position_sizer'):
                component_tests['decision_manager'] = True
            else:
                component_tests['decision_manager'] = False
        except Exception as e:
            component_tests['decision_manager'] = False
            self.logger.error(f"Decision manager test failed: {e}")
        
        # Test execution manager
        try:
            execution_manager = self.agent.execution_manager
            if hasattr(execution_manager, 'order_manager') and hasattr(execution_manager, 'portfolio_manager'):
                component_tests['execution_manager'] = True
            else:
                component_tests['execution_manager'] = False
        except Exception as e:
            component_tests['execution_manager'] = False
            self.logger.error(f"Execution manager test failed: {e}")
        
        return component_tests
    
    async def test_data_flow(self) -> bool:
        """Test data flow between components."""
        try:
            if not self.agent:
                return False
            
            # Test if data collector can provide data
            data_collector = self.agent.data_collector
            
            # Check if collectors are properly initialized
            if not hasattr(data_collector, 'news_collector'):
                self.logger.error("News collector not found")
                return False
            
            if not hasattr(data_collector, 'market_collector'):
                self.logger.error("Market collector not found")
                return False
            
            self.logger.info("Data flow test passed")
            return True
        except Exception as e:
            self.logger.error(f"Data flow test failed: {e}")
            return False
    
    async def test_risk_system(self) -> bool:
        """Test risk management system."""
        try:
            if not self.agent:
                return False
            
            risk_monitor = self.agent.risk_monitor
            
            # Check if risk monitor has required methods
            required_methods = ['check_position_risk', 'check_portfolio_risk', 'get_risk_metrics']
            for method in required_methods:
                if not hasattr(risk_monitor, method):
                    self.logger.error(f"Risk monitor missing method: {method}")
                    return False
            
            self.logger.info("Risk system test passed")
            return True
        except Exception as e:
            self.logger.error(f"Risk system test failed: {e}")
            return False
    
    async def test_monitoring_system(self) -> bool:
        """Test monitoring and dashboard system."""
        try:
            if not self.agent:
                return False
            
            dashboard = self.agent.dashboard
            
            # Check if dashboard has required components
            if not hasattr(dashboard, 'performance_monitor'):
                self.logger.error("Performance monitor not found in dashboard")
                return False
            
            if not hasattr(dashboard, 'system_monitor'):
                self.logger.error("System monitor not found in dashboard")
                return False
            
            self.logger.info("Monitoring system test passed")
            return True
        except Exception as e:
            self.logger.error(f"Monitoring system test failed: {e}")
            return False
    
    def print_test_summary(self):
        """Print test results summary."""
        print("\n" + "="*60)
        print("SYSTEM ARCHITECTURE TEST RESULTS")
        print("="*60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'PASS')
        failed_tests = sum(1 for result in self.test_results.values() if result['status'] == 'FAIL')
        error_tests = sum(1 for result in self.test_results.values() if result['status'] == 'ERROR')
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Errors: {error_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        print("\nDetailed Results:")
        print("-"*40)
        
        for test_name, result in self.test_results.items():
            status_symbol = "✓" if result['status'] == 'PASS' else "✗" if result['status'] == 'FAIL' else "⚠"
            print(f"{status_symbol} {test_name}: {result['status']}")
            
            if result['status'] == 'ERROR':
                print(f"  Error: {result['error']}")
            elif result['status'] == 'FAIL' and 'details' in result:
                if isinstance(result['details'], dict):
                    for key, value in result['details'].items():
                        if isinstance(value, bool):
                            symbol = "✓" if value else "✗"
                            print(f"    {symbol} {key}")
        
        print("\n" + "="*60)
        
        if passed_tests == total_tests:
            print("🎉 ALL TESTS PASSED! System architecture is working correctly.")
        elif passed_tests > total_tests * 0.8:
            print("⚠️  Most tests passed, but some issues need attention.")
        else:
            print("❌ Multiple test failures detected. System needs debugging.")
        
        print("="*60)


async def main():
    """Main test function."""
    tester = SystemTester()
    
    try:
        await tester.run_tests()
        tester.print_test_summary()
        
        # Return appropriate exit code
        passed_tests = sum(1 for result in tester.test_results.values() if result['status'] == 'PASS')
        total_tests = len(tester.test_results)
        
        if passed_tests == total_tests:
            return 0  # All tests passed
        elif passed_tests > total_tests * 0.8:
            return 1  # Most tests passed
        else:
            return 2  # Multiple failures
            
    except Exception as e:
        print(f"Test execution failed: {e}")
        return 3


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)