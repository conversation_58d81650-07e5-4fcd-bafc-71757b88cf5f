#!/usr/bin/env python3
"""
Basic System Test

Bu script temel sistem bileşenlerini test eder.

Author: inkbytefo
"""

import sys
import os
import asyncio
import logging
from datetime import datetime

# Add src to path
sys.path.append('src')

def test_imports():
    """Test basic imports."""
    print("🔍 Testing imports...")
    
    try:
        # Test basic imports
        import numpy as np
        import pandas as pd
        import ccxt
        from dotenv import load_dotenv
        print("✅ Basic libraries imported successfully")
        
        # Test project imports
        from src.utils.logger import setup_logger
        print("✅ Logger module imported")
        
        from src.config.settings import Settings
        print("✅ Settings module imported")
        
        from src.config.config_manager import ConfigManager
        print("✅ Config manager imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ General error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("\n🔧 Testing configuration...")
    
    try:
        from src.config.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        print("✅ Config manager created")
        
        # Test getting settings
        settings = config_manager.get_settings()
        print("✅ Settings loaded")
        
        print(f"   App name: {settings.general.app_name}")
        print(f"   Version: {settings.general.version}")
        print(f"   Environment: {settings.general.environment}")
        print(f"   Default exchange: {settings.trading.default_exchange}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

def test_logger():
    """Test logging system."""
    print("\n📝 Testing logger...")
    
    try:
        from src.utils.logger import setup_logger, get_logger
        
        # Setup logger
        setup_logger(level="INFO")
        logger = get_logger(__name__)
        
        logger.info("Test log message")
        print("✅ Logger working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Logger test failed: {e}")
        return False

def test_data_structures():
    """Test basic data structures."""
    print("\n📊 Testing data structures...")
    
    try:
        import pandas as pd
        import numpy as np
        
        # Create sample data
        data = {
            'timestamp': pd.date_range('2024-01-01', periods=100, freq='1H'),
            'price': np.random.uniform(40000, 50000, 100),
            'volume': np.random.uniform(1, 100, 100)
        }
        
        df = pd.DataFrame(data)
        print(f"✅ Sample dataframe created: {df.shape}")
        
        # Basic calculations
        mean_price = df['price'].mean()
        print(f"   Mean price: ${mean_price:,.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Data structures test failed: {e}")
        return False

def test_ccxt():
    """Test CCXT library."""
    print("\n🔗 Testing CCXT...")
    
    try:
        import ccxt
        
        # Create exchange instance (without credentials)
        exchange = ccxt.binance({
            'sandbox': False,  # Use live data for testing
            'enableRateLimit': True,
        })
        
        print("✅ CCXT Binance exchange created")
        
        # Test public endpoint
        try:
            ticker = exchange.fetch_ticker('BTC/USDT')
            print(f"✅ BTC/USDT price: ${ticker['last']:,.2f}")
            return True
        except Exception as e:
            print(f"⚠️ Public API test failed: {e}")
            print("   This might be due to rate limiting")
            return True  # Still consider success
        
    except Exception as e:
        print(f"❌ CCXT test failed: {e}")
        return False

async def test_async_functionality():
    """Test async functionality."""
    print("\n⚡ Testing async functionality...")
    
    try:
        # Simple async test
        await asyncio.sleep(0.1)
        print("✅ Basic async/await working")
        
        # Test async with multiple tasks
        async def sample_task(n):
            await asyncio.sleep(0.01)
            return f"Task {n} completed"
        
        tasks = [sample_task(i) for i in range(3)]
        results = await asyncio.gather(*tasks)
        
        print(f"✅ Async tasks completed: {len(results)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Async test failed: {e}")
        return False

def test_environment():
    """Test environment setup."""
    print("\n🌍 Testing environment...")
    
    try:
        from dotenv import load_dotenv
        
        # Load environment variables
        load_dotenv()
        
        # Check for API keys
        api_key = os.getenv('BINANCE_API_KEY')
        api_secret = os.getenv('BINANCE_API_SECRET')
        
        if api_key and api_secret:
            print(f"✅ API credentials found")
            print(f"   API Key: {api_key[:8]}...")
        else:
            print("⚠️ API credentials not found in .env")
            print("   This is normal if not configured yet")
        
        return True
        
    except Exception as e:
        print(f"❌ Environment test failed: {e}")
        return False

async def run_all_tests():
    """Run all system tests."""
    print("🚀 AI Trading System - Basic System Test")
    print("=" * 60)
    
    tests = [
        ("Import Test", test_imports),
        ("Configuration Test", test_configuration),
        ("Logger Test", test_logger),
        ("Data Structures Test", test_data_structures),
        ("CCXT Test", test_ccxt),
        ("Environment Test", test_environment),
    ]
    
    # Run sync tests
    passed_tests = 0
    total_tests = len(tests) + 1  # +1 for async test
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
    
    # Run async test
    try:
        result = await test_async_functionality()
        if result:
            passed_tests += 1
    except Exception as e:
        print(f"❌ Async test failed with exception: {e}")
    
    # Summary
    success_rate = (passed_tests / total_tests) * 100
    
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {total_tests}")
    print(f"Passed: {passed_tests}")
    print(f"Failed: {total_tests - passed_tests}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 85:
        print("\n🎉 System is ready for trading!")
        print("✅ All core components are working correctly")
        print("\n📋 Next Steps:")
        print("1. Configure Binance testnet API keys")
        print("2. Run comprehensive trading tests")
        print("3. Start with paper trading")
    elif success_rate >= 70:
        print("\n✅ System is mostly ready")
        print("⚠️ Some minor issues detected")
        print("\n📋 Next Steps:")
        print("1. Review failed tests")
        print("2. Fix any configuration issues")
        print("3. Re-run tests")
    else:
        print("\n❌ System needs attention")
        print("🚨 Multiple components have issues")
        print("\n📋 Next Steps:")
        print("1. Check Python environment")
        print("2. Install missing dependencies")
        print("3. Fix configuration issues")
    
    return success_rate >= 70

if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)