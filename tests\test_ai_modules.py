#!/usr/bin/env python3
"""
AI Modules Test Suite

Author: inkbytefo
Description: Comprehensive tests for AI analysis components
"""

import asyncio
import unittest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock
import pandas as pd
import numpy as np
from datetime import datetime, timedelta

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from src.ai.analysis_engine import AnalysisEngine
from src.ai.sentiment_analyzer import SentimentAnalyzer
from src.ai.pattern_detector import PatternDetector
from src.ai.prediction_model import PredictionModel
from src.utils.logger import setup_logger


class TestAIModules(unittest.TestCase):
    """Test suite for AI modules."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = setup_logger("test_ai_modules", level="INFO")
        
        # Mock settings
        self.mock_settings = {
            'ai': {
                'sentiment': {
                    'enabled': True,
                    'sources': ['news', 'social'],
                    'update_interval': 300
                },
                'prediction': {
                    'enabled': True,
                    'models': ['price', 'direction', 'volatility'],
                    'timeframes': ['short', 'medium', 'long']
                },
                'pattern': {
                    'enabled': True,
                    'indicators': ['rsi', 'macd', 'bollinger']
                }
            }
        }
        
        # Sample market data
        self.sample_data = pd.DataFrame({
            'timestamp': pd.date_range(start='2024-01-01', periods=100, freq='1H'),
            'open': np.random.uniform(100, 110, 100),
            'high': np.random.uniform(110, 120, 100),
            'low': np.random.uniform(90, 100, 100),
            'close': np.random.uniform(100, 110, 100),
            'volume': np.random.uniform(1000, 10000, 100)
        })
        
        # Sample news data
        self.sample_news = [
            {
                'title': 'Bitcoin reaches new all-time high',
                'content': 'Bitcoin has surged to unprecedented levels...',
                'timestamp': datetime.now(),
                'source': 'CoinDesk',
                'sentiment_score': 0.8
            },
            {
                'title': 'Market volatility increases amid uncertainty',
                'content': 'Cryptocurrency markets show increased volatility...',
                'timestamp': datetime.now() - timedelta(hours=1),
                'source': 'CryptoNews',
                'sentiment_score': -0.3
            }
        ]
    
    def test_sentiment_analyzer_initialization(self):
        """Test sentiment analyzer initialization."""
        try:
            analyzer = SentimentAnalyzer(self.mock_settings['ai']['sentiment'])
            self.assertIsNotNone(analyzer)
            self.assertTrue(analyzer.enabled)
            self.logger.info("✅ SentimentAnalyzer initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ SentimentAnalyzer initialization test failed: {e}")
            self.fail(f"SentimentAnalyzer initialization failed: {e}")
    
    def test_sentiment_analysis(self):
        """Test sentiment analysis functionality."""
        try:
            analyzer = SentimentAnalyzer(self.mock_settings['ai']['sentiment'])
            
            # Test text sentiment analysis
            positive_text = "Bitcoin is performing excellently with strong bullish momentum"
            negative_text = "Market crash imminent, sell everything now"
            
            pos_sentiment = analyzer.analyze_text_sentiment(positive_text)
            neg_sentiment = analyzer.analyze_text_sentiment(negative_text)
            
            self.assertGreater(pos_sentiment, 0, "Positive text should have positive sentiment")
            self.assertLess(neg_sentiment, 0, "Negative text should have negative sentiment")
            
            self.logger.info("✅ Sentiment analysis test passed")
        except Exception as e:
            self.logger.error(f"❌ Sentiment analysis test failed: {e}")
            self.fail(f"Sentiment analysis failed: {e}")
    
    def test_pattern_detector_initialization(self):
        """Test pattern detector initialization."""
        try:
            detector = PatternDetector(self.mock_settings['ai']['pattern'])
            self.assertIsNotNone(detector)
            self.assertTrue(detector.enabled)
            self.logger.info("✅ PatternDetector initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ PatternDetector initialization test failed: {e}")
            self.fail(f"PatternDetector initialization failed: {e}")
    
    def test_pattern_detection(self):
        """Test pattern detection functionality."""
        try:
            detector = PatternDetector(self.mock_settings['ai']['pattern'])
            
            # Test technical indicator calculation
            indicators = detector.calculate_technical_indicators(self.sample_data)
            
            self.assertIsInstance(indicators, dict)
            self.assertIn('rsi', indicators)
            self.assertIn('macd', indicators)
            
            # Test pattern recognition
            patterns = detector.detect_patterns(self.sample_data)
            self.assertIsInstance(patterns, list)
            
            self.logger.info("✅ Pattern detection test passed")
        except Exception as e:
            self.logger.error(f"❌ Pattern detection test failed: {e}")
            self.fail(f"Pattern detection failed: {e}")
    
    def test_prediction_model_initialization(self):
        """Test prediction model initialization."""
        try:
            model = PredictionModel(self.mock_settings['ai']['prediction'])
            self.assertIsNotNone(model)
            self.assertTrue(model.enabled)
            self.logger.info("✅ PredictionModel initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ PredictionModel initialization test failed: {e}")
            self.fail(f"PredictionModel initialization failed: {e}")
    
    @patch('src.ai.prediction_model.PredictionModel._load_models')
    def test_prediction_model_training(self, mock_load):
        """Test prediction model training functionality."""
        try:
            mock_load.return_value = None
            model = PredictionModel(self.mock_settings['ai']['prediction'])
            
            # Test feature engineering
            features = model.engineer_features(self.sample_data)
            self.assertIsInstance(features, pd.DataFrame)
            self.assertGreater(len(features.columns), len(self.sample_data.columns))
            
            self.logger.info("✅ Prediction model training test passed")
        except Exception as e:
            self.logger.error(f"❌ Prediction model training test failed: {e}")
            self.fail(f"Prediction model training failed: {e}")
    
    def test_analysis_engine_initialization(self):
        """Test analysis engine initialization."""
        try:
            engine = AnalysisEngine(self.mock_settings)
            self.assertIsNotNone(engine)
            self.assertIsNotNone(engine.sentiment_analyzer)
            self.assertIsNotNone(engine.pattern_detector)
            self.assertIsNotNone(engine.prediction_model)
            self.logger.info("✅ AnalysisEngine initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ AnalysisEngine initialization test failed: {e}")
            self.fail(f"AnalysisEngine initialization failed: {e}")
    
    async def test_analysis_engine_comprehensive_analysis(self):
        """Test comprehensive analysis functionality."""
        try:
            engine = AnalysisEngine(self.mock_settings)
            
            # Mock data
            market_data = {
                'BTC/USDT': self.sample_data,
                'ETH/USDT': self.sample_data.copy()
            }
            
            news_data = self.sample_news
            
            # Perform comprehensive analysis
            analysis_result = await engine.analyze_comprehensive(
                market_data=market_data,
                news_data=news_data,
                symbols=['BTC/USDT', 'ETH/USDT']
            )
            
            self.assertIsInstance(analysis_result, dict)
            self.assertIn('technical_analysis', analysis_result)
            self.assertIn('sentiment_analysis', analysis_result)
            self.assertIn('predictions', analysis_result)
            
            self.logger.info("✅ Comprehensive analysis test passed")
        except Exception as e:
            self.logger.error(f"❌ Comprehensive analysis test failed: {e}")
            self.fail(f"Comprehensive analysis failed: {e}")
    
    def test_ai_module_integration(self):
        """Test integration between AI modules."""
        try:
            # Initialize all modules
            sentiment_analyzer = SentimentAnalyzer(self.mock_settings['ai']['sentiment'])
            pattern_detector = PatternDetector(self.mock_settings['ai']['pattern'])
            prediction_model = PredictionModel(self.mock_settings['ai']['prediction'])
            
            # Test data flow between modules
            sentiment_score = sentiment_analyzer.analyze_news_batch(self.sample_news)
            technical_indicators = pattern_detector.calculate_technical_indicators(self.sample_data)
            
            self.assertIsInstance(sentiment_score, (int, float))
            self.assertIsInstance(technical_indicators, dict)
            
            self.logger.info("✅ AI module integration test passed")
        except Exception as e:
            self.logger.error(f"❌ AI module integration test failed: {e}")
            self.fail(f"AI module integration failed: {e}")
    
    def test_error_handling(self):
        """Test error handling in AI modules."""
        try:
            # Test with invalid data
            analyzer = SentimentAnalyzer(self.mock_settings['ai']['sentiment'])
            
            # Should handle empty text gracefully
            result = analyzer.analyze_text_sentiment("")
            self.assertIsInstance(result, (int, float))
            
            # Should handle None input gracefully
            result = analyzer.analyze_text_sentiment(None)
            self.assertIsInstance(result, (int, float))
            
            self.logger.info("✅ Error handling test passed")
        except Exception as e:
            self.logger.error(f"❌ Error handling test failed: {e}")
            self.fail(f"Error handling failed: {e}")


class TestAIModulesAsync(unittest.IsolatedAsyncioTestCase):
    """Async test suite for AI modules."""
    
    async def asyncSetUp(self):
        """Set up async test fixtures."""
        self.logger = setup_logger("test_ai_modules_async", level="INFO")
        
        self.mock_settings = {
            'ai': {
                'sentiment': {'enabled': True, 'sources': ['news', 'social']},
                'prediction': {'enabled': True, 'models': ['price', 'direction']},
                'pattern': {'enabled': True, 'indicators': ['rsi', 'macd']}
            }
        }
    
    async def test_async_analysis_pipeline(self):
        """Test async analysis pipeline."""
        try:
            engine = AnalysisEngine(self.mock_settings)
            
            # Mock async data collection
            async def mock_data_collection():
                return {
                    'market_data': {'BTC/USDT': pd.DataFrame({'close': [100, 101, 102]})},
                    'news_data': [{'title': 'Test news', 'sentiment_score': 0.5}]
                }
            
            data = await mock_data_collection()
            self.assertIsNotNone(data)
            
            self.logger.info("✅ Async analysis pipeline test passed")
        except Exception as e:
            self.logger.error(f"❌ Async analysis pipeline test failed: {e}")
            self.fail(f"Async analysis pipeline failed: {e}")


def run_ai_tests():
    """Run all AI module tests."""
    print("\n" + "=" * 60)
    print("AI MODULES TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestAIModules))
    suite.addTests(loader.loadTestsFromTestCase(TestAIModulesAsync))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 ALL AI MODULE TESTS PASSED!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_ai_tests()
    sys.exit(0 if success else 1)