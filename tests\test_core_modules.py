#!/usr/bin/env python3
"""
Core Modules Test Suite

Author: inkbytefo
Description: Comprehensive tests for core system components including agent, configuration, events, and state management
"""

import asyncio
import unittest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import json
import time
from datetime import datetime, timedelta
import threading
from enum import Enum
import yaml

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.core.agent import TradingAgent
    from src.core.config_manager import ConfigurationManager
    from src.core.event_system import EventSystem, Event, EventType
    from src.core.state_manager import StateManager, TradingState
    from src.core.plugin_system import PluginSystem, Plugin
    from src.utils.logger import setup_logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Creating mock classes for testing...")
    
    # Create mock classes if imports fail
    class EventType(Enum):
        MARKET_DATA_UPDATE = "market_data_update"
        TRADE_SIGNAL = "trade_signal"
        ORDER_EXECUTED = "order_executed"
        RISK_ALERT = "risk_alert"
        SYSTEM_STATUS = "system_status"
        ERROR = "error"
    
    class TradingState(Enum):
        INITIALIZING = "initializing"
        RUNNING = "running"
        PAUSED = "paused"
        STOPPED = "stopped"
        ERROR = "error"
    
    class Event:
        def __init__(self, event_type, data=None, timestamp=None):
            self.type = event_type
            self.data = data or {}
            self.timestamp = timestamp or datetime.now()
            self.id = f"{event_type.value}_{int(time.time() * 1000)}"
        
        def to_dict(self):
            return {
                'id': self.id,
                'type': self.type.value,
                'data': self.data,
                'timestamp': self.timestamp.isoformat()
            }
    
    class TradingAgent:
        def __init__(self, config):
            self.config = config
            self.state = TradingState.INITIALIZING
            self.is_running = False
            self.components = {}
            self.event_system = EventSystem()
            self.state_manager = StateManager()
            self.config_manager = ConfigurationManager(config)
            self.plugin_system = PluginSystem()
        
        async def start(self):
            self.state = TradingState.RUNNING
            self.is_running = True
            return True
        
        async def stop(self):
            self.state = TradingState.STOPPED
            self.is_running = False
            return True
        
        async def pause(self):
            self.state = TradingState.PAUSED
            return True
        
        async def resume(self):
            self.state = TradingState.RUNNING
            return True
        
        def get_status(self):
            return {
                'state': self.state.value,
                'is_running': self.is_running,
                'uptime': time.time(),
                'components': len(self.components)
            }
        
        def add_component(self, name, component):
            self.components[name] = component
        
        def get_component(self, name):
            return self.components.get(name)
    
    class ConfigurationManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.watchers = []
            self.config_file = None
        
        def get(self, key, default=None):
            keys = key.split('.')
            value = self.config
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            return value
        
        def set(self, key, value):
            keys = key.split('.')
            config = self.config
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            config[keys[-1]] = value
        
        def load_from_file(self, file_path):
            self.config_file = file_path
            try:
                with open(file_path, 'r') as f:
                    if file_path.endswith('.json'):
                        self.config = json.load(f)
                    elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
                        self.config = yaml.safe_load(f)
                return True
            except:
                return False
        
        def save_to_file(self, file_path=None):
            file_path = file_path or self.config_file
            if not file_path:
                return False
            try:
                with open(file_path, 'w') as f:
                    if file_path.endswith('.json'):
                        json.dump(self.config, f, indent=2)
                    elif file_path.endswith('.yaml') or file_path.endswith('.yml'):
                        yaml.dump(self.config, f, default_flow_style=False)
                return True
            except:
                return False
        
        def watch(self, callback):
            self.watchers.append(callback)
        
        def notify_watchers(self, key, old_value, new_value):
            for watcher in self.watchers:
                try:
                    watcher(key, old_value, new_value)
                except:
                    pass
    
    class EventSystem:
        def __init__(self):
            self.listeners = {}
            self.event_queue = asyncio.Queue() if hasattr(asyncio, 'Queue') else []
            self.is_processing = False
        
        def subscribe(self, event_type, callback):
            if event_type not in self.listeners:
                self.listeners[event_type] = []
            self.listeners[event_type].append(callback)
        
        def unsubscribe(self, event_type, callback):
            if event_type in self.listeners:
                try:
                    self.listeners[event_type].remove(callback)
                except ValueError:
                    pass
        
        async def emit(self, event):
            if event.type in self.listeners:
                for callback in self.listeners[event.type]:
                    try:
                        if asyncio.iscoroutinefunction(callback):
                            await callback(event)
                        else:
                            callback(event)
                    except Exception as e:
                        print(f"Error in event callback: {e}")
        
        def emit_sync(self, event):
            if event.type in self.listeners:
                for callback in self.listeners[event.type]:
                    try:
                        callback(event)
                    except Exception as e:
                        print(f"Error in event callback: {e}")
        
        async def start_processing(self):
            self.is_processing = True
            return True
        
        async def stop_processing(self):
            self.is_processing = False
            return True
    
    class StateManager:
        def __init__(self):
            self.state = {}
            self.history = []
            self.max_history = 1000
            self.lock = threading.Lock()
        
        def get_state(self, key=None):
            with self.lock:
                if key:
                    return self.state.get(key)
                return self.state.copy()
        
        def set_state(self, key, value):
            with self.lock:
                old_value = self.state.get(key)
                self.state[key] = value
                self._add_to_history(key, old_value, value)
        
        def update_state(self, updates):
            with self.lock:
                for key, value in updates.items():
                    old_value = self.state.get(key)
                    self.state[key] = value
                    self._add_to_history(key, old_value, value)
        
        def _add_to_history(self, key, old_value, new_value):
            self.history.append({
                'key': key,
                'old_value': old_value,
                'new_value': new_value,
                'timestamp': datetime.now()
            })
            if len(self.history) > self.max_history:
                self.history.pop(0)
        
        def get_history(self, key=None, limit=None):
            history = self.history
            if key:
                history = [h for h in history if h['key'] == key]
            if limit:
                history = history[-limit:]
            return history
        
        def clear_state(self):
            with self.lock:
                self.state.clear()
                self.history.clear()
    
    class Plugin:
        def __init__(self, name, version="1.0.0"):
            self.name = name
            self.version = version
            self.enabled = False
            self.dependencies = []
        
        async def initialize(self):
            return True
        
        async def start(self):
            self.enabled = True
            return True
        
        async def stop(self):
            self.enabled = False
            return True
        
        def get_info(self):
            return {
                'name': self.name,
                'version': self.version,
                'enabled': self.enabled,
                'dependencies': self.dependencies
            }
    
    class PluginSystem:
        def __init__(self):
            self.plugins = {}
            self.plugin_order = []
        
        def register_plugin(self, plugin):
            self.plugins[plugin.name] = plugin
            if plugin.name not in self.plugin_order:
                self.plugin_order.append(plugin.name)
        
        def unregister_plugin(self, plugin_name):
            if plugin_name in self.plugins:
                del self.plugins[plugin_name]
            if plugin_name in self.plugin_order:
                self.plugin_order.remove(plugin_name)
        
        def get_plugin(self, plugin_name):
            return self.plugins.get(plugin_name)
        
        def list_plugins(self):
            return list(self.plugins.keys())
        
        async def start_all_plugins(self):
            for plugin_name in self.plugin_order:
                plugin = self.plugins[plugin_name]
                await plugin.start()
        
        async def stop_all_plugins(self):
            for plugin_name in reversed(self.plugin_order):
                plugin = self.plugins[plugin_name]
                await plugin.stop()
    
    def setup_logger(name, level="INFO"):
        import logging
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level))
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class TestCoreModules(unittest.TestCase):
    """Test suite for core modules."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = setup_logger("test_core_modules", level="INFO")
        
        # Mock settings
        self.mock_settings = {
            'trading': {
                'enabled': True,
                'mode': 'paper',
                'symbols': ['BTC/USDT', 'ETH/USDT'],
                'risk_management': {
                    'max_position_size': 0.1,
                    'stop_loss': 0.02,
                    'take_profit': 0.05
                }
            },
            'data': {
                'sources': ['binance', 'coinbase'],
                'update_interval': 60
            },
            'ai': {
                'model': 'lstm',
                'prediction_horizon': 24
            }
        }
    
    def test_trading_agent_initialization(self):
        """Test trading agent initialization."""
        try:
            agent = TradingAgent(self.mock_settings)
            self.assertIsNotNone(agent)
            self.assertEqual(agent.state, TradingState.INITIALIZING)
            self.assertFalse(agent.is_running)
            self.assertIsNotNone(agent.event_system)
            self.assertIsNotNone(agent.state_manager)
            self.assertIsNotNone(agent.config_manager)
            self.assertIsNotNone(agent.plugin_system)
            self.logger.info("✅ TradingAgent initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ TradingAgent initialization test failed: {e}")
            self.fail(f"TradingAgent initialization failed: {e}")
    
    def test_configuration_manager_initialization(self):
        """Test configuration manager initialization."""
        try:
            config_manager = ConfigurationManager(self.mock_settings)
            self.assertIsNotNone(config_manager)
            self.assertIsInstance(config_manager.config, dict)
            self.assertIsInstance(config_manager.watchers, list)
            self.logger.info("✅ ConfigurationManager initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ ConfigurationManager initialization test failed: {e}")
            self.fail(f"ConfigurationManager initialization failed: {e}")
    
    def test_configuration_get_set(self):
        """Test configuration get/set functionality."""
        try:
            config_manager = ConfigurationManager(self.mock_settings)
            
            # Test get
            trading_enabled = config_manager.get('trading.enabled')
            self.assertTrue(trading_enabled)
            
            # Test get with default
            non_existent = config_manager.get('non.existent.key', 'default_value')
            self.assertEqual(non_existent, 'default_value')
            
            # Test set
            config_manager.set('new.setting', 'test_value')
            retrieved_value = config_manager.get('new.setting')
            self.assertEqual(retrieved_value, 'test_value')
            
            self.logger.info("✅ Configuration get/set test passed")
        except Exception as e:
            self.logger.error(f"❌ Configuration get/set test failed: {e}")
            self.fail(f"Configuration get/set failed: {e}")
    
    def test_event_system_initialization(self):
        """Test event system initialization."""
        try:
            event_system = EventSystem()
            self.assertIsNotNone(event_system)
            self.assertIsInstance(event_system.listeners, dict)
            self.assertFalse(event_system.is_processing)
            self.logger.info("✅ EventSystem initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ EventSystem initialization test failed: {e}")
            self.fail(f"EventSystem initialization failed: {e}")
    
    def test_event_subscription_and_emission(self):
        """Test event subscription and emission."""
        try:
            event_system = EventSystem()
            received_events = []
            
            # Subscribe to events
            def event_handler(event):
                received_events.append(event)
            
            event_system.subscribe(EventType.MARKET_DATA_UPDATE, event_handler)
            
            # Create and emit event
            test_event = Event(EventType.MARKET_DATA_UPDATE, {'symbol': 'BTC/USDT', 'price': 45000})
            event_system.emit_sync(test_event)
            
            # Verify event was received
            self.assertEqual(len(received_events), 1)
            self.assertEqual(received_events[0].type, EventType.MARKET_DATA_UPDATE)
            self.assertEqual(received_events[0].data['symbol'], 'BTC/USDT')
            
            self.logger.info("✅ Event subscription and emission test passed")
        except Exception as e:
            self.logger.error(f"❌ Event subscription and emission test failed: {e}")
            self.fail(f"Event subscription and emission failed: {e}")
    
    def test_state_manager_initialization(self):
        """Test state manager initialization."""
        try:
            state_manager = StateManager()
            self.assertIsNotNone(state_manager)
            self.assertIsInstance(state_manager.state, dict)
            self.assertIsInstance(state_manager.history, list)
            self.assertEqual(len(state_manager.state), 0)
            self.logger.info("✅ StateManager initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ StateManager initialization test failed: {e}")
            self.fail(f"StateManager initialization failed: {e}")
    
    def test_state_management(self):
        """Test state management functionality."""
        try:
            state_manager = StateManager()
            
            # Test set state
            state_manager.set_state('current_price', 45000)
            price = state_manager.get_state('current_price')
            self.assertEqual(price, 45000)
            
            # Test update state
            state_manager.update_state({
                'volume': 1000,
                'timestamp': datetime.now()
            })
            
            full_state = state_manager.get_state()
            self.assertIn('current_price', full_state)
            self.assertIn('volume', full_state)
            self.assertIn('timestamp', full_state)
            
            # Test history
            history = state_manager.get_history()
            self.assertGreater(len(history), 0)
            
            self.logger.info("✅ State management test passed")
        except Exception as e:
            self.logger.error(f"❌ State management test failed: {e}")
            self.fail(f"State management failed: {e}")
    
    def test_plugin_system_initialization(self):
        """Test plugin system initialization."""
        try:
            plugin_system = PluginSystem()
            self.assertIsNotNone(plugin_system)
            self.assertIsInstance(plugin_system.plugins, dict)
            self.assertIsInstance(plugin_system.plugin_order, list)
            self.assertEqual(len(plugin_system.plugins), 0)
            self.logger.info("✅ PluginSystem initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ PluginSystem initialization test failed: {e}")
            self.fail(f"PluginSystem initialization failed: {e}")
    
    def test_plugin_registration(self):
        """Test plugin registration and management."""
        try:
            plugin_system = PluginSystem()
            
            # Create test plugin
            test_plugin = Plugin('test_plugin', '1.0.0')
            
            # Register plugin
            plugin_system.register_plugin(test_plugin)
            
            # Verify registration
            self.assertIn('test_plugin', plugin_system.plugins)
            self.assertIn('test_plugin', plugin_system.plugin_order)
            
            # Get plugin
            retrieved_plugin = plugin_system.get_plugin('test_plugin')
            self.assertEqual(retrieved_plugin.name, 'test_plugin')
            
            # List plugins
            plugin_list = plugin_system.list_plugins()
            self.assertIn('test_plugin', plugin_list)
            
            # Unregister plugin
            plugin_system.unregister_plugin('test_plugin')
            self.assertNotIn('test_plugin', plugin_system.plugins)
            
            self.logger.info("✅ Plugin registration test passed")
        except Exception as e:
            self.logger.error(f"❌ Plugin registration test failed: {e}")
            self.fail(f"Plugin registration failed: {e}")
    
    def test_component_management(self):
        """Test agent component management."""
        try:
            agent = TradingAgent(self.mock_settings)
            
            # Add component
            test_component = Mock()
            agent.add_component('test_component', test_component)
            
            # Get component
            retrieved_component = agent.get_component('test_component')
            self.assertEqual(retrieved_component, test_component)
            
            # Check status
            status = agent.get_status()
            self.assertEqual(status['components'], 1)
            
            self.logger.info("✅ Component management test passed")
        except Exception as e:
            self.logger.error(f"❌ Component management test failed: {e}")
            self.fail(f"Component management failed: {e}")


class TestCoreModulesAsync(unittest.IsolatedAsyncioTestCase):
    """Async test suite for core modules."""
    
    async def asyncSetUp(self):
        """Set up async test fixtures."""
        self.logger = setup_logger("test_core_modules_async", level="INFO")
        
        self.mock_settings = {
            'trading': {'enabled': True, 'mode': 'paper'},
            'data': {'update_interval': 60}
        }
    
    async def test_trading_agent_lifecycle(self):
        """Test trading agent lifecycle operations."""
        try:
            agent = TradingAgent(self.mock_settings)
            
            # Test start
            start_result = await agent.start()
            self.assertTrue(start_result)
            self.assertEqual(agent.state, TradingState.RUNNING)
            self.assertTrue(agent.is_running)
            
            # Test pause
            pause_result = await agent.pause()
            self.assertTrue(pause_result)
            self.assertEqual(agent.state, TradingState.PAUSED)
            
            # Test resume
            resume_result = await agent.resume()
            self.assertTrue(resume_result)
            self.assertEqual(agent.state, TradingState.RUNNING)
            
            # Test stop
            stop_result = await agent.stop()
            self.assertTrue(stop_result)
            self.assertEqual(agent.state, TradingState.STOPPED)
            self.assertFalse(agent.is_running)
            
            self.logger.info("✅ Trading agent lifecycle test passed")
        except Exception as e:
            self.logger.error(f"❌ Trading agent lifecycle test failed: {e}")
            self.fail(f"Trading agent lifecycle failed: {e}")
    
    async def test_async_event_emission(self):
        """Test async event emission."""
        try:
            event_system = EventSystem()
            received_events = []
            
            # Async event handler
            async def async_event_handler(event):
                received_events.append(event)
            
            event_system.subscribe(EventType.TRADE_SIGNAL, async_event_handler)
            
            # Create and emit event
            test_event = Event(EventType.TRADE_SIGNAL, {'action': 'buy', 'symbol': 'BTC/USDT'})
            await event_system.emit(test_event)
            
            # Verify event was received
            self.assertEqual(len(received_events), 1)
            self.assertEqual(received_events[0].type, EventType.TRADE_SIGNAL)
            
            self.logger.info("✅ Async event emission test passed")
        except Exception as e:
            self.logger.error(f"❌ Async event emission test failed: {e}")
            self.fail(f"Async event emission failed: {e}")
    
    async def test_event_system_processing(self):
        """Test event system processing lifecycle."""
        try:
            event_system = EventSystem()
            
            # Test start processing
            start_result = await event_system.start_processing()
            self.assertTrue(start_result)
            self.assertTrue(event_system.is_processing)
            
            # Test stop processing
            stop_result = await event_system.stop_processing()
            self.assertTrue(stop_result)
            self.assertFalse(event_system.is_processing)
            
            self.logger.info("✅ Event system processing test passed")
        except Exception as e:
            self.logger.error(f"❌ Event system processing test failed: {e}")
            self.fail(f"Event system processing failed: {e}")
    
    async def test_plugin_lifecycle(self):
        """Test plugin lifecycle operations."""
        try:
            plugin_system = PluginSystem()
            test_plugin = Plugin('async_test_plugin', '1.0.0')
            
            # Register plugin
            plugin_system.register_plugin(test_plugin)
            
            # Test plugin initialization
            init_result = await test_plugin.initialize()
            self.assertTrue(init_result)
            
            # Test start all plugins
            await plugin_system.start_all_plugins()
            self.assertTrue(test_plugin.enabled)
            
            # Test stop all plugins
            await plugin_system.stop_all_plugins()
            self.assertFalse(test_plugin.enabled)
            
            self.logger.info("✅ Plugin lifecycle test passed")
        except Exception as e:
            self.logger.error(f"❌ Plugin lifecycle test failed: {e}")
            self.fail(f"Plugin lifecycle failed: {e}")


def run_core_tests():
    """Run all core module tests."""
    print("\n" + "=" * 60)
    print("CORE MODULES TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestCoreModules))
    suite.addTests(loader.loadTestsFromTestCase(TestCoreModulesAsync))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 ALL CORE MODULE TESTS PASSED!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_core_tests()
    sys.exit(0 if success else 1)