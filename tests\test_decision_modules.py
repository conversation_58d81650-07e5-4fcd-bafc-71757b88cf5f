#!/usr/bin/env python3
"""
Decision Modules Test Suite

Author: inkbytefo
Description: Comprehensive tests for decision-making components including decision engine, strategy manager, signal generator, and risk assessment
"""

import asyncio
import unittest
import sys
from pathlib import Path
from unittest.mock import Mock, patch, AsyncMock, MagicMock
import json
import time
from datetime import datetime, timedelta
import threading
from enum import Enum
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Any

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from src.decision.decision_engine import DecisionEngine
    from src.decision.strategy_manager import StrategyManager, Strategy
    from src.decision.signal_generator import SignalGenerator, TradingSignal, SignalType
    from src.decision.risk_assessment import RiskAssessment, RiskLevel
    from src.decision.market_analyzer import MarketAnalyzer
    from src.utils.logger import setup_logger
except ImportError as e:
    print(f"Import error: {e}")
    print("Creating mock classes for testing...")
    
    # Create mock classes if imports fail
    class SignalType(Enum):
        BUY = "buy"
        SELL = "sell"
        HOLD = "hold"
        STRONG_BUY = "strong_buy"
        STRONG_SELL = "strong_sell"
    
    class RiskLevel(Enum):
        LOW = "low"
        MEDIUM = "medium"
        HIGH = "high"
        CRITICAL = "critical"
    
    class TradingSignal:
        def __init__(self, signal_type, symbol, confidence=0.5, timestamp=None, metadata=None):
            self.type = signal_type
            self.symbol = symbol
            self.confidence = confidence
            self.timestamp = timestamp or datetime.now()
            self.metadata = metadata or {}
            self.id = f"{symbol}_{signal_type.value}_{int(time.time() * 1000)}"
        
        def to_dict(self):
            return {
                'id': self.id,
                'type': self.type.value,
                'symbol': self.symbol,
                'confidence': self.confidence,
                'timestamp': self.timestamp.isoformat(),
                'metadata': self.metadata
            }
        
        def is_valid(self):
            return (
                self.confidence >= 0.0 and self.confidence <= 1.0 and
                self.symbol and
                self.type in SignalType
            )
    
    class Strategy:
        def __init__(self, name, parameters=None):
            self.name = name
            self.parameters = parameters or {}
            self.enabled = True
            self.performance = {
                'total_trades': 0,
                'winning_trades': 0,
                'total_return': 0.0,
                'max_drawdown': 0.0
            }
        
        async def analyze(self, market_data):
            # Mock analysis
            return TradingSignal(
                SignalType.HOLD,
                market_data.get('symbol', 'BTC/USDT'),
                confidence=0.6
            )
        
        def update_performance(self, trade_result):
            self.performance['total_trades'] += 1
            if trade_result.get('profit', 0) > 0:
                self.performance['winning_trades'] += 1
            self.performance['total_return'] += trade_result.get('profit', 0)
        
        def get_info(self):
            return {
                'name': self.name,
                'enabled': self.enabled,
                'parameters': self.parameters,
                'performance': self.performance
            }
    
    class DecisionEngine:
        def __init__(self, config=None):
            self.config = config or {}
            self.strategies = []
            self.signal_weights = {}
            self.risk_threshold = 0.7
            self.decision_history = []
            self.is_active = False
        
        def add_strategy(self, strategy, weight=1.0):
            self.strategies.append(strategy)
            self.signal_weights[strategy.name] = weight
        
        def remove_strategy(self, strategy_name):
            self.strategies = [s for s in self.strategies if s.name != strategy_name]
            if strategy_name in self.signal_weights:
                del self.signal_weights[strategy_name]
        
        async def make_decision(self, market_data):
            if not self.is_active:
                return None
            
            signals = []
            for strategy in self.strategies:
                if strategy.enabled:
                    signal = await strategy.analyze(market_data)
                    signals.append(signal)
            
            # Aggregate signals
            if signals:
                # Simple aggregation - take the signal with highest confidence
                best_signal = max(signals, key=lambda s: s.confidence)
                
                decision = {
                    'signal': best_signal,
                    'timestamp': datetime.now(),
                    'market_data': market_data,
                    'contributing_signals': len(signals)
                }
                
                self.decision_history.append(decision)
                return decision
            
            return None
        
        def get_performance_metrics(self):
            return {
                'total_decisions': len(self.decision_history),
                'active_strategies': len([s for s in self.strategies if s.enabled]),
                'average_confidence': np.mean([d['signal'].confidence for d in self.decision_history]) if self.decision_history else 0.0
            }
        
        def start(self):
            self.is_active = True
        
        def stop(self):
            self.is_active = False
    
    class StrategyManager:
        def __init__(self):
            self.strategies = {}
            self.strategy_configs = {}
            self.performance_tracker = {}
        
        def register_strategy(self, strategy):
            self.strategies[strategy.name] = strategy
            self.performance_tracker[strategy.name] = {
                'created_at': datetime.now(),
                'last_updated': datetime.now(),
                'signals_generated': 0
            }
        
        def unregister_strategy(self, strategy_name):
            if strategy_name in self.strategies:
                del self.strategies[strategy_name]
            if strategy_name in self.performance_tracker:
                del self.performance_tracker[strategy_name]
        
        def get_strategy(self, strategy_name):
            return self.strategies.get(strategy_name)
        
        def list_strategies(self):
            return list(self.strategies.keys())
        
        def enable_strategy(self, strategy_name):
            if strategy_name in self.strategies:
                self.strategies[strategy_name].enabled = True
                return True
            return False
        
        def disable_strategy(self, strategy_name):
            if strategy_name in self.strategies:
                self.strategies[strategy_name].enabled = False
                return True
            return False
        
        def get_performance_summary(self):
            summary = {}
            for name, strategy in self.strategies.items():
                summary[name] = {
                    'enabled': strategy.enabled,
                    'performance': strategy.performance,
                    'tracker': self.performance_tracker.get(name, {})
                }
            return summary
        
        async def run_all_strategies(self, market_data):
            results = {}
            for name, strategy in self.strategies.items():
                if strategy.enabled:
                    try:
                        signal = await strategy.analyze(market_data)
                        results[name] = signal
                        self.performance_tracker[name]['signals_generated'] += 1
                        self.performance_tracker[name]['last_updated'] = datetime.now()
                    except Exception as e:
                        results[name] = None
            return results
    
    class SignalGenerator:
        def __init__(self, config=None):
            self.config = config or {}
            self.indicators = {}
            self.signal_history = []
            self.thresholds = {
                'rsi_oversold': 30,
                'rsi_overbought': 70,
                'macd_signal': 0,
                'volume_spike': 2.0
            }
        
        def add_indicator(self, name, indicator_func):
            self.indicators[name] = indicator_func
        
        def calculate_rsi(self, prices, period=14):
            # Simple RSI calculation
            if len(prices) < period:
                return 50  # Neutral
            
            gains = []
            losses = []
            
            for i in range(1, len(prices)):
                change = prices[i] - prices[i-1]
                if change > 0:
                    gains.append(change)
                    losses.append(0)
                else:
                    gains.append(0)
                    losses.append(abs(change))
            
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        
        def calculate_macd(self, prices, fast=12, slow=26, signal=9):
            # Simple MACD calculation
            if len(prices) < slow:
                return 0, 0, 0
            
            ema_fast = np.mean(prices[-fast:])
            ema_slow = np.mean(prices[-slow:])
            macd_line = ema_fast - ema_slow
            signal_line = macd_line  # Simplified
            histogram = macd_line - signal_line
            
            return macd_line, signal_line, histogram
        
        async def generate_signal(self, market_data):
            symbol = market_data.get('symbol', 'BTC/USDT')
            prices = market_data.get('prices', [45000])
            volume = market_data.get('volume', 1000)
            
            # Calculate indicators
            rsi = self.calculate_rsi(prices)
            macd, macd_signal, macd_hist = self.calculate_macd(prices)
            
            # Generate signal based on indicators
            signal_type = SignalType.HOLD
            confidence = 0.5
            
            if rsi < self.thresholds['rsi_oversold'] and macd > macd_signal:
                signal_type = SignalType.BUY
                confidence = 0.8
            elif rsi > self.thresholds['rsi_overbought'] and macd < macd_signal:
                signal_type = SignalType.SELL
                confidence = 0.8
            elif rsi < 25:
                signal_type = SignalType.STRONG_BUY
                confidence = 0.9
            elif rsi > 75:
                signal_type = SignalType.STRONG_SELL
                confidence = 0.9
            
            signal = TradingSignal(
                signal_type,
                symbol,
                confidence,
                metadata={
                    'rsi': rsi,
                    'macd': macd,
                    'macd_signal': macd_signal,
                    'volume': volume
                }
            )
            
            self.signal_history.append(signal)
            return signal
        
        def get_signal_history(self, limit=None):
            if limit:
                return self.signal_history[-limit:]
            return self.signal_history
        
        def clear_history(self):
            self.signal_history.clear()
    
    class RiskAssessment:
        def __init__(self, config=None):
            self.config = config or {}
            self.risk_factors = {
                'volatility_threshold': 0.05,
                'drawdown_threshold': 0.1,
                'position_size_limit': 0.1,
                'correlation_limit': 0.8
            }
            self.risk_history = []
        
        def calculate_volatility(self, prices):
            if len(prices) < 2:
                return 0.0
            
            returns = []
            for i in range(1, len(prices)):
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
            
            return np.std(returns) if returns else 0.0
        
        def calculate_var(self, returns, confidence=0.95):
            # Value at Risk calculation
            if not returns:
                return 0.0
            
            sorted_returns = sorted(returns)
            index = int((1 - confidence) * len(sorted_returns))
            return abs(sorted_returns[index]) if index < len(sorted_returns) else 0.0
        
        async def assess_risk(self, market_data, portfolio_data=None):
            symbol = market_data.get('symbol', 'BTC/USDT')
            prices = market_data.get('prices', [45000])
            volume = market_data.get('volume', 1000)
            
            # Calculate risk metrics
            volatility = self.calculate_volatility(prices)
            
            # Determine risk level
            risk_level = RiskLevel.LOW
            risk_score = 0.0
            
            if volatility > self.risk_factors['volatility_threshold']:
                risk_level = RiskLevel.MEDIUM
                risk_score = 0.5
            
            if volatility > self.risk_factors['volatility_threshold'] * 2:
                risk_level = RiskLevel.HIGH
                risk_score = 0.8
            
            if volatility > self.risk_factors['volatility_threshold'] * 3:
                risk_level = RiskLevel.CRITICAL
                risk_score = 0.95
            
            risk_assessment = {
                'symbol': symbol,
                'risk_level': risk_level,
                'risk_score': risk_score,
                'volatility': volatility,
                'timestamp': datetime.now(),
                'factors': {
                    'price_volatility': volatility,
                    'volume_analysis': volume,
                    'market_conditions': 'normal'
                }
            }
            
            self.risk_history.append(risk_assessment)
            return risk_assessment
        
        def get_risk_summary(self):
            if not self.risk_history:
                return {'average_risk_score': 0.0, 'assessments_count': 0}
            
            avg_risk = np.mean([r['risk_score'] for r in self.risk_history])
            return {
                'average_risk_score': avg_risk,
                'assessments_count': len(self.risk_history),
                'last_assessment': self.risk_history[-1]['timestamp']
            }
        
        def is_risk_acceptable(self, risk_assessment, max_risk_score=0.7):
            return risk_assessment['risk_score'] <= max_risk_score
    
    class MarketAnalyzer:
        def __init__(self, config=None):
            self.config = config or {}
            self.analysis_history = []
            self.market_conditions = {
                'trend': 'neutral',
                'volatility': 'normal',
                'volume': 'average',
                'sentiment': 'neutral'
            }
        
        def analyze_trend(self, prices):
            if len(prices) < 3:
                return 'neutral'
            
            recent_prices = prices[-10:] if len(prices) >= 10 else prices
            
            if recent_prices[-1] > recent_prices[0] * 1.02:
                return 'bullish'
            elif recent_prices[-1] < recent_prices[0] * 0.98:
                return 'bearish'
            else:
                return 'neutral'
        
        def analyze_volume(self, volumes):
            if len(volumes) < 2:
                return 'average'
            
            avg_volume = np.mean(volumes[:-1])
            current_volume = volumes[-1]
            
            if current_volume > avg_volume * 1.5:
                return 'high'
            elif current_volume < avg_volume * 0.5:
                return 'low'
            else:
                return 'average'
        
        async def analyze_market(self, market_data):
            symbol = market_data.get('symbol', 'BTC/USDT')
            prices = market_data.get('prices', [45000])
            volumes = market_data.get('volumes', [1000])
            
            # Perform analysis
            trend = self.analyze_trend(prices)
            volume_analysis = self.analyze_volume(volumes)
            
            # Calculate support and resistance
            support = min(prices[-20:]) if len(prices) >= 20 else min(prices)
            resistance = max(prices[-20:]) if len(prices) >= 20 else max(prices)
            
            analysis = {
                'symbol': symbol,
                'timestamp': datetime.now(),
                'trend': trend,
                'volume': volume_analysis,
                'support_level': support,
                'resistance_level': resistance,
                'current_price': prices[-1] if prices else 0,
                'price_position': 'middle',  # Simplified
                'market_strength': 'moderate'
            }
            
            # Determine price position
            if prices:
                current_price = prices[-1]
                price_range = resistance - support
                if price_range > 0:
                    position = (current_price - support) / price_range
                    if position < 0.3:
                        analysis['price_position'] = 'near_support'
                    elif position > 0.7:
                        analysis['price_position'] = 'near_resistance'
            
            self.analysis_history.append(analysis)
            return analysis
        
        def get_market_summary(self):
            if not self.analysis_history:
                return self.market_conditions
            
            latest = self.analysis_history[-1]
            return {
                'trend': latest['trend'],
                'volume': latest['volume'],
                'price_position': latest['price_position'],
                'market_strength': latest['market_strength'],
                'last_analysis': latest['timestamp']
            }
    
    def setup_logger(name, level="INFO"):
        import logging
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level))
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
        return logger


class TestDecisionModules(unittest.TestCase):
    """Test suite for decision modules."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = setup_logger("test_decision_modules", level="INFO")
        
        # Mock market data
        self.mock_market_data = {
            'symbol': 'BTC/USDT',
            'prices': [44000, 44500, 45000, 45200, 45100, 44800, 44900, 45300],
            'volumes': [1000, 1200, 1100, 1300, 1150, 1050, 1250, 1400],
            'timestamp': datetime.now()
        }
        
        # Mock portfolio data
        self.mock_portfolio = {
            'total_value': 10000,
            'positions': {
                'BTC/USDT': {'size': 0.1, 'value': 4500}
            },
            'available_balance': 5500
        }
    
    def test_decision_engine_initialization(self):
        """Test decision engine initialization."""
        try:
            engine = DecisionEngine()
            self.assertIsNotNone(engine)
            self.assertIsInstance(engine.strategies, list)
            self.assertIsInstance(engine.signal_weights, dict)
            self.assertIsInstance(engine.decision_history, list)
            self.assertFalse(engine.is_active)
            self.logger.info("✅ DecisionEngine initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ DecisionEngine initialization test failed: {e}")
            self.fail(f"DecisionEngine initialization failed: {e}")
    
    def test_strategy_manager_initialization(self):
        """Test strategy manager initialization."""
        try:
            manager = StrategyManager()
            self.assertIsNotNone(manager)
            self.assertIsInstance(manager.strategies, dict)
            self.assertIsInstance(manager.strategy_configs, dict)
            self.assertIsInstance(manager.performance_tracker, dict)
            self.logger.info("✅ StrategyManager initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ StrategyManager initialization test failed: {e}")
            self.fail(f"StrategyManager initialization failed: {e}")
    
    def test_signal_generator_initialization(self):
        """Test signal generator initialization."""
        try:
            generator = SignalGenerator()
            self.assertIsNotNone(generator)
            self.assertIsInstance(generator.indicators, dict)
            self.assertIsInstance(generator.signal_history, list)
            self.assertIsInstance(generator.thresholds, dict)
            self.logger.info("✅ SignalGenerator initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ SignalGenerator initialization test failed: {e}")
            self.fail(f"SignalGenerator initialization failed: {e}")
    
    def test_risk_assessment_initialization(self):
        """Test risk assessment initialization."""
        try:
            risk_assessment = RiskAssessment()
            self.assertIsNotNone(risk_assessment)
            self.assertIsInstance(risk_assessment.risk_factors, dict)
            self.assertIsInstance(risk_assessment.risk_history, list)
            self.logger.info("✅ RiskAssessment initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ RiskAssessment initialization test failed: {e}")
            self.fail(f"RiskAssessment initialization failed: {e}")
    
    def test_market_analyzer_initialization(self):
        """Test market analyzer initialization."""
        try:
            analyzer = MarketAnalyzer()
            self.assertIsNotNone(analyzer)
            self.assertIsInstance(analyzer.analysis_history, list)
            self.assertIsInstance(analyzer.market_conditions, dict)
            self.logger.info("✅ MarketAnalyzer initialization test passed")
        except Exception as e:
            self.logger.error(f"❌ MarketAnalyzer initialization test failed: {e}")
            self.fail(f"MarketAnalyzer initialization failed: {e}")
    
    def test_strategy_registration(self):
        """Test strategy registration and management."""
        try:
            manager = StrategyManager()
            strategy = Strategy('test_strategy', {'param1': 'value1'})
            
            # Register strategy
            manager.register_strategy(strategy)
            
            # Verify registration
            self.assertIn('test_strategy', manager.strategies)
            self.assertIn('test_strategy', manager.performance_tracker)
            
            # Get strategy
            retrieved_strategy = manager.get_strategy('test_strategy')
            self.assertEqual(retrieved_strategy.name, 'test_strategy')
            
            # List strategies
            strategy_list = manager.list_strategies()
            self.assertIn('test_strategy', strategy_list)
            
            # Enable/disable strategy
            self.assertTrue(manager.enable_strategy('test_strategy'))
            self.assertTrue(strategy.enabled)
            
            self.assertTrue(manager.disable_strategy('test_strategy'))
            self.assertFalse(strategy.enabled)
            
            # Unregister strategy
            manager.unregister_strategy('test_strategy')
            self.assertNotIn('test_strategy', manager.strategies)
            
            self.logger.info("✅ Strategy registration test passed")
        except Exception as e:
            self.logger.error(f"❌ Strategy registration test failed: {e}")
            self.fail(f"Strategy registration failed: {e}")
    
    def test_signal_generation(self):
        """Test signal generation functionality."""
        try:
            generator = SignalGenerator()
            
            # Test RSI calculation
            prices = [100, 102, 101, 103, 105, 104, 106, 108, 107, 109]
            rsi = generator.calculate_rsi(prices)
            self.assertIsInstance(rsi, (int, float))
            self.assertGreaterEqual(rsi, 0)
            self.assertLessEqual(rsi, 100)
            
            # Test MACD calculation
            macd, signal, hist = generator.calculate_macd(prices)
            self.assertIsInstance(macd, (int, float))
            self.assertIsInstance(signal, (int, float))
            self.assertIsInstance(hist, (int, float))
            
            self.logger.info("✅ Signal generation test passed")
        except Exception as e:
            self.logger.error(f"❌ Signal generation test failed: {e}")
            self.fail(f"Signal generation failed: {e}")
    
    def test_risk_calculation(self):
        """Test risk calculation functionality."""
        try:
            risk_assessment = RiskAssessment()
            
            # Test volatility calculation
            prices = [100, 102, 98, 105, 95, 108, 92, 110]
            volatility = risk_assessment.calculate_volatility(prices)
            self.assertIsInstance(volatility, (int, float))
            self.assertGreaterEqual(volatility, 0)
            
            # Test VaR calculation
            returns = [0.02, -0.01, 0.03, -0.02, 0.01, -0.03, 0.04, -0.01]
            var = risk_assessment.calculate_var(returns)
            self.assertIsInstance(var, (int, float))
            self.assertGreaterEqual(var, 0)
            
            self.logger.info("✅ Risk calculation test passed")
        except Exception as e:
            self.logger.error(f"❌ Risk calculation test failed: {e}")
            self.fail(f"Risk calculation failed: {e}")
    
    def test_market_analysis(self):
        """Test market analysis functionality."""
        try:
            analyzer = MarketAnalyzer()
            
            # Test trend analysis
            bullish_prices = [100, 102, 105, 108, 110]
            trend = analyzer.analyze_trend(bullish_prices)
            self.assertIn(trend, ['bullish', 'bearish', 'neutral'])
            
            # Test volume analysis
            volumes = [1000, 1100, 1200, 2000]  # Last volume is high
            volume_analysis = analyzer.analyze_volume(volumes)
            self.assertIn(volume_analysis, ['high', 'low', 'average'])
            
            self.logger.info("✅ Market analysis test passed")
        except Exception as e:
            self.logger.error(f"❌ Market analysis test failed: {e}")
            self.fail(f"Market analysis failed: {e}")
    
    def test_trading_signal_validation(self):
        """Test trading signal validation."""
        try:
            # Valid signal
            valid_signal = TradingSignal(
                SignalType.BUY,
                'BTC/USDT',
                confidence=0.8
            )
            self.assertTrue(valid_signal.is_valid())
            
            # Invalid signal (confidence out of range)
            invalid_signal = TradingSignal(
                SignalType.SELL,
                'ETH/USDT',
                confidence=1.5  # Invalid confidence
            )
            self.assertFalse(invalid_signal.is_valid())
            
            # Test signal serialization
            signal_dict = valid_signal.to_dict()
            self.assertIsInstance(signal_dict, dict)
            self.assertIn('id', signal_dict)
            self.assertIn('type', signal_dict)
            self.assertIn('symbol', signal_dict)
            
            self.logger.info("✅ Trading signal validation test passed")
        except Exception as e:
            self.logger.error(f"❌ Trading signal validation test failed: {e}")
            self.fail(f"Trading signal validation failed: {e}")
    
    def test_decision_engine_strategy_management(self):
        """Test decision engine strategy management."""
        try:
            engine = DecisionEngine()
            strategy = Strategy('test_strategy')
            
            # Add strategy
            engine.add_strategy(strategy, weight=0.8)
            self.assertIn(strategy, engine.strategies)
            self.assertEqual(engine.signal_weights['test_strategy'], 0.8)
            
            # Remove strategy
            engine.remove_strategy('test_strategy')
            self.assertNotIn(strategy, engine.strategies)
            self.assertNotIn('test_strategy', engine.signal_weights)
            
            self.logger.info("✅ Decision engine strategy management test passed")
        except Exception as e:
            self.logger.error(f"❌ Decision engine strategy management test failed: {e}")
            self.fail(f"Decision engine strategy management failed: {e}")


class TestDecisionModulesAsync(unittest.IsolatedAsyncioTestCase):
    """Async test suite for decision modules."""
    
    async def asyncSetUp(self):
        """Set up async test fixtures."""
        self.logger = setup_logger("test_decision_modules_async", level="INFO")
        
        self.mock_market_data = {
            'symbol': 'BTC/USDT',
            'prices': [44000, 44500, 45000, 45200, 45100],
            'volumes': [1000, 1200, 1100, 1300, 1150],
            'timestamp': datetime.now()
        }
    
    async def test_signal_generation_async(self):
        """Test async signal generation."""
        try:
            generator = SignalGenerator()
            
            signal = await generator.generate_signal(self.mock_market_data)
            
            self.assertIsInstance(signal, TradingSignal)
            self.assertEqual(signal.symbol, 'BTC/USDT')
            self.assertIn(signal.type, SignalType)
            self.assertGreaterEqual(signal.confidence, 0.0)
            self.assertLessEqual(signal.confidence, 1.0)
            
            # Check signal history
            history = generator.get_signal_history()
            self.assertEqual(len(history), 1)
            self.assertEqual(history[0], signal)
            
            self.logger.info("✅ Async signal generation test passed")
        except Exception as e:
            self.logger.error(f"❌ Async signal generation test failed: {e}")
            self.fail(f"Async signal generation failed: {e}")
    
    async def test_risk_assessment_async(self):
        """Test async risk assessment."""
        try:
            risk_assessment = RiskAssessment()
            
            assessment = await risk_assessment.assess_risk(self.mock_market_data)
            
            self.assertIsInstance(assessment, dict)
            self.assertIn('symbol', assessment)
            self.assertIn('risk_level', assessment)
            self.assertIn('risk_score', assessment)
            self.assertIn('volatility', assessment)
            
            self.assertEqual(assessment['symbol'], 'BTC/USDT')
            self.assertIn(assessment['risk_level'], RiskLevel)
            self.assertGreaterEqual(assessment['risk_score'], 0.0)
            self.assertLessEqual(assessment['risk_score'], 1.0)
            
            # Test risk acceptability
            is_acceptable = risk_assessment.is_risk_acceptable(assessment)
            self.assertIsInstance(is_acceptable, bool)
            
            self.logger.info("✅ Async risk assessment test passed")
        except Exception as e:
            self.logger.error(f"❌ Async risk assessment test failed: {e}")
            self.fail(f"Async risk assessment failed: {e}")
    
    async def test_market_analysis_async(self):
        """Test async market analysis."""
        try:
            analyzer = MarketAnalyzer()
            
            analysis = await analyzer.analyze_market(self.mock_market_data)
            
            self.assertIsInstance(analysis, dict)
            self.assertIn('symbol', analysis)
            self.assertIn('trend', analysis)
            self.assertIn('volume', analysis)
            self.assertIn('support_level', analysis)
            self.assertIn('resistance_level', analysis)
            
            self.assertEqual(analysis['symbol'], 'BTC/USDT')
            self.assertIn(analysis['trend'], ['bullish', 'bearish', 'neutral'])
            self.assertIn(analysis['volume'], ['high', 'low', 'average'])
            
            # Test market summary
            summary = analyzer.get_market_summary()
            self.assertIsInstance(summary, dict)
            
            self.logger.info("✅ Async market analysis test passed")
        except Exception as e:
            self.logger.error(f"❌ Async market analysis test failed: {e}")
            self.fail(f"Async market analysis failed: {e}")
    
    async def test_strategy_execution_async(self):
        """Test async strategy execution."""
        try:
            manager = StrategyManager()
            strategy = Strategy('async_test_strategy')
            
            # Register strategy
            manager.register_strategy(strategy)
            
            # Run all strategies
            results = await manager.run_all_strategies(self.mock_market_data)
            
            self.assertIsInstance(results, dict)
            self.assertIn('async_test_strategy', results)
            
            signal = results['async_test_strategy']
            if signal:  # Strategy might return None
                self.assertIsInstance(signal, TradingSignal)
            
            # Check performance tracking
            performance = manager.get_performance_summary()
            self.assertIn('async_test_strategy', performance)
            
            self.logger.info("✅ Async strategy execution test passed")
        except Exception as e:
            self.logger.error(f"❌ Async strategy execution test failed: {e}")
            self.fail(f"Async strategy execution failed: {e}")
    
    async def test_decision_making_async(self):
        """Test async decision making process."""
        try:
            engine = DecisionEngine()
            strategy1 = Strategy('strategy1')
            strategy2 = Strategy('strategy2')
            
            # Add strategies
            engine.add_strategy(strategy1, weight=0.6)
            engine.add_strategy(strategy2, weight=0.4)
            
            # Start engine
            engine.start()
            self.assertTrue(engine.is_active)
            
            # Make decision
            decision = await engine.make_decision(self.mock_market_data)
            
            if decision:  # Decision might be None if no strategies are enabled
                self.assertIsInstance(decision, dict)
                self.assertIn('signal', decision)
                self.assertIn('timestamp', decision)
                self.assertIn('market_data', decision)
                
                signal = decision['signal']
                self.assertIsInstance(signal, TradingSignal)
            
            # Check performance metrics
            metrics = engine.get_performance_metrics()
            self.assertIsInstance(metrics, dict)
            self.assertIn('total_decisions', metrics)
            self.assertIn('active_strategies', metrics)
            
            # Stop engine
            engine.stop()
            self.assertFalse(engine.is_active)
            
            self.logger.info("✅ Async decision making test passed")
        except Exception as e:
            self.logger.error(f"❌ Async decision making test failed: {e}")
            self.fail(f"Async decision making failed: {e}")


def run_decision_tests():
    """Run all decision module tests."""
    print("\n" + "=" * 60)
    print("DECISION MODULES TEST SUITE")
    print("=" * 60)
    
    # Create test suite
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()
    
    # Add test cases
    suite.addTests(loader.loadTestsFromTestCase(TestDecisionModules))
    suite.addTests(loader.loadTestsFromTestCase(TestDecisionModulesAsync))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    if result.wasSuccessful():
        print("🎉 ALL DECISION MODULE TESTS PASSED!")
    else:
        print(f"❌ {len(result.failures)} test(s) failed, {len(result.errors)} error(s)")
        for failure in result.failures:
            print(f"FAILURE: {failure[0]}")
        for error in result.errors:
            print(f"ERROR: {error[0]}")
    print("=" * 60)
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_decision_tests()
    sys.exit(0 if success else 1)