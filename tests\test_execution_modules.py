#!/usr/bin/env python3
"""
Execution Modules Test Suite

This module contains comprehensive tests for execution-related components:
- ExecutionEngine: Main execution orchestrator
- OrderManager: Order lifecycle management
- PortfolioManager: Portfolio tracking and management
- TradeExecutor: Trade execution logic
- PositionManager: Position tracking and management

Author: inkbytefo
Date: 2025-01-05
"""

import sys
import os
import unittest
import asyncio
import logging
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from decimal import Decimal

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_execution_modules')

# Try to import actual modules, fall back to mocks if not available
try:
    from src.execution.engine import ExecutionEngine
    from src.execution.order_manager import OrderManager
    from src.execution.portfolio_manager import PortfolioManager
    from src.execution.trade_executor import TradeExecutor
    from src.execution.position_manager import PositionManager
    from src.utils.logger import setup_logger
except ImportError as e:
    logger.warning(f"Import error: {e}. Using mock classes for testing.")
    
    # Mock classes for testing
    class ExecutionEngine:
        def __init__(self, config=None):
            self.config = config or {}
            self.order_manager = Mock()
            self.portfolio_manager = Mock()
            self.trade_executor = Mock()
            self.position_manager = Mock()
            self.is_running = False
            self.orders = []
            self.trades = []
            
        async def start(self):
            self.is_running = True
            return True
            
        async def stop(self):
            self.is_running = False
            return True
            
        async def execute_order(self, order):
            self.orders.append(order)
            return {'order_id': order.get('id', 'test_order'), 'status': 'executed'}
            
        def get_portfolio_status(self):
            return {
                'total_value': 100000.0,
                'available_cash': 50000.0,
                'positions': []
            }
    
    class OrderManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.orders = {}
            self.order_counter = 0
            
        def create_order(self, symbol, side, quantity, order_type='market', price=None):
            self.order_counter += 1
            order_id = f"order_{self.order_counter}"
            order = {
                'id': order_id,
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'type': order_type,
                'price': price,
                'status': 'pending',
                'created_at': datetime.now()
            }
            self.orders[order_id] = order
            return order
            
        def cancel_order(self, order_id):
            if order_id in self.orders:
                self.orders[order_id]['status'] = 'cancelled'
                return True
            return False
            
        def get_order(self, order_id):
            return self.orders.get(order_id)
            
        def get_active_orders(self):
            return [order for order in self.orders.values() if order['status'] in ['pending', 'partial']]
    
    class PortfolioManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.cash = 100000.0
            self.positions = {}
            self.total_value = 100000.0
            
        def get_cash_balance(self):
            return self.cash
            
        def get_position(self, symbol):
            return self.positions.get(symbol, {'quantity': 0, 'avg_price': 0})
            
        def update_position(self, symbol, quantity, price):
            if symbol not in self.positions:
                self.positions[symbol] = {'quantity': 0, 'avg_price': 0}
            
            current_pos = self.positions[symbol]
            new_quantity = current_pos['quantity'] + quantity
            
            if new_quantity != 0:
                total_cost = (current_pos['quantity'] * current_pos['avg_price']) + (quantity * price)
                self.positions[symbol] = {
                    'quantity': new_quantity,
                    'avg_price': total_cost / new_quantity
                }
            else:
                self.positions[symbol] = {'quantity': 0, 'avg_price': 0}
                
        def calculate_portfolio_value(self, market_prices=None):
            market_prices = market_prices or {}
            portfolio_value = self.cash
            
            for symbol, position in self.positions.items():
                if position['quantity'] > 0:
                    price = market_prices.get(symbol, position['avg_price'])
                    portfolio_value += position['quantity'] * price
                    
            self.total_value = portfolio_value
            return portfolio_value
    
    class TradeExecutor:
        def __init__(self, config=None):
            self.config = config or {}
            self.executed_trades = []
            
        async def execute_market_order(self, order):
            # Simulate market order execution
            execution_price = 100.0  # Mock price
            trade = {
                'order_id': order['id'],
                'symbol': order['symbol'],
                'side': order['side'],
                'quantity': order['quantity'],
                'price': execution_price,
                'executed_at': datetime.now(),
                'status': 'filled'
            }
            self.executed_trades.append(trade)
            return trade
            
        async def execute_limit_order(self, order):
            # Simulate limit order execution
            trade = {
                'order_id': order['id'],
                'symbol': order['symbol'],
                'side': order['side'],
                'quantity': order['quantity'],
                'price': order['price'],
                'executed_at': datetime.now(),
                'status': 'filled'
            }
            self.executed_trades.append(trade)
            return trade
            
        def get_execution_history(self):
            return self.executed_trades
    
    class PositionManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.positions = {}
            
        def open_position(self, symbol, side, quantity, price):
            position_id = f"pos_{symbol}_{datetime.now().timestamp()}"
            position = {
                'id': position_id,
                'symbol': symbol,
                'side': side,
                'quantity': quantity,
                'entry_price': price,
                'current_price': price,
                'pnl': 0.0,
                'opened_at': datetime.now(),
                'status': 'open'
            }
            self.positions[position_id] = position
            return position
            
        def close_position(self, position_id, exit_price):
            if position_id in self.positions:
                position = self.positions[position_id]
                position['status'] = 'closed'
                position['exit_price'] = exit_price
                position['closed_at'] = datetime.now()
                
                # Calculate PnL
                if position['side'] == 'buy':
                    position['pnl'] = (exit_price - position['entry_price']) * position['quantity']
                else:
                    position['pnl'] = (position['entry_price'] - exit_price) * position['quantity']
                    
                return position
            return None
            
        def update_position_price(self, position_id, current_price):
            if position_id in self.positions:
                position = self.positions[position_id]
                position['current_price'] = current_price
                
                # Update unrealized PnL
                if position['side'] == 'buy':
                    position['pnl'] = (current_price - position['entry_price']) * position['quantity']
                else:
                    position['pnl'] = (position['entry_price'] - current_price) * position['quantity']
                    
                return position
            return None
            
        def get_open_positions(self):
            return [pos for pos in self.positions.values() if pos['status'] == 'open']
    
    def setup_logger(name):
        return logging.getLogger(name)


class TestExecutionModules(unittest.TestCase):
    """Test suite for execution module components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'execution': {
                'max_orders_per_minute': 60,
                'default_slippage': 0.001,
                'risk_limits': {
                    'max_position_size': 10000,
                    'max_daily_loss': 5000
                }
            }
        }
        
    def test_execution_engine_initialization(self):
        """Test ExecutionEngine initialization."""
        engine = ExecutionEngine(self.config)
        
        self.assertIsNotNone(engine)
        self.assertEqual(engine.config, self.config)
        self.assertFalse(engine.is_running)
        
        logger.info("✅ ExecutionEngine initialization test passed")
        
    def test_order_manager_initialization(self):
        """Test OrderManager initialization."""
        order_manager = OrderManager(self.config)
        
        self.assertIsNotNone(order_manager)
        self.assertEqual(order_manager.config, self.config)
        self.assertEqual(len(order_manager.orders), 0)
        
        logger.info("✅ OrderManager initialization test passed")
        
    def test_portfolio_manager_initialization(self):
        """Test PortfolioManager initialization."""
        portfolio_manager = PortfolioManager(self.config)
        
        self.assertIsNotNone(portfolio_manager)
        self.assertEqual(portfolio_manager.config, self.config)
        self.assertGreater(portfolio_manager.cash, 0)
        
        logger.info("✅ PortfolioManager initialization test passed")
        
    def test_trade_executor_initialization(self):
        """Test TradeExecutor initialization."""
        trade_executor = TradeExecutor(self.config)
        
        self.assertIsNotNone(trade_executor)
        self.assertEqual(trade_executor.config, self.config)
        self.assertEqual(len(trade_executor.executed_trades), 0)
        
        logger.info("✅ TradeExecutor initialization test passed")
        
    def test_position_manager_initialization(self):
        """Test PositionManager initialization."""
        position_manager = PositionManager(self.config)
        
        self.assertIsNotNone(position_manager)
        self.assertEqual(position_manager.config, self.config)
        self.assertEqual(len(position_manager.positions), 0)
        
        logger.info("✅ PositionManager initialization test passed")
        
    def test_order_creation_and_management(self):
        """Test order creation and management."""
        order_manager = OrderManager(self.config)
        
        # Create a market order
        order = order_manager.create_order('BTCUSDT', 'buy', 1.0, 'market')
        
        self.assertIsNotNone(order)
        self.assertEqual(order['symbol'], 'BTCUSDT')
        self.assertEqual(order['side'], 'buy')
        self.assertEqual(order['quantity'], 1.0)
        self.assertEqual(order['type'], 'market')
        self.assertEqual(order['status'], 'pending')
        
        # Test order retrieval
        retrieved_order = order_manager.get_order(order['id'])
        self.assertEqual(retrieved_order, order)
        
        # Test active orders
        active_orders = order_manager.get_active_orders()
        self.assertEqual(len(active_orders), 1)
        
        # Test order cancellation
        cancelled = order_manager.cancel_order(order['id'])
        self.assertTrue(cancelled)
        self.assertEqual(order_manager.get_order(order['id'])['status'], 'cancelled')
        
        logger.info("✅ Order creation and management test passed")
        
    def test_portfolio_management(self):
        """Test portfolio management functionality."""
        portfolio_manager = PortfolioManager(self.config)
        
        # Test initial cash balance
        initial_cash = portfolio_manager.get_cash_balance()
        self.assertGreater(initial_cash, 0)
        
        # Test position updates
        portfolio_manager.update_position('BTCUSDT', 1.0, 50000.0)
        position = portfolio_manager.get_position('BTCUSDT')
        
        self.assertEqual(position['quantity'], 1.0)
        self.assertEqual(position['avg_price'], 50000.0)
        
        # Test portfolio value calculation
        market_prices = {'BTCUSDT': 55000.0}
        portfolio_value = portfolio_manager.calculate_portfolio_value(market_prices)
        
        expected_value = initial_cash + (1.0 * 55000.0)
        self.assertEqual(portfolio_value, expected_value)
        
        logger.info("✅ Portfolio management test passed")
        
    def test_position_lifecycle(self):
        """Test position lifecycle management."""
        position_manager = PositionManager(self.config)
        
        # Open a position
        position = position_manager.open_position('BTCUSDT', 'buy', 1.0, 50000.0)
        
        self.assertIsNotNone(position)
        self.assertEqual(position['symbol'], 'BTCUSDT')
        self.assertEqual(position['side'], 'buy')
        self.assertEqual(position['quantity'], 1.0)
        self.assertEqual(position['entry_price'], 50000.0)
        self.assertEqual(position['status'], 'open')
        
        # Update position price
        updated_position = position_manager.update_position_price(position['id'], 55000.0)
        self.assertEqual(updated_position['current_price'], 55000.0)
        self.assertEqual(updated_position['pnl'], 5000.0)  # (55000 - 50000) * 1.0
        
        # Close position
        closed_position = position_manager.close_position(position['id'], 55000.0)
        self.assertEqual(closed_position['status'], 'closed')
        self.assertEqual(closed_position['exit_price'], 55000.0)
        self.assertEqual(closed_position['pnl'], 5000.0)
        
        logger.info("✅ Position lifecycle test passed")
        
    def test_trade_execution_validation(self):
        """Test trade execution validation."""
        order_manager = OrderManager(self.config)
        portfolio_manager = PortfolioManager(self.config)
        
        # Create and validate order
        order = order_manager.create_order('BTCUSDT', 'buy', 1.0, 'market')
        
        # Check if sufficient funds
        cash_balance = portfolio_manager.get_cash_balance()
        estimated_cost = 1.0 * 50000.0  # Assuming 50k price
        
        self.assertGreater(cash_balance, estimated_cost)
        
        # Validate order parameters
        self.assertIn(order['side'], ['buy', 'sell'])
        self.assertGreater(order['quantity'], 0)
        self.assertIn(order['type'], ['market', 'limit'])
        
        logger.info("✅ Trade execution validation test passed")


class TestExecutionModulesAsync(unittest.TestCase):
    """Test suite for async execution module operations."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'execution': {
                'max_orders_per_minute': 60,
                'default_slippage': 0.001
            }
        }
        self.logger = setup_logger('test_execution_modules_async')
        
    async def test_execution_engine_lifecycle_async(self):
        """Test async execution engine lifecycle."""
        engine = ExecutionEngine(self.config)
        
        # Test engine startup
        started = await engine.start()
        self.assertTrue(started)
        self.assertTrue(engine.is_running)
        
        # Test order execution
        order = {
            'id': 'test_order_1',
            'symbol': 'BTCUSDT',
            'side': 'buy',
            'quantity': 1.0,
            'type': 'market'
        }
        
        result = await engine.execute_order(order)
        self.assertIsNotNone(result)
        self.assertEqual(result['order_id'], 'test_order_1')
        self.assertEqual(result['status'], 'executed')
        
        # Test engine shutdown
        stopped = await engine.stop()
        self.assertTrue(stopped)
        self.assertFalse(engine.is_running)
        
        self.logger.info("✅ Async execution engine lifecycle test passed")
        
    async def test_trade_execution_async(self):
        """Test async trade execution."""
        trade_executor = TradeExecutor(self.config)
        
        # Test market order execution
        market_order = {
            'id': 'market_order_1',
            'symbol': 'BTCUSDT',
            'side': 'buy',
            'quantity': 1.0,
            'type': 'market'
        }
        
        trade = await trade_executor.execute_market_order(market_order)
        
        self.assertIsNotNone(trade)
        self.assertEqual(trade['order_id'], 'market_order_1')
        self.assertEqual(trade['status'], 'filled')
        self.assertGreater(trade['price'], 0)
        
        # Test limit order execution
        limit_order = {
            'id': 'limit_order_1',
            'symbol': 'BTCUSDT',
            'side': 'sell',
            'quantity': 0.5,
            'type': 'limit',
            'price': 55000.0
        }
        
        trade = await trade_executor.execute_limit_order(limit_order)
        
        self.assertIsNotNone(trade)
        self.assertEqual(trade['order_id'], 'limit_order_1')
        self.assertEqual(trade['status'], 'filled')
        self.assertEqual(trade['price'], 55000.0)
        
        # Check execution history
        history = trade_executor.get_execution_history()
        self.assertEqual(len(history), 2)
        
        self.logger.info("✅ Async trade execution test passed")
        
    def test_async_execution_suite(self):
        """Run all async tests."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(self.test_execution_engine_lifecycle_async())
            loop.run_until_complete(self.test_trade_execution_async())
        finally:
            loop.close()


if __name__ == '__main__':
    print("\n" + "="*60)
    print("🚀 STARTING EXECUTION MODULES TEST SUITE")
    print("="*60)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add synchronous tests
    suite.addTest(TestExecutionModules('test_execution_engine_initialization'))
    suite.addTest(TestExecutionModules('test_order_manager_initialization'))
    suite.addTest(TestExecutionModules('test_portfolio_manager_initialization'))
    suite.addTest(TestExecutionModules('test_trade_executor_initialization'))
    suite.addTest(TestExecutionModules('test_position_manager_initialization'))
    suite.addTest(TestExecutionModules('test_order_creation_and_management'))
    suite.addTest(TestExecutionModules('test_portfolio_management'))
    suite.addTest(TestExecutionModules('test_position_lifecycle'))
    suite.addTest(TestExecutionModules('test_trade_execution_validation'))
    
    # Add asynchronous tests
    suite.addTest(TestExecutionModulesAsync('test_async_execution_suite'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print results
    if result.wasSuccessful():
        print("\n" + "="*60)
        print("🎉 ALL EXECUTION MODULE TESTS PASSED!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ SOME EXECUTION MODULE TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        print("="*60)