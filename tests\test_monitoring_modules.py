#!/usr/bin/env python3
"""
Monitoring Modules Test Suite

This module contains comprehensive tests for monitoring-related components:
- PerformanceMonitor: System and trading performance monitoring
- SystemHealthMonitor: System health and resource monitoring
- AlertManager: Alert generation and notification management
- MetricsCollector: Metrics collection and aggregation
- DashboardManager: Dashboard data management

Author: inkbytefo
Date: 2025-01-05
"""

import sys
import os
import unittest
import asyncio
import logging
import time
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
from collections import defaultdict

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_monitoring_modules')

# Try to import actual modules, fall back to mocks if not available
try:
    from src.monitoring.performance_monitor import PerformanceMonitor
    from src.monitoring.system_health import SystemHealthMonitor
    from src.monitoring.alert_manager import AlertManager
    from src.monitoring.metrics_collector import MetricsCollector
    from src.monitoring.dashboard_manager import DashboardManager
    from src.utils.logger import setup_logger
except ImportError as e:
    logger.warning(f"Import error: {e}. Using mock classes for testing.")
    
    # Mock classes for testing
    class PerformanceMonitor:
        def __init__(self, config=None):
            self.config = config or {}
            self.metrics = defaultdict(list)
            self.start_time = datetime.now()
            self.is_monitoring = False
            
        def start_monitoring(self):
            self.is_monitoring = True
            self.start_time = datetime.now()
            return True
            
        def stop_monitoring(self):
            self.is_monitoring = False
            return True
            
        def record_trade_performance(self, trade_data):
            self.metrics['trades'].append({
                'timestamp': datetime.now(),
                'symbol': trade_data.get('symbol'),
                'pnl': trade_data.get('pnl', 0),
                'execution_time': trade_data.get('execution_time', 0.1)
            })
            
        def calculate_sharpe_ratio(self, returns=None):
            # Mock Sharpe ratio calculation
            returns = returns or [0.01, 0.02, -0.005, 0.015, 0.008]
            if not returns:
                return 0.0
            
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            std_dev = variance ** 0.5
            
            return mean_return / std_dev if std_dev > 0 else 0.0
            
        def get_performance_summary(self):
            return {
                'total_trades': len(self.metrics['trades']),
                'total_pnl': sum(trade['pnl'] for trade in self.metrics['trades']),
                'avg_execution_time': 0.1,
                'sharpe_ratio': self.calculate_sharpe_ratio(),
                'uptime': (datetime.now() - self.start_time).total_seconds()
            }
    
    class SystemHealthMonitor:
        def __init__(self, config=None):
            self.config = config or {}
            self.health_status = 'healthy'
            self.resource_usage = {
                'cpu_percent': 25.0,
                'memory_percent': 45.0,
                'disk_percent': 60.0
            }
            self.is_monitoring = False
            
        def start_monitoring(self):
            self.is_monitoring = True
            return True
            
        def stop_monitoring(self):
            self.is_monitoring = False
            return True
            
        def check_system_health(self):
            # Mock system health check
            cpu_ok = self.resource_usage['cpu_percent'] < 80
            memory_ok = self.resource_usage['memory_percent'] < 85
            disk_ok = self.resource_usage['disk_percent'] < 90
            
            if cpu_ok and memory_ok and disk_ok:
                self.health_status = 'healthy'
            elif cpu_ok and memory_ok:
                self.health_status = 'warning'
            else:
                self.health_status = 'critical'
                
            return self.health_status
            
        def get_resource_usage(self):
            return self.resource_usage.copy()
            
        def get_system_info(self):
            return {
                'platform': 'Windows',
                'python_version': '3.9.0',
                'memory_total': '16GB',
                'cpu_cores': 8,
                'disk_space': '500GB'
            }
    
    class AlertManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.alerts = []
            self.alert_rules = []
            self.notification_channels = ['email', 'slack']
            
        def add_alert_rule(self, rule):
            self.alert_rules.append(rule)
            return True
            
        def create_alert(self, alert_type, message, severity='medium'):
            alert = {
                'id': f"alert_{len(self.alerts) + 1}",
                'type': alert_type,
                'message': message,
                'severity': severity,
                'timestamp': datetime.now(),
                'status': 'active'
            }
            self.alerts.append(alert)
            return alert
            
        def resolve_alert(self, alert_id):
            for alert in self.alerts:
                if alert['id'] == alert_id:
                    alert['status'] = 'resolved'
                    alert['resolved_at'] = datetime.now()
                    return True
            return False
            
        def get_active_alerts(self):
            return [alert for alert in self.alerts if alert['status'] == 'active']
            
        def send_notification(self, alert, channel='email'):
            # Mock notification sending
            return {
                'success': True,
                'channel': channel,
                'alert_id': alert['id'],
                'sent_at': datetime.now()
            }
    
    class MetricsCollector:
        def __init__(self, config=None):
            self.config = config or {}
            self.metrics = defaultdict(list)
            self.collection_interval = 60  # seconds
            self.is_collecting = False
            
        def start_collection(self):
            self.is_collecting = True
            return True
            
        def stop_collection(self):
            self.is_collecting = False
            return True
            
        def collect_metric(self, metric_name, value, tags=None):
            metric_entry = {
                'timestamp': datetime.now(),
                'value': value,
                'tags': tags or {}
            }
            self.metrics[metric_name].append(metric_entry)
            
        def get_metric_history(self, metric_name, time_range=None):
            if metric_name not in self.metrics:
                return []
                
            metrics = self.metrics[metric_name]
            
            if time_range:
                start_time = datetime.now() - timedelta(seconds=time_range)
                metrics = [m for m in metrics if m['timestamp'] >= start_time]
                
            return metrics
            
        def calculate_metric_stats(self, metric_name):
            values = [m['value'] for m in self.metrics[metric_name]]
            if not values:
                return {}
                
            return {
                'count': len(values),
                'min': min(values),
                'max': max(values),
                'avg': sum(values) / len(values),
                'latest': values[-1] if values else None
            }
    
    class DashboardManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.dashboard_data = {}
            self.widgets = []
            self.update_interval = 30  # seconds
            
        def add_widget(self, widget_config):
            widget = {
                'id': f"widget_{len(self.widgets) + 1}",
                'type': widget_config.get('type', 'chart'),
                'title': widget_config.get('title', 'Untitled'),
                'data_source': widget_config.get('data_source'),
                'config': widget_config
            }
            self.widgets.append(widget)
            return widget
            
        def update_dashboard_data(self, data_updates):
            self.dashboard_data.update(data_updates)
            self.dashboard_data['last_updated'] = datetime.now()
            
        def get_dashboard_data(self):
            return {
                'widgets': self.widgets,
                'data': self.dashboard_data,
                'last_updated': self.dashboard_data.get('last_updated', datetime.now())
            }
            
        def generate_report(self, report_type='summary'):
            return {
                'type': report_type,
                'generated_at': datetime.now(),
                'data': self.dashboard_data,
                'widget_count': len(self.widgets)
            }
    
    def setup_logger(name):
        return logging.getLogger(name)


class TestMonitoringModules(unittest.TestCase):
    """Test suite for monitoring module components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'monitoring': {
                'performance': {
                    'collection_interval': 60,
                    'metrics_retention': 86400  # 24 hours
                },
                'alerts': {
                    'cpu_threshold': 80,
                    'memory_threshold': 85,
                    'disk_threshold': 90
                }
            }
        }
        
    def test_performance_monitor_initialization(self):
        """Test PerformanceMonitor initialization."""
        monitor = PerformanceMonitor(self.config)
        
        self.assertIsNotNone(monitor)
        self.assertEqual(monitor.config, self.config)
        self.assertFalse(monitor.is_monitoring)
        
        logger.info("✅ PerformanceMonitor initialization test passed")
        
    def test_system_health_monitor_initialization(self):
        """Test SystemHealthMonitor initialization."""
        health_monitor = SystemHealthMonitor(self.config)
        
        self.assertIsNotNone(health_monitor)
        self.assertEqual(health_monitor.config, self.config)
        self.assertFalse(health_monitor.is_monitoring)
        
        logger.info("✅ SystemHealthMonitor initialization test passed")
        
    def test_alert_manager_initialization(self):
        """Test AlertManager initialization."""
        alert_manager = AlertManager(self.config)
        
        self.assertIsNotNone(alert_manager)
        self.assertEqual(alert_manager.config, self.config)
        self.assertEqual(len(alert_manager.alerts), 0)
        
        logger.info("✅ AlertManager initialization test passed")
        
    def test_metrics_collector_initialization(self):
        """Test MetricsCollector initialization."""
        metrics_collector = MetricsCollector(self.config)
        
        self.assertIsNotNone(metrics_collector)
        self.assertEqual(metrics_collector.config, self.config)
        self.assertFalse(metrics_collector.is_collecting)
        
        logger.info("✅ MetricsCollector initialization test passed")
        
    def test_dashboard_manager_initialization(self):
        """Test DashboardManager initialization."""
        dashboard_manager = DashboardManager(self.config)
        
        self.assertIsNotNone(dashboard_manager)
        self.assertEqual(dashboard_manager.config, self.config)
        self.assertEqual(len(dashboard_manager.widgets), 0)
        
        logger.info("✅ DashboardManager initialization test passed")
        
    def test_performance_monitoring(self):
        """Test performance monitoring functionality."""
        monitor = PerformanceMonitor(self.config)
        
        # Start monitoring
        started = monitor.start_monitoring()
        self.assertTrue(started)
        self.assertTrue(monitor.is_monitoring)
        
        # Record trade performance
        trade_data = {
            'symbol': 'BTCUSDT',
            'pnl': 150.0,
            'execution_time': 0.05
        }
        monitor.record_trade_performance(trade_data)
        
        # Get performance summary
        summary = monitor.get_performance_summary()
        self.assertEqual(summary['total_trades'], 1)
        self.assertEqual(summary['total_pnl'], 150.0)
        self.assertGreater(summary['sharpe_ratio'], 0)
        
        # Stop monitoring
        stopped = monitor.stop_monitoring()
        self.assertTrue(stopped)
        self.assertFalse(monitor.is_monitoring)
        
        logger.info("✅ Performance monitoring test passed")
        
    def test_system_health_monitoring(self):
        """Test system health monitoring."""
        health_monitor = SystemHealthMonitor(self.config)
        
        # Start monitoring
        started = health_monitor.start_monitoring()
        self.assertTrue(started)
        
        # Check system health
        health_status = health_monitor.check_system_health()
        self.assertIn(health_status, ['healthy', 'warning', 'critical'])
        
        # Get resource usage
        resource_usage = health_monitor.get_resource_usage()
        self.assertIn('cpu_percent', resource_usage)
        self.assertIn('memory_percent', resource_usage)
        self.assertIn('disk_percent', resource_usage)
        
        # Get system info
        system_info = health_monitor.get_system_info()
        self.assertIn('platform', system_info)
        self.assertIn('python_version', system_info)
        
        logger.info("✅ System health monitoring test passed")
        
    def test_alert_management(self):
        """Test alert management functionality."""
        alert_manager = AlertManager(self.config)
        
        # Create alert
        alert = alert_manager.create_alert(
            'system_warning',
            'High CPU usage detected',
            'high'
        )
        
        self.assertIsNotNone(alert)
        self.assertEqual(alert['type'], 'system_warning')
        self.assertEqual(alert['severity'], 'high')
        self.assertEqual(alert['status'], 'active')
        
        # Check active alerts
        active_alerts = alert_manager.get_active_alerts()
        self.assertEqual(len(active_alerts), 1)
        
        # Send notification
        notification_result = alert_manager.send_notification(alert, 'email')
        self.assertTrue(notification_result['success'])
        self.assertEqual(notification_result['channel'], 'email')
        
        # Resolve alert
        resolved = alert_manager.resolve_alert(alert['id'])
        self.assertTrue(resolved)
        
        # Check active alerts after resolution
        active_alerts = alert_manager.get_active_alerts()
        self.assertEqual(len(active_alerts), 0)
        
        logger.info("✅ Alert management test passed")
        
    def test_metrics_collection(self):
        """Test metrics collection functionality."""
        metrics_collector = MetricsCollector(self.config)
        
        # Start collection
        started = metrics_collector.start_collection()
        self.assertTrue(started)
        
        # Collect metrics
        metrics_collector.collect_metric('cpu_usage', 45.5, {'host': 'trading-server'})
        metrics_collector.collect_metric('cpu_usage', 50.2, {'host': 'trading-server'})
        metrics_collector.collect_metric('memory_usage', 60.0, {'host': 'trading-server'})
        
        # Get metric history
        cpu_history = metrics_collector.get_metric_history('cpu_usage')
        self.assertEqual(len(cpu_history), 2)
        
        # Calculate metric stats
        cpu_stats = metrics_collector.calculate_metric_stats('cpu_usage')
        self.assertEqual(cpu_stats['count'], 2)
        self.assertEqual(cpu_stats['min'], 45.5)
        self.assertEqual(cpu_stats['max'], 50.2)
        
        logger.info("✅ Metrics collection test passed")
        
    def test_dashboard_management(self):
        """Test dashboard management functionality."""
        dashboard_manager = DashboardManager(self.config)
        
        # Add widget
        widget_config = {
            'type': 'line_chart',
            'title': 'CPU Usage',
            'data_source': 'cpu_metrics'
        }
        widget = dashboard_manager.add_widget(widget_config)
        
        self.assertIsNotNone(widget)
        self.assertEqual(widget['type'], 'line_chart')
        self.assertEqual(widget['title'], 'CPU Usage')
        
        # Update dashboard data
        data_updates = {
            'cpu_metrics': [45.5, 50.2, 48.1],
            'memory_metrics': [60.0, 62.5, 58.9]
        }
        dashboard_manager.update_dashboard_data(data_updates)
        
        # Get dashboard data
        dashboard_data = dashboard_manager.get_dashboard_data()
        self.assertEqual(len(dashboard_data['widgets']), 1)
        self.assertIn('cpu_metrics', dashboard_data['data'])
        self.assertIn('last_updated', dashboard_data['data'])
        
        # Generate report
        report = dashboard_manager.generate_report('performance')
        self.assertEqual(report['type'], 'performance')
        self.assertEqual(report['widget_count'], 1)
        
        logger.info("✅ Dashboard management test passed")


class TestMonitoringModulesAsync(unittest.TestCase):
    """Test suite for async monitoring module operations."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'monitoring': {
                'async_interval': 30,
                'batch_size': 100
            }
        }
        self.logger = setup_logger('test_monitoring_modules_async')
        
    async def test_async_performance_monitoring(self):
        """Test async performance monitoring."""
        monitor = PerformanceMonitor(self.config)
        
        # Start monitoring
        started = monitor.start_monitoring()
        self.assertTrue(started)
        
        # Simulate async trade recording
        trades = [
            {'symbol': 'BTCUSDT', 'pnl': 100.0, 'execution_time': 0.05},
            {'symbol': 'ETHUSDT', 'pnl': -50.0, 'execution_time': 0.08},
            {'symbol': 'ADAUSDT', 'pnl': 75.0, 'execution_time': 0.06}
        ]
        
        for trade in trades:
            monitor.record_trade_performance(trade)
            await asyncio.sleep(0.01)  # Simulate async delay
            
        # Get performance summary
        summary = monitor.get_performance_summary()
        self.assertEqual(summary['total_trades'], 3)
        self.assertEqual(summary['total_pnl'], 125.0)
        
        self.logger.info("✅ Async performance monitoring test passed")
        
    async def test_async_alert_processing(self):
        """Test async alert processing."""
        alert_manager = AlertManager(self.config)
        
        # Create multiple alerts
        alerts = [
            ('cpu_warning', 'High CPU usage', 'medium'),
            ('memory_warning', 'High memory usage', 'high'),
            ('disk_warning', 'Low disk space', 'critical')
        ]
        
        created_alerts = []
        for alert_type, message, severity in alerts:
            alert = alert_manager.create_alert(alert_type, message, severity)
            created_alerts.append(alert)
            await asyncio.sleep(0.01)  # Simulate async delay
            
        # Process notifications asynchronously
        notification_results = []
        for alert in created_alerts:
            result = alert_manager.send_notification(alert, 'slack')
            notification_results.append(result)
            await asyncio.sleep(0.01)  # Simulate async delay
            
        # Verify all notifications were sent
        self.assertEqual(len(notification_results), 3)
        for result in notification_results:
            self.assertTrue(result['success'])
            self.assertEqual(result['channel'], 'slack')
            
        self.logger.info("✅ Async alert processing test passed")
        
    def test_async_monitoring_suite(self):
        """Run all async tests."""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        
        try:
            loop.run_until_complete(self.test_async_performance_monitoring())
            loop.run_until_complete(self.test_async_alert_processing())
        finally:
            loop.close()


if __name__ == '__main__':
    print("\n" + "="*60)
    print("🚀 STARTING MONITORING MODULES TEST SUITE")
    print("="*60)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add synchronous tests
    suite.addTest(TestMonitoringModules('test_performance_monitor_initialization'))
    suite.addTest(TestMonitoringModules('test_system_health_monitor_initialization'))
    suite.addTest(TestMonitoringModules('test_alert_manager_initialization'))
    suite.addTest(TestMonitoringModules('test_metrics_collector_initialization'))
    suite.addTest(TestMonitoringModules('test_dashboard_manager_initialization'))
    suite.addTest(TestMonitoringModules('test_performance_monitoring'))
    suite.addTest(TestMonitoringModules('test_system_health_monitoring'))
    suite.addTest(TestMonitoringModules('test_alert_management'))
    suite.addTest(TestMonitoringModules('test_metrics_collection'))
    suite.addTest(TestMonitoringModules('test_dashboard_management'))
    
    # Add asynchronous tests
    suite.addTest(TestMonitoringModulesAsync('test_async_monitoring_suite'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print results
    if result.wasSuccessful():
        print("\n" + "="*60)
        print("🎉 ALL MONITORING MODULE TESTS PASSED!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ SOME MONITORING MODULE TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        print("="*60)