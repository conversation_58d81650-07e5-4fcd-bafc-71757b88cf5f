#!/usr/bin/env python3
"""
Utils Modules Test Suite

This module contains comprehensive tests for utility components:
- Logger utilities: Logging configuration and management
- Configuration manager: Configuration loading and validation
- Database utilities: Database connection and operations
- File utilities: File operations and management
- Network utilities: Network operations and validation
- Validation utilities: Data validation and sanitization

Author: inkbytefo
Date: 2025-01-05
"""

import sys
import os
import unittest
import asyncio
import logging
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('test_utils_modules')

# Try to import actual modules, fall back to mocks if not available
try:
    from src.utils.logger import setup_logger, LoggerConfig
    from src.utils.config_manager import ConfigurationManager
    from src.utils.database import DatabaseManager
    from src.utils.file_utils import FileManager
    from src.utils.network_utils import NetworkManager
    from src.utils.validation import ValidationManager
except ImportError as e:
    logger.warning(f"Import error: {e}. Using mock classes for testing.")
    
    # Mock classes for testing
    class LoggerConfig:
        def __init__(self, level='INFO', format_string=None, file_path=None):
            self.level = level
            self.format_string = format_string or '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            self.file_path = file_path
            
    def setup_logger(name, config=None):
        config = config or LoggerConfig()
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, config.level))
        
        # Add handler if not exists
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(config.format_string)
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            
        return logger
    
    class ConfigurationManager:
        def __init__(self, config_path=None):
            self.config_path = config_path
            self.config_data = {
                'database': {
                    'host': 'localhost',
                    'port': 5432,
                    'name': 'trading_db',
                    'user': 'trader',
                    'password': 'secret'
                },
                'api': {
                    'base_url': 'https://api.example.com',
                    'timeout': 30,
                    'rate_limit': 100
                },
                'trading': {
                    'max_position_size': 10000,
                    'risk_limit': 0.02,
                    'stop_loss': 0.05
                }
            }
            
        def load_config(self, config_path=None):
            # Mock config loading
            return self.config_data
            
        def get_config(self, key_path, default=None):
            keys = key_path.split('.')
            value = self.config_data
            
            for key in keys:
                if isinstance(value, dict) and key in value:
                    value = value[key]
                else:
                    return default
                    
            return value
            
        def set_config(self, key_path, value):
            keys = key_path.split('.')
            config = self.config_data
            
            for key in keys[:-1]:
                if key not in config:
                    config[key] = {}
                config = config[key]
                
            config[keys[-1]] = value
            return True
            
        def validate_config(self):
            required_keys = [
                'database.host',
                'database.port',
                'api.base_url',
                'trading.max_position_size'
            ]
            
            for key in required_keys:
                if self.get_config(key) is None:
                    return False, f"Missing required config: {key}"
                    
            return True, "Configuration is valid"
            
        def save_config(self, config_path=None):
            # Mock config saving
            return True
    
    class DatabaseManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.connection = None
            self.is_connected = False
            self.tables = ['trades', 'orders', 'positions', 'metrics']
            
        def connect(self):
            # Mock database connection
            self.is_connected = True
            self.connection = Mock()
            return True
            
        def disconnect(self):
            self.is_connected = False
            self.connection = None
            return True
            
        def execute_query(self, query, params=None):
            if not self.is_connected:
                raise Exception("Database not connected")
                
            # Mock query execution
            if query.lower().startswith('select'):
                return [
                    {'id': 1, 'symbol': 'BTCUSDT', 'price': 50000.0},
                    {'id': 2, 'symbol': 'ETHUSDT', 'price': 3000.0}
                ]
            elif query.lower().startswith('insert'):
                return {'inserted_id': 123, 'affected_rows': 1}
            elif query.lower().startswith('update'):
                return {'affected_rows': 1}
            elif query.lower().startswith('delete'):
                return {'affected_rows': 1}
            else:
                return {'status': 'success'}
                
        def create_table(self, table_name, schema):
            if table_name not in self.tables:
                self.tables.append(table_name)
            return True
            
        def table_exists(self, table_name):
            return table_name in self.tables
            
        def get_table_info(self, table_name):
            if table_name in self.tables:
                return {
                    'name': table_name,
                    'columns': ['id', 'created_at', 'updated_at'],
                    'row_count': 100
                }
            return None
    
    class FileManager:
        def __init__(self, base_path=None):
            self.base_path = base_path or os.getcwd()
            
        def read_file(self, file_path, encoding='utf-8'):
            # Mock file reading
            if file_path.endswith('.json'):
                return '{"test": "data", "value": 123}'
            elif file_path.endswith('.csv'):
                return 'symbol,price,volume\nBTCUSDT,50000,1.5\nETHUSDT,3000,2.0'
            else:
                return 'Mock file content'
                
        def write_file(self, file_path, content, encoding='utf-8'):
            # Mock file writing
            return True
            
        def file_exists(self, file_path):
            # Mock file existence check
            return file_path in ['config.json', 'data.csv', 'log.txt']
            
        def create_directory(self, dir_path):
            # Mock directory creation
            return True
            
        def list_files(self, directory, pattern=None):
            # Mock file listing
            mock_files = ['config.json', 'data.csv', 'log.txt', 'backup.zip']
            if pattern:
                return [f for f in mock_files if pattern in f]
            return mock_files
            
        def get_file_info(self, file_path):
            return {
                'size': 1024,
                'created': datetime.now(),
                'modified': datetime.now(),
                'extension': Path(file_path).suffix
            }
            
        def backup_file(self, file_path, backup_dir=None):
            backup_path = f"{file_path}.backup"
            return backup_path
    
    class NetworkManager:
        def __init__(self, config=None):
            self.config = config or {}
            self.timeout = self.config.get('timeout', 30)
            self.retry_count = self.config.get('retry_count', 3)
            
        def make_request(self, method, url, headers=None, data=None, timeout=None):
            # Mock HTTP request
            timeout = timeout or self.timeout
            
            mock_response = {
                'status_code': 200,
                'headers': {'Content-Type': 'application/json'},
                'data': {'success': True, 'message': 'Mock response'},
                'url': url,
                'method': method
            }
            
            if 'error' in url:
                mock_response['status_code'] = 500
                mock_response['data'] = {'error': 'Mock error'}
                
            return mock_response
            
        def download_file(self, url, local_path):
            # Mock file download
            return {
                'success': True,
                'local_path': local_path,
                'size': 2048,
                'url': url
            }
            
        def check_connectivity(self, host='*******', port=53, timeout=5):
            # Mock connectivity check
            return True
            
        def validate_url(self, url):
            # Mock URL validation
            return url.startswith(('http://', 'https://'))
            
        def get_ip_info(self):
            return {
                'public_ip': '***********',
                'local_ip': '*************',
                'hostname': 'trading-server'
            }
    
    class ValidationManager:
        def __init__(self):
            self.validation_rules = {}
            
        def add_rule(self, field_name, rule_type, **kwargs):
            if field_name not in self.validation_rules:
                self.validation_rules[field_name] = []
                
            rule = {'type': rule_type, 'params': kwargs}
            self.validation_rules[field_name].append(rule)
            
        def validate_data(self, data):
            errors = []
            
            for field_name, rules in self.validation_rules.items():
                if field_name not in data:
                    errors.append(f"Missing required field: {field_name}")
                    continue
                    
                value = data[field_name]
                
                for rule in rules:
                    error = self._apply_rule(field_name, value, rule)
                    if error:
                        errors.append(error)
                        
            return len(errors) == 0, errors
            
        def _apply_rule(self, field_name, value, rule):
            rule_type = rule['type']
            params = rule['params']
            
            if rule_type == 'required' and not value:
                return f"{field_name} is required"
            elif rule_type == 'type' and not isinstance(value, params['expected_type']):
                return f"{field_name} must be of type {params['expected_type'].__name__}"
            elif rule_type == 'range' and not (params['min'] <= value <= params['max']):
                return f"{field_name} must be between {params['min']} and {params['max']}"
            elif rule_type == 'length' and len(str(value)) > params['max_length']:
                return f"{field_name} must not exceed {params['max_length']} characters"
                
            return None
            
        def validate_email(self, email):
            # Simple email validation
            return '@' in email and '.' in email.split('@')[1]
            
        def validate_symbol(self, symbol):
            # Trading symbol validation
            return len(symbol) >= 3 and symbol.isalpha()
            
        def sanitize_input(self, input_string):
            # Basic input sanitization
            dangerous_chars = ['<', '>', '&', '"', "'"]
            sanitized = input_string
            
            for char in dangerous_chars:
                sanitized = sanitized.replace(char, '')
                
            return sanitized.strip()


class TestUtilsModules(unittest.TestCase):
    """Test suite for utility module components."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.config = {
            'database': {
                'host': 'localhost',
                'port': 5432,
                'name': 'test_db'
            },
            'network': {
                'timeout': 30,
                'retry_count': 3
            }
        }
        
    def test_logger_setup(self):
        """Test logger setup and configuration."""
        # Test basic logger setup
        test_logger = setup_logger('test_logger')
        self.assertIsNotNone(test_logger)
        self.assertEqual(test_logger.name, 'test_logger')
        
        # Test logger with custom config
        config = LoggerConfig(level='DEBUG', file_path='test.log')
        debug_logger = setup_logger('debug_logger', config)
        self.assertIsNotNone(debug_logger)
        self.assertEqual(debug_logger.level, logging.DEBUG)
        
        logger.info("✅ Logger setup test passed")
        
    def test_configuration_manager(self):
        """Test configuration management functionality."""
        config_manager = ConfigurationManager()
        
        # Test config loading
        config_data = config_manager.load_config()
        self.assertIsNotNone(config_data)
        self.assertIn('database', config_data)
        
        # Test config retrieval
        db_host = config_manager.get_config('database.host')
        self.assertEqual(db_host, 'localhost')
        
        # Test config setting
        success = config_manager.set_config('api.new_setting', 'test_value')
        self.assertTrue(success)
        
        new_value = config_manager.get_config('api.new_setting')
        self.assertEqual(new_value, 'test_value')
        
        # Test config validation
        is_valid, message = config_manager.validate_config()
        self.assertTrue(is_valid)
        
        logger.info("✅ Configuration manager test passed")
        
    def test_database_manager(self):
        """Test database management functionality."""
        db_manager = DatabaseManager(self.config['database'])
        
        # Test connection
        connected = db_manager.connect()
        self.assertTrue(connected)
        self.assertTrue(db_manager.is_connected)
        
        # Test query execution
        select_result = db_manager.execute_query("SELECT * FROM trades")
        self.assertIsInstance(select_result, list)
        self.assertGreater(len(select_result), 0)
        
        insert_result = db_manager.execute_query(
            "INSERT INTO trades (symbol, price) VALUES (?, ?)",
            ('BTCUSDT', 50000.0)
        )
        self.assertIn('inserted_id', insert_result)
        
        # Test table operations
        table_exists = db_manager.table_exists('trades')
        self.assertTrue(table_exists)
        
        table_info = db_manager.get_table_info('trades')
        self.assertIsNotNone(table_info)
        self.assertEqual(table_info['name'], 'trades')
        
        # Test disconnection
        disconnected = db_manager.disconnect()
        self.assertTrue(disconnected)
        self.assertFalse(db_manager.is_connected)
        
        logger.info("✅ Database manager test passed")
        
    def test_file_manager(self):
        """Test file management functionality."""
        file_manager = FileManager()
        
        # Test file reading
        json_content = file_manager.read_file('test.json')
        self.assertIn('test', json_content)
        
        csv_content = file_manager.read_file('data.csv')
        self.assertIn('symbol,price', csv_content)
        
        # Test file existence
        exists = file_manager.file_exists('config.json')
        self.assertTrue(exists)
        
        not_exists = file_manager.file_exists('nonexistent.txt')
        self.assertFalse(not_exists)
        
        # Test file listing
        files = file_manager.list_files('/mock/directory')
        self.assertIsInstance(files, list)
        self.assertGreater(len(files), 0)
        
        # Test file info
        file_info = file_manager.get_file_info('test.txt')
        self.assertIn('size', file_info)
        self.assertIn('created', file_info)
        
        # Test backup
        backup_path = file_manager.backup_file('important.txt')
        self.assertTrue(backup_path.endswith('.backup'))
        
        logger.info("✅ File manager test passed")
        
    def test_network_manager(self):
        """Test network management functionality."""
        network_manager = NetworkManager(self.config['network'])
        
        # Test HTTP request
        response = network_manager.make_request('GET', 'https://api.example.com/data')
        self.assertEqual(response['status_code'], 200)
        self.assertIn('data', response)
        
        # Test error handling
        error_response = network_manager.make_request('GET', 'https://api.example.com/error')
        self.assertEqual(error_response['status_code'], 500)
        
        # Test connectivity check
        is_connected = network_manager.check_connectivity()
        self.assertTrue(is_connected)
        
        # Test URL validation
        valid_url = network_manager.validate_url('https://example.com')
        self.assertTrue(valid_url)
        
        invalid_url = network_manager.validate_url('not-a-url')
        self.assertFalse(invalid_url)
        
        # Test IP info
        ip_info = network_manager.get_ip_info()
        self.assertIn('public_ip', ip_info)
        self.assertIn('local_ip', ip_info)
        
        logger.info("✅ Network manager test passed")
        
    def test_validation_manager(self):
        """Test validation management functionality."""
        validator = ValidationManager()
        
        # Add validation rules
        validator.add_rule('symbol', 'required')
        validator.add_rule('symbol', 'type', expected_type=str)
        validator.add_rule('symbol', 'length', max_length=10)
        validator.add_rule('price', 'required')
        validator.add_rule('price', 'type', expected_type=float)
        validator.add_rule('price', 'range', min=0.0, max=1000000.0)
        
        # Test valid data
        valid_data = {
            'symbol': 'BTCUSDT',
            'price': 50000.0
        }
        is_valid, errors = validator.validate_data(valid_data)
        self.assertTrue(is_valid)
        self.assertEqual(len(errors), 0)
        
        # Test invalid data
        invalid_data = {
            'symbol': 'VERYLONGSYMBOLNAME',  # Too long
            'price': -100.0  # Out of range
        }
        is_valid, errors = validator.validate_data(invalid_data)
        self.assertFalse(is_valid)
        self.assertGreater(len(errors), 0)
        
        # Test email validation
        valid_email = validator.validate_email('<EMAIL>')
        self.assertTrue(valid_email)
        
        invalid_email = validator.validate_email('invalid-email')
        self.assertFalse(invalid_email)
        
        # Test symbol validation
        valid_symbol = validator.validate_symbol('BTCUSDT')
        self.assertTrue(valid_symbol)
        
        invalid_symbol = validator.validate_symbol('BT1')
        self.assertFalse(invalid_symbol)
        
        # Test input sanitization
        dangerous_input = '<script>alert("xss")</script>'
        sanitized = validator.sanitize_input(dangerous_input)
        self.assertNotIn('<script>', sanitized)
        
        logger.info("✅ Validation manager test passed")


class TestUtilsModulesIntegration(unittest.TestCase):
    """Test suite for utility module integration scenarios."""
    
    def setUp(self):
        """Set up test fixtures."""
        self.logger = setup_logger('test_utils_integration')
        
    def test_config_database_integration(self):
        """Test configuration and database integration."""
        # Load config
        config_manager = ConfigurationManager()
        config_data = config_manager.load_config()
        
        # Use config for database
        db_config = config_data['database']
        db_manager = DatabaseManager(db_config)
        
        # Test connection with config
        connected = db_manager.connect()
        self.assertTrue(connected)
        
        # Test query execution
        result = db_manager.execute_query("SELECT 1")
        self.assertIsNotNone(result)
        
        db_manager.disconnect()
        
        self.logger.info("✅ Config-Database integration test passed")
        
    def test_file_validation_integration(self):
        """Test file operations with validation."""
        file_manager = FileManager()
        validator = ValidationManager()
        
        # Read JSON file
        json_content = file_manager.read_file('config.json')
        
        # Parse and validate JSON data
        try:
            data = json.loads(json_content)
            
            # Add validation rules for config
            validator.add_rule('test', 'required')
            validator.add_rule('value', 'type', expected_type=int)
            
            is_valid, errors = validator.validate_data(data)
            self.assertTrue(is_valid)
            
        except json.JSONDecodeError:
            self.fail("Invalid JSON content")
            
        self.logger.info("✅ File-Validation integration test passed")
        
    def test_network_config_integration(self):
        """Test network operations with configuration."""
        config_manager = ConfigurationManager()
        config_data = config_manager.load_config()
        
        # Get API config
        api_config = config_data['api']
        network_manager = NetworkManager({
            'timeout': api_config['timeout'],
            'retry_count': 3
        })
        
        # Make request using config
        response = network_manager.make_request(
            'GET',
            api_config['base_url'] + '/status'
        )
        
        self.assertEqual(response['status_code'], 200)
        
        self.logger.info("✅ Network-Config integration test passed")


if __name__ == '__main__':
    print("\n" + "="*60)
    print("🚀 STARTING UTILS MODULES TEST SUITE")
    print("="*60)
    
    # Create test suite
    suite = unittest.TestSuite()
    
    # Add component tests
    suite.addTest(TestUtilsModules('test_logger_setup'))
    suite.addTest(TestUtilsModules('test_configuration_manager'))
    suite.addTest(TestUtilsModules('test_database_manager'))
    suite.addTest(TestUtilsModules('test_file_manager'))
    suite.addTest(TestUtilsModules('test_network_manager'))
    suite.addTest(TestUtilsModules('test_validation_manager'))
    
    # Add integration tests
    suite.addTest(TestUtilsModulesIntegration('test_config_database_integration'))
    suite.addTest(TestUtilsModulesIntegration('test_file_validation_integration'))
    suite.addTest(TestUtilsModulesIntegration('test_network_config_integration'))
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print results
    if result.wasSuccessful():
        print("\n" + "="*60)
        print("🎉 ALL UTILS MODULE TESTS PASSED!")
        print("="*60)
    else:
        print("\n" + "="*60)
        print("❌ SOME UTILS MODULE TESTS FAILED!")
        print(f"Failures: {len(result.failures)}")
        print(f"Errors: {len(result.errors)}")
        print("="*60)